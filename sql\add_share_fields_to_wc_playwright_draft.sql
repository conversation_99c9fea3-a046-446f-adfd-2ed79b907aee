-- 为 wc_playwright_draft 表添加分享相关字段
-- 执行时间：2025-07-07
-- 说明：为支持文心一言等AI服务的分享功能，添加分享链接和分享图片字段

USE ucube;

-- 检查字段是否已存在，如果不存在则添加
SET @sql = '';
SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ucube' 
  AND TABLE_NAME = 'wc_playwright_draft' 
  AND COLUMN_NAME = 'share_url';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE wc_playwright_draft ADD COLUMN share_url VARCHAR(500) DEFAULT NULL COMMENT ''分享链接'';');
END IF;

SELECT COUNT(*) INTO @col_exists 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ucube' 
  AND TABLE_NAME = 'wc_playwright_draft' 
  AND COLUMN_NAME = 'share_img_url';

IF @col_exists = 0 THEN
    SET @sql = CONCAT(@sql, 'ALTER TABLE wc_playwright_draft ADD COLUMN share_img_url VARCHAR(500) DEFAULT NULL COMMENT ''分享图片链接'';');
END IF;

-- 执行SQL语句
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 验证字段是否添加成功
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'ucube' 
  AND TABLE_NAME = 'wc_playwright_draft' 
  AND COLUMN_NAME IN ('share_url', 'share_img_url');
