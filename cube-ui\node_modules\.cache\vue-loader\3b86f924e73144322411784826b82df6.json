{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\index.vue?vue&type=style&index=0&id=a83bd3b0&lang=scss&scoped=true", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\index.vue", "mtime": 1751901200955}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 1751784287954}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 1751784287582}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 1751784287472}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\sass-loader\\dist\\cjs.js", "mtime": 1751784292220}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKDQouZGFzaGJvYXJkLWVkaXRvci1jb250YWluZXIgew0KICBwYWRkaW5nOiAzMnB4Ow0KICBiYWNrZ3JvdW5kLWNvbG9yOiByZ2IoMjQwLCAyNDIsIDI0NSk7DQogIHBvc2l0aW9uOiByZWxhdGl2ZTsNCg0KICAuY2hhcnQtd3JhcHBlciB7DQogICAgYmFja2dyb3VuZDogI2ZmZjsNCiAgICBwYWRkaW5nOiAxNnB4IDE2cHggMDsNCiAgICBtYXJnaW4tYm90dG9tOiAzMnB4Ow0KICB9DQp9DQoNCkBtZWRpYSAobWF4LXdpZHRoOjEwMjRweCkgew0KICAuY2hhcnQtd3JhcHBlciB7DQogICAgcGFkZGluZzogOHB4Ow0KICB9DQp9DQoNCi8vIOetvuWIsOaXpeWOhuagt+W8jw0KLnNpZ24taW4tY29udGFpbmVyIHsNCiAgbWF4LXdpZHRoOiA2MDBweDsNCiAgbWFyZ2luOiAwIGF1dG87DQogIHBhZGRpbmc6IDIwcHggMTZweDsNCiAgZm9udC1zaXplOiAxM3B4Ow0KICBjb2xvcjogIzMzMzMzMzsNCn0NCg0KLnN0YXRzLWNhcmRzIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBnYXA6IDE2cHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQp9DQoNCi5zdGF0cy1jYXJkIHsNCiAgZmxleDogMTsNCiAgYmFja2dyb3VuZDogI0ZGRkZGRjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KICB0ZXh0LWFsaWduOiBjZW50ZXI7DQogIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsMCwwLDAuMDUpOw0KfQ0KDQouc3RhdHMtbnVtYmVyIHsNCiAgZm9udC1zaXplOiAyMHB4Ow0KICBmb250LXdlaWdodDogYm9sZDsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KICBjb2xvcjogI0ZGNkI2QjsNCn0NCg0KLnN0YXRzLWxhYmVsIHsNCiAgY29sb3I6ICM2NjY2NjY7DQp9DQoNCi5jYWxlbmRhci1zZWN0aW9uIHsNCiAgYmFja2dyb3VuZDogI0ZGRkZGRjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KICBtYXJnaW4tYm90dG9tOiAyMHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLDAsMCwwLjA1KTsNCn0NCg0KLmNhbGVuZGFyLWhlYWRlciB7DQogIG1hcmdpbi1ib3R0b206IDE2cHg7DQp9DQoNCi5tb250aC10aXRsZSB7DQogIGZvbnQtc2l6ZTogMTZweDsNCiAgZm9udC13ZWlnaHQ6IGJvbGQ7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCn0NCg0KLndlZWtkYXlzIHsNCiAgZGlzcGxheTogZ3JpZDsNCiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoNywgMWZyKTsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBjb2xvcjogIzY2NjY2NjsNCiAgbWFyZ2luLWJvdHRvbTogOHB4Ow0KfQ0KDQouY2FsZW5kYXItZ3JpZCB7DQogIGRpc3BsYXk6IGdyaWQ7DQogIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KDcsIDFmcik7DQogIGdhcDogNHB4Ow0KfQ0KDQouY2FsZW5kYXItZGF5IHsNCiAgaGVpZ2h0OiA0MHB4Ow0KICBkaXNwbGF5OiBmbGV4Ow0KICBhbGlnbi1pdGVtczogY2VudGVyOw0KICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjsNCiAgYm9yZGVyLXJhZGl1czogNHB4Ow0KfQ0KDQouY2FsZW5kYXItZGF5LnNpZ25lZCB7DQogIGJhY2tncm91bmQtY29sb3I6ICNGRjZCNkI7DQogIGNvbG9yOiB3aGl0ZTsNCn0NCg0KLmNhbGVuZGFyLWRheS50b2RheSB7DQogIGJvcmRlcjogMnB4IHNvbGlkICNGRjZCNkI7DQp9DQoNCi5jYWxlbmRhci1kYXkuZnV0dXJlIHsNCiAgY29sb3I6ICM5OTk5OTk7DQp9DQoNCi5jYWxlbmRhci1kYXkuZW1wdHkgew0KICBiYWNrZ3JvdW5kOiBub25lOw0KfQ0KDQouc2lnbi1pbi1idXR0b24gew0KICB3aWR0aDogMTAwJTsNCiAgaGVpZ2h0OiA0NHB4Ow0KICBmb250LXNpemU6IDE2cHg7DQogIG1hcmdpbi1ib3R0b206IDIwcHg7DQogIGJhY2tncm91bmQtY29sb3I6ICNGRjZCNkI7DQogIGJvcmRlcjogbm9uZTsNCiAgY29sb3I6IHdoaXRlOw0KICBjdXJzb3I6IHBvaW50ZXI7DQogIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zcyBlYXNlOw0KfQ0KDQouc2lnbi1pbi1idXR0b246ZGlzYWJsZWQgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjQ0NDQ0NDOw0KICBjdXJzb3I6IG5vdC1hbGxvd2VkOw0KfQ0KDQouaGlzdG9yeS1zZWN0aW9uIHsNCiAgYmFja2dyb3VuZDogI0ZGRkZGRjsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBwYWRkaW5nOiAxNnB4Ow0KICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLDAsMCwwLjA1KTsNCn0NCg0KLmhpc3RvcnktdGl0bGUgew0KICBmb250LXNpemU6IDE2cHg7DQogIGZvbnQtd2VpZ2h0OiBib2xkOw0KICBtYXJnaW4tYm90dG9tOiAxNnB4Ow0KfQ0KDQouaGlzdG9yeS1pdGVtIHsNCiAgZGlzcGxheTogZmxleDsNCiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICBwYWRkaW5nOiAxMnB4IDA7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjRUVFRUVFOw0KfQ0KDQouaGlzdG9yeS1pdGVtOmxhc3QtY2hpbGQgew0KICBib3JkZXItYm90dG9tOiBub25lOw0KfQ0KDQouaGlzdG9yeS10aW1lIHsNCiAgY29sb3I6ICM2NjY2NjY7DQp9DQoNCi5wdWxsLXJpZ2h0IC5lbC1idXR0b24tLXRleHQgew0KICBwYWRkaW5nOiAwOw0KICBjb2xvcjogIzQwOUVGRjsNCn0NCg0KLnFyLWNvZGUtY29udGFpbmVyIHsNCiAgcGFkZGluZzogMjBweDsNCiAgdGV4dC1hbGlnbjogY2VudGVyOw0KICBtaW4taGVpZ2h0OiA2MDBweDsNCiAgZGlzcGxheTogZmxleDsNCiAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQp9DQoNCi5xci1jb2RlIHsNCiAgaW1nIHsNCiAgICB3aWR0aDogMTYwMHB4Ow0KICAgIGhlaWdodDogNjAwcHg7DQogICAgbWFyZ2luLWJvdHRvbTogMTVweDsNCiAgfQ0KfQ0KDQoucXItdGlwIHsNCiAgY29sb3I6ICM2NjY7DQogIGZvbnQtc2l6ZTogMTRweDsNCiAgbWFyZ2luLXRvcDogMTBweDsNCn0NCg0KLmxvYWRpbmctdGlwIHsNCiAgY29sb3I6ICM5MDkzOTk7DQogIGZvbnQtc2l6ZTogMTRweDsNCn0NCg0KLmFpLXN0YXR1cy1jYXJkIHsNCiAgLmNhcmQtdGl0bGUgew0KICAgIGRpc3BsYXk6IGZsZXg7DQogICAgYWxpZ24taXRlbXM6IGNlbnRlcjsNCiAgICBmb250LXNpemU6IDE2cHg7DQogICAgZm9udC13ZWlnaHQ6IDYwMDsNCiAgICBjb2xvcjogIzMwMzEzMzsNCg0KICAgIC50aXRsZS1pY29uIHsNCiAgICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICAgICAgZm9udC1zaXplOiAxOHB4Ow0KICAgICAgY29sb3I6ICM0MDlFRkY7DQogICAgfQ0KICB9DQoNCiAgLmVsLWJ1dHRvbi0tdGV4dCB7DQogICAgY29sb3I6ICM0MDlFRkY7DQogICAgZm9udC1zaXplOiAxNHB4Ow0KDQogICAgJjpob3ZlciB7DQogICAgICBjb2xvcjogIzY2YjFmZjsNCiAgICB9DQoNCiAgICBpIHsNCiAgICAgIG1hcmdpbi1yaWdodDogNHB4Ow0KICAgIH0NCiAgfQ0KDQogIC5haS1zdGF0dXMtbGlzdCB7DQogICAgLmFpLXN0YXR1cy1pdGVtIHsNCiAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOw0KICAgICAgcGFkZGluZzogMTJweCAwOw0KICAgICAgYm9yZGVyLWJvdHRvbTogMXB4IHNvbGlkICNFQkVFRjU7DQoNCiAgICAgICY6bGFzdC1jaGlsZCB7DQogICAgICAgIGJvcmRlci1ib3R0b206IG5vbmU7DQogICAgICB9DQoNCiAgICAgIC5haS1wbGF0Zm9ybSB7DQogICAgICAgIGRpc3BsYXk6IGZsZXg7DQogICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgICAgICAgLnBsYXRmb3JtLWljb24gew0KICAgICAgICAgIHdpZHRoOiAzMnB4Ow0KICAgICAgICAgIGhlaWdodDogMzJweDsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgICAgICAgYmFja2dyb3VuZDogI0Y1RjdGQTsNCiAgICAgICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogICAgICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7DQogICAgICAgICAgbWFyZ2luLXJpZ2h0OiAxMnB4Ow0KICAgICAgICAgIG92ZXJmbG93OiBoaWRkZW47DQoNCiAgICAgICAgICBpbWcgew0KICAgICAgICAgICAgd2lkdGg6IDEwMCU7DQogICAgICAgICAgICBoZWlnaHQ6IDEwMCU7DQogICAgICAgICAgICBvYmplY3QtZml0OiBjb3ZlcjsNCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICAucGxhdGZvcm0tbmFtZSB7DQogICAgICAgICAgZm9udC1zaXplOiAxNHB4Ow0KICAgICAgICAgIGNvbG9yOiAjNjA2MjY2Ow0KICAgICAgICAgIGZvbnQtd2VpZ2h0OiA1MDA7DQogICAgICAgICAgZGlzcGxheTogZmxleDsNCiAgICAgICAgICBhbGlnbi1pdGVtczogY2VudGVyOw0KDQogICAgICAgICAgLmxvYWRpbmctaWNvbiB7DQogICAgICAgICAgICBtYXJnaW4tbGVmdDogOHB4Ow0KICAgICAgICAgICAgY29sb3I6ICM0MDlFRkY7DQogICAgICAgICAgICBmb250LXNpemU6IDE2cHg7DQogICAgICAgICAgICBhbmltYXRpb246IHJvdGF0aW5nIDJzIGxpbmVhciBpbmZpbml0ZTsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLnN0YXR1cy1hY3Rpb24gew0KICAgICAgICAuc3RhdHVzLXRhZyB7DQogICAgICAgICAgcGFkZGluZzogMHB4IDEycHg7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogMTZweDsNCg0KICAgICAgICAgIGkgew0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLmxvZ2luLWJ0biB7DQogICAgICAgICAgcGFkZGluZzogNnB4IDEycHg7DQogICAgICAgICAgYm9yZGVyLXJhZGl1czogMTZweDsNCg0KICAgICAgICAgIGkgew0KICAgICAgICAgICAgbWFyZ2luLXJpZ2h0OiA0cHg7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KICB9DQp9DQoNCi5xci1jb2RlLWNvbnRhaW5lciB7DQogIHBhZGRpbmc6IDIwcHg7DQogIHRleHQtYWxpZ246IGNlbnRlcjsNCiAgbWluLWhlaWdodDogNTUwcHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQogIGp1c3RpZnktY29udGVudDogY2VudGVyOw0KICBiYWNrZ3JvdW5kOiAjRjVGN0ZBOw0KICBib3JkZXItcmFkaXVzOiA4cHg7DQp9DQoNCi5xci1jb2RlIHsNCiAgYmFja2dyb3VuZDogI0ZGRkZGRjsNCiAgcGFkZGluZzogMjBweDsNCiAgYm9yZGVyLXJhZGl1czogOHB4Ow0KICBib3gtc2hhZG93OiAwIDJweCAxMnB4IDAgcmdiYSgwLDAsMCwwLjEpOw0KDQogIGltZyB7DQogICAgd2lkdGg6IDEwMDBweDsNCiAgICBoZWlnaHQ6IDU1MHB4Ow0KICAgIG1hcmdpbi1ib3R0b206IDE1cHg7DQogIH0NCn0NCg0KLnFyLXRpcCB7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBmb250LXNpemU6IDE0cHg7DQogIG1hcmdpbi10b3A6IDEwcHg7DQogIGZvbnQtd2VpZ2h0OiA1MDA7DQp9DQoNCi5sb2FkaW5nLXRpcCB7DQogIGNvbG9yOiAjOTA5Mzk5Ow0KICBmb250LXNpemU6IDE0cHg7DQogIGRpc3BsYXk6IGZsZXg7DQogIGFsaWduLWl0ZW1zOiBjZW50ZXI7DQoNCiAgJjo6YmVmb3JlIHsNCiAgICBjb250ZW50OiAnJzsNCiAgICBkaXNwbGF5OiBpbmxpbmUtYmxvY2s7DQogICAgd2lkdGg6IDE2cHg7DQogICAgaGVpZ2h0OiAxNnB4Ow0KICAgIG1hcmdpbi1yaWdodDogOHB4Ow0KICAgIGJvcmRlcjogMnB4IHNvbGlkICNEQ0RGRTY7DQogICAgYm9yZGVyLXRvcC1jb2xvcjogIzQwOUVGRjsNCiAgICBib3JkZXItcmFkaXVzOiA1MCU7DQogICAgYW5pbWF0aW9uOiBsb2FkaW5nIDFzIGxpbmVhciBpbmZpbml0ZTsNCiAgfQ0KfQ0KDQpAa2V5ZnJhbWVzIGxvYWRpbmcgew0KICB0byB7DQogICAgdHJhbnNmb3JtOiByb3RhdGUoMzYwZGVnKTsNCiAgfQ0KfQ0KDQoubWVzc2FnZS1jYXJkIHsNCiAgbWFyZ2luLXRvcDogMjBweDsNCg0KICAubWVzc2FnZS1saXN0IHsNCiAgICBoZWlnaHQ6IDMwMHB4Ow0KICAgIG92ZXJmbG93LXk6IGF1dG87DQogICAgcGFkZGluZzogMTBweDsNCiAgICBiYWNrZ3JvdW5kOiAjZjVmN2ZhOw0KICAgIGJvcmRlci1yYWRpdXM6IDRweDsNCg0KICAgIC5tZXNzYWdlLWl0ZW0gew0KICAgICAgbWFyZ2luLWJvdHRvbTogMTBweDsNCg0KICAgICAgLm1lc3NhZ2UtY29udGVudCB7DQogICAgICAgIG1heC13aWR0aDogODAlOw0KDQogICAgICAgIC5tZXNzYWdlLXRpbWUgew0KICAgICAgICAgIGZvbnQtc2l6ZTogMTJweDsNCiAgICAgICAgICBjb2xvcjogIzkwOTM5OTsNCiAgICAgICAgICBtYXJnaW4tYm90dG9tOiA0cHg7DQogICAgICAgIH0NCg0KICAgICAgICAubWVzc2FnZS10ZXh0IHsNCiAgICAgICAgICBwYWRkaW5nOiA4cHggMTJweDsNCiAgICAgICAgICBib3JkZXItcmFkaXVzOiA0cHg7DQogICAgICAgICAgd29yZC1icmVhazogYnJlYWstYWxsOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLm1lc3NhZ2Utc2VuZCB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LWVuZDsNCg0KICAgICAgLm1lc3NhZ2UtY29udGVudCB7DQogICAgICAgIC5tZXNzYWdlLXRleHQgew0KICAgICAgICAgIGJhY2tncm91bmQ6ICM0MDlFRkY7DQogICAgICAgICAgY29sb3I6IHdoaXRlOw0KICAgICAgICB9DQogICAgICB9DQogICAgfQ0KDQogICAgLm1lc3NhZ2UtcmVjZWl2ZSB7DQogICAgICBkaXNwbGF5OiBmbGV4Ow0KICAgICAganVzdGlmeS1jb250ZW50OiBmbGV4LXN0YXJ0Ow0KDQogICAgICAubWVzc2FnZS1jb250ZW50IHsNCiAgICAgICAgLm1lc3NhZ2UtdGV4dCB7DQogICAgICAgICAgYmFja2dyb3VuZDogd2hpdGU7DQogICAgICAgICAgY29sb3I6ICMzMDMxMzM7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9DQogIH0NCg0KICAubWVzc2FnZS1pbnB1dCB7DQogICAgbWFyZ2luLXRvcDogMTBweDsNCiAgfQ0KfQ0KDQpAa2V5ZnJhbWVzIHJvdGF0aW5nIHsNCiAgZnJvbSB7DQogICAgdHJhbnNmb3JtOiByb3RhdGUoMGRlZyk7DQogIH0NCiAgdG8gew0KICAgIHRyYW5zZm9ybTogcm90YXRlKDM2MGRlZyk7DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+xBA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n\r\n<!--    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />-->\r\n    <div class=\"app-container\">\r\n      <el-row :gutter=\"30\">\r\n        <el-col :span=\"6\" :xs=\"30\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>个人信息</span>\r\n            </div>\r\n            <div>\r\n              <div class=\"text-center\">\r\n                <userAvatar />\r\n              </div>\r\n              <ul class=\"list-group list-group-striped\">\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />用户名称\r\n                  <div class=\"pull-right\" id=\"userName\">{{ user.nickName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"phone\" />手机号码\r\n                  <div class=\"pull-right\">{{ user.phonenumber }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"date\" />创建日期\r\n                  <div class=\"pull-right\">{{ user.createTime }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />\r\n                  <span>积分余额</span>\r\n                  <div  :style=\"{ color: user.points >= 0 ? 'green' : 'red' }\" class=\"pull-right\">{{ user.points }}</div>\r\n                  <el-tooltip content=\"点击可查看积分明细\" placement=\"top\" effect=\"light\">\r\n                    <i class=\"el-icon-chat-dot-round\" @click=\"showPointsDetail\"></i>\r\n                  </el-tooltip>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleBindWechat\">绑定公众号</el-button>\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleAgentBind\">Agent API设置</el-button>\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleSpaceBind\">元器空间绑定</el-button>\r\n\r\n\r\n                  <el-dialog title=\"公众号智能体API配置\" :visible.sync=\"dialogAgentFormVisible\">\r\n                    <el-form :model=\"agentForm\" :rules=\"agentrules\" ref=\"agentForm\" >\r\n                      <el-form-item label=\"智能体ID\" :label-width=\"formLabelWidth\" prop=\"agentId\">\r\n                        <el-input v-model=\"agentForm.agentId\" maxlength=\"32\"  placeholder=\"请输入agentId\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"智能体Token\" :label-width=\"formLabelWidth\" prop=\"agentToken\">\r\n                        <el-input v-model=\"agentForm.agentToken\" maxlength=\"50\"  placeholder=\"请输入agentToken\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogAgentFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmAgentBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n\r\n                  <el-dialog title=\"元器空间绑定\" :visible.sync=\"dialogSpaceFormVisible\">\r\n                    <el-form :model=\"spaceForm\" :rules=\"spacerules\" ref=\"spaceForm\" >\r\n                      <el-form-item label=\"空间ID\" :label-width=\"formLabelWidth\" prop=\"spaceId\">\r\n                        <el-input v-model=\"spaceForm.spaceId\" maxlength=\"32\"  placeholder=\"请输入空间ID\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"空间名称\" :label-width=\"formLabelWidth\" prop=\"spaceName\">\r\n                        <el-input v-model=\"spaceForm.spaceName\" maxlength=\"50\"  placeholder=\"请输入空间名称\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogSpaceFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmSpaceBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n\r\n                  <el-dialog title=\"绑定微信公众号\" :visible.sync=\"dialogFormVisible\">\r\n                    <el-form :model=\"form\" :rules=\"rules\" ref=\"form\" >\r\n                      <el-form-item label=\"appId\" :label-width=\"formLabelWidth\" prop=\"appId\">\r\n                        <el-input v-model=\"form.appId\" maxlength=\"32\"  placeholder=\"请输入appId\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"appSecret\" :label-width=\"formLabelWidth\" prop=\"appSecret\">\r\n                        <el-input v-model=\"form.appSecret\" maxlength=\"50\"  placeholder=\"请输入appSecret\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"公众号名称\" :label-width=\"formLabelWidth\" prop=\"officeAccountName\">\r\n                        <el-input v-model=\"form.officeAccountName\" maxlength=\"50\"  placeholder=\"请输入公众号名称\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"素材封面图\" :label-width=\"formLabelWidth\" prop=\"picUrl\">\r\n                        <image-upload v-model=\"form.picUrl\"/>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"规范说明\" :label-width=\"formLabelWidth\">\r\n                        <div style=\"color: #f56c6c; font-size: 13px;\">\r\n                          请把IP: *************** 添加到公众号IP白名单。步骤：登录微信公众平台→点击设置与开发→安全中心→IP白名单。一般一小时后生效。\r\n                        </div>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :span=\"6\" :xs=\"30\">\r\n          <el-card class=\"box-card ai-status-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span class=\"card-title\">\r\n                <svg-icon icon-class=\"ai\" class=\"title-icon\" />\r\n                AI 登录状态\r\n              </span>\r\n              <el-button\r\n                style=\"float: right; margin-top: -30px;\"\r\n                type=\"text\"\r\n                @click=\"handleRefresh\"\r\n              >\r\n                <i class=\"el-icon-refresh\"></i> 刷新\r\n              </el-button>\r\n            </div>\r\n            <div class=\"ai-status-list\">\r\n              <div class=\"ai-status-item\" v-for=\"(status, type) in aiLoginStatus\" :key=\"type\">\r\n                <div class=\"ai-platform\">\r\n                  <div class=\"platform-icon\">\r\n                    <img :src=\"getPlatformIcon(type)\" :alt=\"getPlatformName(type)\" />\r\n                  </div>\r\n                  <div class=\"platform-name\">\r\n                    {{ getPlatformName(type) }}\r\n                    <el-tooltip v-if=\"isLoading[type]\" content=\"正在登录中...\" placement=\"top\">\r\n                      <i class=\"el-icon-loading loading-icon\"></i>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n                <div class=\"status-action\">\r\n                  <el-tag v-if=\"status\" type=\"success\" effect=\"dark\" class=\"status-tag\">\r\n                    <i class=\"el-icon-success\"></i> <span>{{ accounts[type] }}</span>\r\n                  </el-tag>\r\n                  <el-button v-else type=\"primary\" size=\"small\" :disabled=\"!isClick[type]\" @click=\"handleAiLogin(type)\" class=\"login-btn\">\r\n                    <i class=\"el-icon-connection\"></i> 点击登录\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n\r\n\r\n\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- AI登录二维码对话框 -->\r\n    <el-dialog :title=\"getAiLoginTitle\" :visible.sync=\"aiLoginDialogVisible\" width=\"1200px\" height=\"800px\" center>\r\n      <div class=\"qr-code-container\" v-loading=\"!qrCodeUrl\">\r\n        <div v-if=\"qrCodeUrl\" class=\"qr-code\">\r\n          <img style=\"width: 100%;height: 100%;\" :src=\"qrCodeUrl\" alt=\"登录二维码\" />\r\n          <p class=\"qr-tip\">请使用对应AI平台APP扫码登录</p>\r\n        </div>\r\n        <div v-else class=\"loading-tip\">\r\n          正在获取登录二维码...\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"积分详细\" :visible.sync=\"openPointsRecord\" width=\"1000px\" append-to-body>\r\n        <el-select\r\n          v-model=\"queryPointForm.type\"\r\n          placeholder=\"积分类型\"\r\n          clearable\r\n          style=\"width: 240px;margin-bottom: 10px\"\r\n          @change=\"getUserPointsRecord\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in changeType\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      <el-table  v-loading=\"loading\" :data=\"pointsRecordList\">\r\n        <el-table-column label=\"用户昵称\" align=\"center\" key=\"nick_name\" prop=\"nick_name\"  :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"变更数量\" align=\"center\" key=\"change_amount\" prop=\"change_amount\" :show-overflow-tooltip=\"true\">\r\n          <template slot-scope=\"scope\">\r\n                <span :style=\"{ color: scope.row.change_amount >= 0 ? 'green' : 'red' }\">\r\n                  {{ scope.row.change_amount }}\r\n                </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"积分余额\" align=\"center\" key=\"balance_after\" prop=\"balance_after\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"变更类型\" align=\"center\" key=\"change_type\" prop=\"change_type\"   />\r\n        <el-table-column  width=\"200\" label=\"变更时间\" align=\"center\" prop=\"create_time\" >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.create_time) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作人\" align=\"center\" key=\"create_name\" prop=\"create_name\"   />\r\n        <el-table-column label=\"备注\" align=\"center\" key=\"remark\" prop=\"remark\"   />\r\n\r\n      </el-table>\r\n      <pagination\r\n        v-show=\"pointtotal>0\"\r\n        :total=\"pointtotal\"\r\n        :page.sync=\"queryPointForm.page\"\r\n        :limit.sync=\"queryPointForm.limit\"\r\n        @pagination=\"getUserPointsRecord\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport PanelGroup from './dashboard/PanelGroup'\r\nimport LineChart from './dashboard/LineChart'\r\nimport RaddarChart from './dashboard/RaddarChart'\r\nimport PieChart from './dashboard/PieChart'\r\nimport BarChart from './dashboard/BarChart'\r\nimport userAvatar from \"@/views/system/user/profile/userAvatar\";\r\nimport userInfo from \"@/views/system/user/profile/userInfo\";\r\nimport resetPwd from \"@/views/system/user/profile/resetPwd\";\r\nimport {getUserProfile, bindWcOfficeAccount, getOfficeAccount, getAgentBind, saveAgentBind, saveSpaceBind, getSpaceInfoByUserId} from \"@/api/system/user\";\r\nimport {getUserPointsRecord } from \"@/api/wechat/company\";\r\nimport websocketClient from '@/utils/websocket';\r\n\r\nconst lineChartData = {\r\n  newVisitis: {\r\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\r\n    actualData: [120, 82, 91, 154, 162, 140, 145]\r\n  },\r\n  messages: {\r\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\r\n    actualData: [180, 160, 151, 106, 145, 150, 130]\r\n  },\r\n  purchases: {\r\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\r\n    actualData: [120, 90, 100, 138, 142, 130, 130]\r\n  },\r\n  shoppings: {\r\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\r\n    actualData: [120, 82, 91, 154, 162, 140, 130]\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: 'Index',\r\n  components: {\r\n    PanelGroup,\r\n    LineChart,\r\n    RaddarChart,\r\n    PieChart,\r\n    BarChart,userAvatar, userInfo, resetPwd\r\n  },\r\n  data() {\r\n    return {\r\n      lineChartData: lineChartData.newVisitis,\r\n      user: {},\r\n      roleGroup: {},\r\n      postGroup: {},\r\n      activeTab: \"userinfo\",\r\n      //------ 绑定公众号相关变量 ------//\r\n      dialogFormVisible: false, // 绑定公众号弹窗\r\n      dialogAgentFormVisible: false, // 绑定智能体弹窗\r\n      dialogSpaceFormVisible: false, // 绑定元器空间弹窗\r\n      form: {\r\n        appId: '', // 公众号appId\r\n        appSecret: '', // 公众号appSecret\r\n        officeAccountName: '', // 公众号名称\r\n        picUrl: '', // 公众号封面图\r\n      },\r\n      agentForm: {\r\n        agentId: '', // 智能体ID\r\n        agentToken: '', // 智能体token\r\n      },\r\n      spaceForm: {\r\n        spaceId: '', // 空间ID\r\n        spaceName: '', // 空间名称\r\n      },\r\n      formLabelWidth: '120px', //输入框宽度\r\n      // 绑定公众号表单验证规则\r\n      rules: {\r\n        appId: [\r\n          { required: true, message: '请输入appId', trigger: 'blur' }\r\n        ],\r\n        appSecret: [\r\n          { required: true, message: '请输入appSecret', trigger: 'blur' }\r\n        ],\r\n        officeAccountName: [\r\n          { required: false, message: '请输入公众号名称', trigger: 'blur' }\r\n        ]\r\n      },\r\n      agentrules: {\r\n        agentId: [\r\n          { required: true, message: '请输入agentId', trigger: 'blur' }\r\n        ],\r\n        agentToken: [\r\n          { required: true, message: '请输入agentToken', trigger: 'blur' }\r\n        ]\r\n      },\r\n      spacerules: {\r\n        spaceId: [\r\n          { required: true, message: '请输入空间ID', trigger: 'blur' }\r\n        ],\r\n        spaceName: [\r\n          { required: true, message: '请输入空间名称', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      //------ 积分相关变量 ------//\r\n      loading: true, // 遮罩层\r\n      changeType:[\r\n        {\r\n          label:\"全部\",\r\n          value:\"0\"\r\n        },\r\n        {\r\n        label:\"增加\",\r\n        value:\"1\"\r\n      },\r\n        {\r\n          label:\"消耗\",\r\n          value:\"2\"\r\n        },\r\n      ],  // 积分明细表中的积分类型\r\n      openPointsRecord: false, // 积分明细弹窗\r\n      pointtotal: 0, // 积分明细总数\r\n      queryPointForm:{\r\n        limit:10,\r\n        page:1,\r\n        type:'',\r\n        userId:''\r\n      }, // 积分明细查询需要的查询参数\r\n      pointsRecordList: null, // 积分明细列表\r\n\r\n      //------ 签到相关变量 ------//\r\n      weekDays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],\r\n      currentYear: new Date().getFullYear(),\r\n      currentMonth: new Date().getMonth() + 1,\r\n      continuousSignInDays: 7,\r\n      monthlySignInDays: 15,\r\n      totalSignInDays: 128,\r\n      todaySignedIn: false,\r\n      signInHistory: [\r\n        { date: '2024-01-15', time: '08:30:25' },\r\n        { date: '2024-01-14', time: '09:15:33' },\r\n        { date: '2024-01-13', time: '07:45:12' },\r\n        { date: '2024-01-12', time: '08:20:45' },\r\n        { date: '2024-01-11', time: '09:00:18' }\r\n      ],\r\n      signedDates: [\r\n        new Date(2024, 0, 1),\r\n        new Date(2024, 0, 2),\r\n        new Date(2024, 0, 3),\r\n        new Date(2024, 0, 4),\r\n        new Date(2024, 0, 5)\r\n      ],\r\n      aiLoginStatus: {\r\n        yuanbao: false,\r\n        doubao: false,\r\n        agent: false,\r\n        wenxin: false,\r\n        // qw: false\r\n      },\r\n      accounts: {\r\n        yuanbao: '',\r\n        doubao: '',\r\n        agent: '',\r\n        wenxin: '',\r\n        // qw: ''\r\n      },\r\n      isClick: {\r\n        yuanbao: false,\r\n        doubao: false,\r\n        agent: false,\r\n        wenxin: false,\r\n        // qw: false\r\n      },\r\n      aiLoginDialogVisible: false,\r\n      currentAiType: '',\r\n      qrCodeUrl: '',\r\n      // 消息相关变量\r\n      messages: [],\r\n      messageInput: '',\r\n      isLoading: {\r\n        yuanbao: true,\r\n        doubao: true,\r\n        wenxin: true,\r\n        agent: true,\r\n        // qw: true\r\n      },\r\n    }\r\n  },\r\n  // 计算当前月份的签到日期\r\n  computed: {\r\n    calendarDates() {\r\n      const dates = [];\r\n      const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1);\r\n      const lastDay = new Date(this.currentYear, this.currentMonth, 0);\r\n\r\n      // Fill in empty slots before first day\r\n      for (let i = 0; i < firstDay.getDay(); i++) {\r\n        dates.push(null);\r\n      }\r\n\r\n      // Fill in days of the month\r\n      for (let i = 1; i <= lastDay.getDate(); i++) {\r\n        dates.push(new Date(this.currentYear, this.currentMonth - 1, i));\r\n      }\r\n\r\n      return dates;\r\n    },\r\n    getAiLoginTitle() {\r\n      const titles = {\r\n        yuanbao: '腾讯元宝登录',\r\n        doubao: '豆包登录',\r\n        agent: '智能体登录',\r\n        wenxin: '百度AI登录',\r\n        // qw: '通义千问登录'\r\n      };\r\n      return titles[this.currentAiType] || '登录';\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.getUser();\r\n\r\n  },\r\n  methods: {\r\n    handleSetLineChartData(type) {\r\n      this.lineChartData = lineChartData[type]\r\n    },\r\n    getUser() {\r\n      getUserProfile().then(response => {\r\n        this.user = response.data;\r\n        this.roleGroup = response.roleGroup;\r\n        this.postGroup = response.postGroup;\r\n        this.userId = response.data.userId;\r\n        this.corpId = response.data.corpId;\r\n\r\n        this.initWebSocket(this.userId); // 创建时建立连接\r\n\r\n        setTimeout(() => {\r\n          // 检查元宝登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_YB_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查豆包登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_DB_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查智能体登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_AGENT_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查百度AI登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_WX_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查通义千问登录状态\r\n          // this.sendMessage({\r\n          //   type: 'PLAY_CHECK_QW_LOGIN',\r\n          //   userId: this.userId,\r\n          //   corpId: this.corpId\r\n          // });\r\n        }, 1000);\r\n      });\r\n    },\r\n    // 获取公众号信息\r\n    handleBindWechat() {\r\n      getOfficeAccount().then(response => {\r\n        if (response.data != null) {\r\n          this.form.appId = response.data.appId;\r\n          this.form.appSecret = response.data.appSecret;\r\n          this.form.officeAccountName = response.data.officeAccountName;\r\n          this.form.picUrl = response.data.picUrl;\r\n        }\r\n        this.dialogFormVisible = true;\r\n      });\r\n    },\r\n    handleAgentBind() {\r\n      getAgentBind().then(response => {\r\n        if (response.data != null) {\r\n          this.agentForm.agentId = response.data.agent_id;\r\n          this.agentForm.agentToken = response.data.agent_token;\r\n        }\r\n        this.dialogAgentFormVisible = true;\r\n      });\r\n    },\r\n    handleSpaceBind() {\r\n      getSpaceInfoByUserId().then(response => {\r\n        if (response.data != null) {\r\n          this.spaceForm.spaceId = response.data.spaceId;\r\n          this.spaceForm.spaceName = response.data.spaceName;\r\n        }\r\n        this.dialogSpaceFormVisible = true;\r\n      });\r\n    },\r\n    // 绑定公众号\r\n    confirmBind() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          bindWcOfficeAccount(this.form).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 绑定公众号\r\n    confirmAgentBind() {\r\n      this.$refs.agentForm.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          saveAgentBind(this.agentForm).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogAgentFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    confirmSpaceBind() {\r\n      this.$refs.spaceForm.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          saveSpaceBind(this.spaceForm).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogSpaceFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 获取当前登录用户积分明细\r\n    showPointsDetail() {\r\n      this.queryPointForm.userId = this.user.userId\r\n      this.getUserPointsRecord();\r\n    },\r\n    // 获取积分明细\r\n    getUserPointsRecord(){\r\n      getUserPointsRecord(this.queryPointForm).then(response => {\r\n        this.openPointsRecord = true;\r\n        this.pointsRecordList = response.data.list;\r\n        this.pointtotal = response.data.total\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 获取当前月份的签到日期\r\n    isSignedDate(date) {\r\n      if (!date) return false;\r\n      return this.signedDates.some(signedDate =>\r\n        signedDate.getDate() === date.getDate() &&\r\n        signedDate.getMonth() === date.getMonth() &&\r\n        signedDate.getFullYear() === date.getFullYear()\r\n      );\r\n    },\r\n    isToday(date) {\r\n      if (!date) return false;\r\n      const today = new Date();\r\n      return date.getDate() === today.getDate() &&\r\n             date.getMonth() === today.getMonth() &&\r\n             date.getFullYear() === today.getFullYear();\r\n    },\r\n    isFutureDate(date) {\r\n      if (!date) return false;\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      return date > today;\r\n    },\r\n    handleSignIn() {\r\n      if (!this.todaySignedIn) {\r\n        this.todaySignedIn = true;\r\n        this.signedDates.push(new Date());\r\n        const now = new Date();\r\n        this.signInHistory.unshift({\r\n          date: `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`,\r\n          time: `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`\r\n        });\r\n        this.monthlySignInDays++;\r\n        this.totalSignInDays++;\r\n      }\r\n    },\r\n    handleAiLogin(type) {\r\n      this.currentAiType = type;\r\n      this.aiLoginDialogVisible = true;\r\n      this.isLoading[type] = true;\r\n      this.getQrCode(type);\r\n    },\r\n    getQrCode(type) {\r\n      this.qrCodeUrl = ''\r\n      if(type == 'yuanbao'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_YB_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'doubao'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_DB_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'agent'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_AGENT_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'wenxin'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_WX_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      // if(type == 'qw'){\r\n      //   this.sendMessage({\r\n      //     type: 'PLAY_GET_QW_QRCODE',\r\n      //     userId: this.userId,\r\n      //     corpId: this.corpId\r\n      //   });\r\n      // }\r\n      this.$message({\r\n        message: '正在获取登录二维码...',\r\n        type: 'info'\r\n      });\r\n    },\r\n    getPlatformIcon(type) {\r\n      const icons = {\r\n        yuanbao: require('@/assets/logo/yuanbao.png'),\r\n        doubao: require('@/assets/logo/doubao.png'),\r\n        agent: require('@/assets/logo/yuanbao.png'),\r\n        wenxin: require('@/assets/ai/yuanbao.png'),\r\n        qw: require('@/assets/logo/qw.png')\r\n      };\r\n      return icons[type] || '';\r\n    },\r\n    getPlatformName(type) {\r\n      const names = {\r\n        yuanbao: '腾讯元宝',\r\n        doubao: '豆包',\r\n        agent: '智能体',\r\n        wenxin: '百度AI',\r\n        // qw: '通义千问'\r\n      };\r\n      return names[type] || '';\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log('WebSocket URL:', process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case 'open':\r\n            this.$message.success('正在获取最新登录状态，请稍后...');\r\n            break;\r\n          case 'message':\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case 'close':\r\n            this.$message.warning('WebSocket连接已关闭');\r\n            break;\r\n          case 'error':\r\n            this.$message.error('WebSocket连接错误');\r\n            break;\r\n          case 'reconnect_failed':\r\n            this.$message.error('WebSocket重连失败，请刷新页面重试');\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n      console.log('收到消息:', data);\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      if (datastr.includes(\"RETURN_YB_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.yuanbao = true;\r\n          this.accounts.yuanbao = dataObj.status;\r\n          this.isLoading.yuanbao = false;\r\n        } else {\r\n          this.isClick.yuanbao = true;\r\n          this.isLoading.yuanbao = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_AGENT_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.agent = true;\r\n          this.accounts.agent = dataObj.status;\r\n          this.isLoading.agent = false;\r\n        } else {\r\n          this.isClick.agent = true;\r\n          this.isLoading.agent = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_PC_YB_QRURL\") || datastr.includes(\"RETURN_PC_DB_QRURL\") || datastr.includes(\"RETURN_PC_AGENT_QRURL\") || datastr.includes(\"RETURN_PC_QW_QRURL\")) {\r\n        this.qrCodeUrl = dataObj.url;\r\n      } else if (datastr.includes(\"RETURN_PC_WX_QRURL\")) {\r\n        // 百度AI不需要显示二维码图片，直接显示提示信息\r\n        this.qrCodeUrl = '';\r\n        this.$message({\r\n          message: '请在浏览器中扫码登录百度AI',\r\n          type: 'info',\r\n          duration: 3000\r\n        });\r\n      } else if (datastr.includes(\"RETURN_DB_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.doubao = true;\r\n          this.accounts.doubao = dataObj.status;\r\n          this.isLoading.doubao = false;\r\n        } else {\r\n          this.isClick.doubao = true;\r\n          this.isLoading.doubao = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_WX_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.wenxin = true;\r\n          this.accounts.wenxin = dataObj.status;\r\n          this.isLoading.wenxin = false;\r\n        } else {\r\n          this.isClick.wenxin = true;\r\n          this.isLoading.wenxin = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_QW_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.qw = true;\r\n          this.accounts.qw = dataObj.status;\r\n          this.isLoading.qw = false;\r\n        } else {\r\n          this.isClick.qw = true;\r\n          this.isLoading.qw = false;\r\n        }\r\n      }\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error('WebSocket未连接');\r\n      }\r\n    },\r\n    // 格式化时间\r\n    formatTime(date) {\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n      return `${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    // 滚动到底部\r\n    scrollToBottom() {\r\n      const messageList = this.$refs.messageList;\r\n      if (messageList) {\r\n        messageList.scrollTop = messageList.scrollHeight;\r\n      }\r\n    },\r\n    handleRefresh() {\r\n      window.location.reload();\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    this.closeWebSocket(); // 销毁时关闭连接\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-editor-container {\r\n  padding: 32px;\r\n  background-color: rgb(240, 242, 245);\r\n  position: relative;\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 32px;\r\n  }\r\n}\r\n\r\n@media (max-width:1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n\r\n// 签到日历样式\r\n.sign-in-container {\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  padding: 20px 16px;\r\n  font-size: 13px;\r\n  color: #333333;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stats-card {\r\n  flex: 1;\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  text-align: center;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.stats-number {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  margin-bottom: 8px;\r\n  color: #FF6B6B;\r\n}\r\n\r\n.stats-label {\r\n  color: #666666;\r\n}\r\n\r\n.calendar-section {\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.calendar-header {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.month-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  text-align: center;\r\n}\r\n\r\n.weekdays {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  text-align: center;\r\n  color: #666666;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.calendar-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  gap: 4px;\r\n}\r\n\r\n.calendar-day {\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 4px;\r\n}\r\n\r\n.calendar-day.signed {\r\n  background-color: #FF6B6B;\r\n  color: white;\r\n}\r\n\r\n.calendar-day.today {\r\n  border: 2px solid #FF6B6B;\r\n}\r\n\r\n.calendar-day.future {\r\n  color: #999999;\r\n}\r\n\r\n.calendar-day.empty {\r\n  background: none;\r\n}\r\n\r\n.sign-in-button {\r\n  width: 100%;\r\n  height: 44px;\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  background-color: #FF6B6B;\r\n  border: none;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.sign-in-button:disabled {\r\n  background-color: #CCCCCC;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.history-section {\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.history-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.history-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #EEEEEE;\r\n}\r\n\r\n.history-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-time {\r\n  color: #666666;\r\n}\r\n\r\n.pull-right .el-button--text {\r\n  padding: 0;\r\n  color: #409EFF;\r\n}\r\n\r\n.qr-code-container {\r\n  padding: 20px;\r\n  text-align: center;\r\n  min-height: 600px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.qr-code {\r\n  img {\r\n    width: 1600px;\r\n    height: 600px;\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.qr-tip {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.loading-tip {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.ai-status-card {\r\n  .card-title {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n\r\n    .title-icon {\r\n      margin-right: 8px;\r\n      font-size: 18px;\r\n      color: #409EFF;\r\n    }\r\n  }\r\n\r\n  .el-button--text {\r\n    color: #409EFF;\r\n    font-size: 14px;\r\n\r\n    &:hover {\r\n      color: #66b1ff;\r\n    }\r\n\r\n    i {\r\n      margin-right: 4px;\r\n    }\r\n  }\r\n\r\n  .ai-status-list {\r\n    .ai-status-item {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #EBEEF5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .ai-platform {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .platform-icon {\r\n          width: 32px;\r\n          height: 32px;\r\n          border-radius: 50%;\r\n          background: #F5F7FA;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 12px;\r\n          overflow: hidden;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n          }\r\n        }\r\n\r\n        .platform-name {\r\n          font-size: 14px;\r\n          color: #606266;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          .loading-icon {\r\n            margin-left: 8px;\r\n            color: #409EFF;\r\n            font-size: 16px;\r\n            animation: rotating 2s linear infinite;\r\n          }\r\n        }\r\n      }\r\n\r\n      .status-action {\r\n        .status-tag {\r\n          padding: 0px 12px;\r\n          border-radius: 16px;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n\r\n        .login-btn {\r\n          padding: 6px 12px;\r\n          border-radius: 16px;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.qr-code-container {\r\n  padding: 20px;\r\n  text-align: center;\r\n  min-height: 550px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #F5F7FA;\r\n  border-radius: 8px;\r\n}\r\n\r\n.qr-code {\r\n  background: #FFFFFF;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);\r\n\r\n  img {\r\n    width: 1000px;\r\n    height: 550px;\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.qr-tip {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n  font-weight: 500;\r\n}\r\n\r\n.loading-tip {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  &::before {\r\n    content: '';\r\n    display: inline-block;\r\n    width: 16px;\r\n    height: 16px;\r\n    margin-right: 8px;\r\n    border: 2px solid #DCDFE6;\r\n    border-top-color: #409EFF;\r\n    border-radius: 50%;\r\n    animation: loading 1s linear infinite;\r\n  }\r\n}\r\n\r\n@keyframes loading {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.message-card {\r\n  margin-top: 20px;\r\n\r\n  .message-list {\r\n    height: 300px;\r\n    overflow-y: auto;\r\n    padding: 10px;\r\n    background: #f5f7fa;\r\n    border-radius: 4px;\r\n\r\n    .message-item {\r\n      margin-bottom: 10px;\r\n\r\n      .message-content {\r\n        max-width: 80%;\r\n\r\n        .message-time {\r\n          font-size: 12px;\r\n          color: #909399;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .message-text {\r\n          padding: 8px 12px;\r\n          border-radius: 4px;\r\n          word-break: break-all;\r\n        }\r\n      }\r\n    }\r\n\r\n    .message-send {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n\r\n      .message-content {\r\n        .message-text {\r\n          background: #409EFF;\r\n          color: white;\r\n        }\r\n      }\r\n    }\r\n\r\n    .message-receive {\r\n      display: flex;\r\n      justify-content: flex-start;\r\n\r\n      .message-content {\r\n        .message-text {\r\n          background: white;\r\n          color: #303133;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-input {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n@keyframes rotating {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n</style>\r\n"]}]}