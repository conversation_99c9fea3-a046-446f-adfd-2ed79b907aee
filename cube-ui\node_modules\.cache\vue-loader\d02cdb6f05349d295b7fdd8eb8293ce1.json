{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1751872130636}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751784291203}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge21hcmtlZH0gZnJvbSAnbWFya2VkJzsNCmltcG9ydCB7bWVzc2FnZSwgc2F2ZVVzZXJDaGF0RGF0YSwgZ2V0Q2hhdEhpc3RvcnksIHB1c2hBdXRvT2ZmaWNlfSBmcm9tICJAL2FwaS93ZWNoYXQvYWlnYyI7DQppbXBvcnQgew0KCQl2NCBhcyB1dWlkdjQNCgl9IGZyb20gJ3V1aWQnOw0KaW1wb3J0IHdlYnNvY2tldENsaWVudCBmcm9tICdAL3V0aWxzL3dlYnNvY2tldCc7DQppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7DQppbXBvcnQgVHVybmRvd25TZXJ2aWNlIGZyb20gJ3R1cm5kb3duJzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnQUlNYW5hZ2VtZW50UGxhdGZvcm0nLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB1c2VySWQ6IHN0b3JlLnN0YXRlLnVzZXIuaWQsDQogICAgICBjb3JwSWQ6IHN0b3JlLnN0YXRlLnVzZXIuY29ycF9pZCwNCiAgICAgIGNoYXRJZDogdXVpZHY0KCksDQogICAgICBleHBhbmRlZEhpc3RvcnlJdGVtczoge30sDQogICAgICB1c2VySW5mb1JlcTogew0KICAgICAgICB1c2VyUHJvbXB0OiAnJywNCiAgICAgICAgdXNlcklkOiAnJywNCiAgICAgICAgY29ycElkOiAnJywNCiAgICAgICAgdGFza0lkOiAnJywNCiAgICAgICAgcm9sZXM6ICcnLA0KICAgICAgICB0b25lQ2hhdElkOiAnJywNCiAgICAgICAgeWJEc0NoYXRJZDogJycsDQogICAgICAgIGRiQ2hhdElkOiAnJywNCiAgICAgICAgaXNOZXdDaGF0OiB0cnVlDQogICAgICB9LA0KICAgICAganNvblJwY1JlcWVzdDogew0KICAgICAgICBqc29ucnBjOiAnMi4wJywNCiAgICAgICAgaWQ6IHV1aWR2NCgpLA0KICAgICAgICBtZXRob2Q6ICcnLA0KICAgICAgICBwYXJhbXM6IHt9DQogICAgICB9LA0KICAgICAgYWlMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAnVHVyYm9TQOWFg+WZqCcsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ1R1cmJvU+mVv+aWh+eJiEDlhYPlmagnLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgLy8gew0KICAgICAgICAvLyAgIG5hbWU6ICdNaW5pTWF4QOWFg+WZqCcsDQogICAgICAgIC8vICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgLy8gICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAvLyAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAvLyAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAvLyAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgIC8vICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICAvLyB9LA0KICAgICAgICAvLyB7DQogICAgICAgIC8vICAgbmFtZTogJ+aQnOeLl+aQnOe0okDlhYPlmagnLA0KICAgICAgICAvLyAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgIC8vICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgIC8vICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgLy8gICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgLy8gICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAvLyAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgLy8gfSwNCiAgICAgICAgLy8gew0KICAgICAgICAvLyAgIG5hbWU6ICdLSU1JQOWFg+WZqCcsDQogICAgICAgIC8vICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgLy8gICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAvLyAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAvLyAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAvLyAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgIC8vICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICAvLyB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+iFvuiur+WFg+WunVQxJywNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZycpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogWw0KICAgICAgICAgICAgeyBsYWJlbDogJ+a3seW6puaAneiAgycsIHZhbHVlOiAnZGVlcF90aGlua2luZycgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfogZTnvZHmkJzntKInLCB2YWx1ZTogJ3dlYl9zZWFyY2gnIH0NCiAgICAgICAgICBdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbJ2RlZXBfdGhpbmtpbmcnLCd3ZWJfc2VhcmNoJ10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfohb7orq/lhYPlrp1EUycsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICfmt7HluqbmgJ3ogIMnLCB2YWx1ZTogJ2RlZXBfdGhpbmtpbmcnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn6IGU572R5pCc57SiJywgdmFsdWU6ICd3ZWJfc2VhcmNoJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWydkZWVwX3RoaW5raW5nJywnd2ViX3NlYXJjaCddLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6LGG5YyFJywNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS/osYbljIUucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5rex5bqm5oCd6ICDJywgdmFsdWU6ICdkZWVwX3RoaW5raW5nJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWydkZWVwX3RoaW5raW5nJ10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfmloflv4PkuIDoqIAnLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5paH5b+DNC41IFR1cmJvJywgdmFsdWU6ICd3eC00LjUnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5paH5b+DWDEgVHVyYm8nLCB2YWx1ZTogJ3d4LXgxJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWyd3eC00LjUnXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9DQogICAgICBdLA0KICAgICAgcHJvbXB0SW5wdXQ6ICcnLA0KICAgICAgdGFza1N0YXJ0ZWQ6IGZhbHNlLA0KICAgICAgYXV0b1BsYXk6IGZhbHNlLA0KICAgICAgc2NyZWVuc2hvdHM6IFtdLA0KICAgICAgcmVzdWx0czogW10sDQogICAgICBhY3RpdmVSZXN1bHRUYWI6ICdyZXN1bHQtMCcsDQogICAgICBhY3RpdmVDb2xsYXBzZXM6IFsnYWktc2VsZWN0aW9uJywgJ3Byb21wdC1pbnB1dCddLCAvLyDpu5jorqTlsZXlvIDov5nkuKTkuKrljLrln58NCiAgICAgIHNob3dJbWFnZURpYWxvZzogZmFsc2UsDQogICAgICBjdXJyZW50TGFyZ2VJbWFnZTogJycsDQogICAgICBlbmFibGVkQUlzOiBbXSwNCiAgICAgIHR1cm5kb3duU2VydmljZTogbmV3IFR1cm5kb3duU2VydmljZSh7DQogICAgICAgIGhlYWRpbmdTdHlsZTogJ2F0eCcsDQogICAgICAgIGNvZGVCbG9ja1N0eWxlOiAnZmVuY2VkJywNCiAgICAgICAgZW1EZWxpbWl0ZXI6ICcqJw0KICAgICAgfSksDQogICAgICBzY29yZURpYWxvZ1Zpc2libGU6IGZhbHNlLA0KICAgICAgc2VsZWN0ZWRSZXN1bHRzOiBbXSwNCiAgICAgIHNjb3JlUHJvbXB0OiBg6K+35L2g5rex5bqm6ZiF6K+75Lul5LiL5Yeg56+H5YWs5LyX5Y+356ug77yM5LuO5aSa5Liq57u05bqm6L+b6KGM6YCQ6aG55omT5YiG77yM6L6T5Ye66K+E5YiG57uT5p6c44CC5bm25Zyo5Lul5LiL5ZCE56+H5paH56ug55qE5Z+656GA5LiK5Y2a6YeH5LyX6ZW/77yM57u85ZCI5pW055CG5LiA56+H5pu05YWo6Z2i55qE5paH56ug44CCYCwNCiAgICAgIGhpc3RvcnlEcmF3ZXJWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGNoYXRIaXN0b3J5OiBbXSwNCiAgICAgIHB1c2hPZmZpY2VOdW06IDAsIC8vIOaKlemAkuWIsOWFrOS8l+WPt+eahOmAkuWinue8luWPtw0KICAgICAgcHVzaGluZ1RvV2VjaGF0OiBmYWxzZSwgLy8g5oqV6YCS5Yiw5YWs5LyX5Y+355qEbG9hZGluZ+eKtuaAgQ0KICAgIH07DQogIH0sDQogIGNvbXB1dGVkOiB7DQogICAgY2FuU2VuZCgpIHsNCiAgICAgIHJldHVybiB0aGlzLnByb21wdElucHV0LnRyaW0oKS5sZW5ndGggPiAwICYmIHRoaXMuYWlMaXN0LnNvbWUoYWkgPT4gYWkuZW5hYmxlZCk7DQogICAgfSwNCiAgICBjYW5TY29yZSgpIHsNCiAgICAgIHJldHVybiB0aGlzLnNlbGVjdGVkUmVzdWx0cy5sZW5ndGggPiAwICYmIHRoaXMuc2NvcmVQcm9tcHQudHJpbSgpLmxlbmd0aCA+IDA7DQogICAgfSwNCiAgICBncm91cGVkSGlzdG9yeSgpIHsNCiAgICAgIGNvbnN0IGdyb3VwcyA9IHt9Ow0KICAgICAgY29uc3QgY2hhdEdyb3VwcyA9IHt9Ow0KDQogICAgICAvLyDpppblhYjmjIljaGF0SWTliIbnu4QNCiAgICAgIHRoaXMuY2hhdEhpc3RvcnkuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaWYgKCFjaGF0R3JvdXBzW2l0ZW0uY2hhdElkXSkgew0KICAgICAgICAgIGNoYXRHcm91cHNbaXRlbS5jaGF0SWRdID0gW107DQogICAgICAgIH0NCiAgICAgICAgY2hhdEdyb3Vwc1tpdGVtLmNoYXRJZF0ucHVzaChpdGVtKTsNCiAgICAgIH0pOw0KDQogICAgICAvLyDnhLblkI7mjInml6XmnJ/liIbnu4TvvIzlubblpITnkIbniLblrZDlhbPns7sNCiAgICAgIE9iamVjdC52YWx1ZXMoY2hhdEdyb3VwcykuZm9yRWFjaChjaGF0R3JvdXAgPT4gew0KICAgICAgICAvLyDmjInml7bpl7TmjpLluo8NCiAgICAgICAgY2hhdEdyb3VwLnNvcnQoKGEsIGIpID0+IG5ldyBEYXRlKGEuY3JlYXRlVGltZSkgLSBuZXcgRGF0ZShiLmNyZWF0ZVRpbWUpKTsNCg0KICAgICAgICAvLyDojrflj5bmnIDml6nnmoTorrDlvZXkvZzkuLrniLbnuqcNCiAgICAgICAgY29uc3QgcGFyZW50SXRlbSA9IGNoYXRHcm91cFswXTsNCiAgICAgICAgY29uc3QgZGF0ZSA9IHRoaXMuZ2V0SGlzdG9yeURhdGUocGFyZW50SXRlbS5jcmVhdGVUaW1lKTsNCg0KICAgICAgICBpZiAoIWdyb3Vwc1tkYXRlXSkgew0KICAgICAgICAgIGdyb3Vwc1tkYXRlXSA9IFtdOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5re75Yqg54i257qn6K6w5b2VDQogICAgICAgIGdyb3Vwc1tkYXRlXS5wdXNoKHsNCiAgICAgICAgICAuLi5wYXJlbnRJdGVtLA0KICAgICAgICAgIGlzUGFyZW50OiB0cnVlLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRoaXMuZXhwYW5kZWRIaXN0b3J5SXRlbXNbcGFyZW50SXRlbS5jaGF0SWRdIHx8IGZhbHNlLA0KICAgICAgICAgIGNoaWxkcmVuOiBjaGF0R3JvdXAuc2xpY2UoMSkubWFwKGNoaWxkID0+ICh7DQogICAgICAgICAgICAuLi5jaGlsZCwNCiAgICAgICAgICAgIGlzUGFyZW50OiBmYWxzZQ0KICAgICAgICAgIH0pKQ0KICAgICAgICB9KTsNCiAgICAgIH0pOw0KDQogICAgICByZXR1cm4gZ3JvdXBzOw0KICAgIH0NCiAgfSwNCiAgY3JlYXRlZCgpIHsNCiAgICBjb25zb2xlLmxvZyh0aGlzLnVzZXJJZCk7DQogICAgY29uc29sZS5sb2codGhpcy5jb3JwSWQpOw0KICAgIHRoaXMuaW5pdFdlYlNvY2tldCh0aGlzLnVzZXJJZCk7DQogICAgdGhpcy5sb2FkQ2hhdEhpc3RvcnkoMCk7IC8vIOWKoOi9veWOhuWPsuiusOW9lQ0KICAgIHRoaXMubG9hZExhc3RDaGF0KCk7IC8vIOWKoOi9veS4iuasoeS8muivnQ0KICB9LA0KICBtZXRob2RzOiB7DQogICAgc2VuZFByb21wdCgpIHsNCiAgICAgIGlmICghdGhpcy5jYW5TZW5kKSByZXR1cm47DQoNCiAgICAgIHRoaXMuc2NyZWVuc2hvdHMgPVtdOw0KICAgICAgLy8g5oqY5Y+g5omA5pyJ5Yy65Z+fDQogICAgICB0aGlzLmFjdGl2ZUNvbGxhcHNlcyA9IFtdOw0KDQogICAgICB0aGlzLnRhc2tTdGFydGVkID0gdHJ1ZTsNCiAgICAgIHRoaXMucmVzdWx0cyA9IFtdOyAvLyDmuIXnqbrkuYvliY3nmoTnu5PmnpwNCg0KICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9ICcnOw0KDQoNCiAgICAgIHRoaXMudXNlckluZm9SZXEudGFza0lkID0gdXVpZHY0KCk7DQogICAgICB0aGlzLnVzZXJJbmZvUmVxLnVzZXJJZCA9IHRoaXMudXNlcklkOw0KICAgICAgdGhpcy51c2VySW5mb1JlcS5jb3JwSWQgPSB0aGlzLmNvcnBJZDsNCiAgICAgIHRoaXMudXNlckluZm9SZXEudXNlclByb21wdCA9IHRoaXMucHJvbXB0SW5wdXQ7DQoNCiAgICAgIC8vIOiOt+WPluWQr+eUqOeahEFJ5YiX6KGo5Y+K5YW254q25oCBDQogICAgICB0aGlzLmVuYWJsZWRBSXMgPSB0aGlzLmFpTGlzdC5maWx0ZXIoYWkgPT4gYWkuZW5hYmxlZCk7DQoNCiAgICAgIC8vIOWwhuaJgOacieWQr+eUqOeahEFJ54q25oCB6K6+572u5Li66L+Q6KGM5LitDQogICAgICB0aGlzLmVuYWJsZWRBSXMuZm9yRWFjaChhaSA9PiB7DQogICAgICAgIHRoaXMuJHNldChhaSwgJ3N0YXR1cycsICdydW5uaW5nJyk7DQogICAgICB9KTsNCg0KICAgICAgdGhpcy5lbmFibGVkQUlzLmZvckVhY2goYWkgPT4gew0KICAgICAgICBpZihhaS5uYW1lID09PSAn6IW+6K6v5YWD5a6dVDEnKXsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd5Yi1odW55dWFuLXB0LCc7DQogICAgICAgICAgaWYoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5jbHVkZXMoImRlZXBfdGhpbmtpbmciKSl7DQogICAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd5Yi1odW55dWFuLXNkc2ssJzsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5jbHVkZXMoIndlYl9zZWFyY2giKSl7DQogICAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd5Yi1odW55dWFuLWx3c3MsJzsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYoYWkubmFtZSA9PT0gJ+iFvuiur+WFg+WunURTJyl7DQogICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAneWItZGVlcHNlZWstcHQsJzsNCiAgICAgICAgICBpZihhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmNsdWRlcygiZGVlcF90aGlua2luZyIpKXsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3liLWRlZXBzZWVrLXNkc2ssJzsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5jbHVkZXMoIndlYl9zZWFyY2giKSl7DQogICAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd5Yi1kZWVwc2Vlay1sd3NzLCc7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmKGFpLm5hbWUgPT09ICdUdXJib1NA5YWD5ZmoJyl7DQogICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAnY3ViZS10cnVib3MtYWdlbnQsJzsNCiAgICAgICAgfQ0KICAgICAgICBpZihhaS5uYW1lID09PSAnVHVyYm9T6ZW/5paH54mIQOWFg+WZqCcpew0KICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ2N1YmUtdHVyYm9zLWxhcmdlLWFnZW50LCc7DQogICAgICAgIH0NCiAgICAgICAgaWYoYWkubmFtZSA9PT0gJ01pbmlNYXhA5YWD5ZmoJyl7DQogICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAnY3ViZS1taW5pLW1heC1hZ2VudCwnOw0KICAgICAgICB9DQogICAgICAgIC8vIGlmKGFpLm5hbWUgPT09ICfmkJzni5fmkJzntKJA5YWD5ZmoJyl7DQogICAgICAgIC8vICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAnY3ViZS1zb2dvdS1hZ2VudCwnOw0KICAgICAgICAvLyB9DQogICAgICAgIC8vIGlmKGFpLm5hbWUgPT09ICdLSU1JQOWFg+WZqCcpew0KICAgICAgICAvLyAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ2N1YmUtbHdzcy1hZ2VudCwnOw0KICAgICAgICAvLyB9DQogICAgICAgIGlmKGFpLm5hbWUgPT09ICfosYbljIUnKXsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd6ai1kYiwnOw0KICAgICAgICAgIGlmIChhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmNsdWRlcygiZGVlcF90aGlua2luZyIpKSB7DQogICAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd6ai1kYi1zZHNrLCc7DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmKGFpLm5hbWUgPT09ICfmloflv4PkuIDoqIAnKXsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd3eC0sJzsNCiAgICAgICAgICBpZiAoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5jbHVkZXMoInd4LTQuNSIpKSB7DQogICAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd3eC00LjUsJzsNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzLmluY2x1ZGVzKCJ3eC14MSIpKSB7DQogICAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd3eC14MSwnOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSk7DQoNCiAgICAgIGNvbnNvbGUubG9nKCLlj4LmlbDvvJoiLCB0aGlzLnVzZXJJbmZvUmVxKQ0KDQogICAgICAvL+iwg+eUqOWQjuerr+aOpeWPow0KICAgICAgdGhpcy5qc29uUnBjUmVxZXN0Lm1ldGhvZCA9ICLkvb/nlKhGOFMiDQogICAgICB0aGlzLmpzb25ScGNSZXFlc3QucGFyYW1zID0gdGhpcy51c2VySW5mb1JlcQ0KICAgICAgdGhpcy5tZXNzYWdlKHRoaXMuanNvblJwY1JlcWVzdCkNCiAgICAgIHRoaXMudXNlckluZm9SZXEuaXNOZXdDaGF0ID0gZmFsc2U7DQogICAgfSwNCg0KICAgIG1lc3NhZ2UoZGF0YSkgew0KICAgICAgbWVzc2FnZShkYXRhKS50aGVuKHJlcyA9PiB7DQogICAgICAgIGlmIChyZXMuY29kZSA9PSAyMDEpIHsNCiAgICAgICAgICB1bmkuc2hvd1RvYXN0KHsNCiAgICAgICAgICAgIHRpdGxlOiByZXMubWVzc2FnZXMsDQogICAgICAgICAgICBpY29uOiAnbm9uZScsDQogICAgICAgICAgICBkdXJhdGlvbjogMTUwMCwNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgIH0sDQogICAgdG9nZ2xlQ2FwYWJpbGl0eShhaSwgY2FwYWJpbGl0eVZhbHVlKSB7DQogICAgICBpZiAoIWFpLmVuYWJsZWQpIHJldHVybjsNCg0KICAgICAgY29uc3QgaW5kZXggPSBhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmRleE9mKGNhcGFiaWxpdHlWYWx1ZSk7DQogICAgICBjb25zb2xlLmxvZygn5YiH5o2i5YmNOicsIGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzKTsNCiAgICAgIGlmIChpbmRleCA9PT0gLTEpIHsNCiAgICAgICAgLy8g5aaC5p6c5LiN5a2Y5Zyo77yM5YiZ5re75YqgDQogICAgICAgIHRoaXMuJHNldChhaS5zZWxlY3RlZENhcGFiaWxpdGllcywgYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMubGVuZ3RoLCBjYXBhYmlsaXR5VmFsdWUpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5bey5a2Y5Zyo77yM5YiZ56e76ZmkDQogICAgICAgIGNvbnN0IG5ld0NhcGFiaWxpdGllcyA9IFsuLi5haS5zZWxlY3RlZENhcGFiaWxpdGllc107DQogICAgICAgIG5ld0NhcGFiaWxpdGllcy5zcGxpY2UoaW5kZXgsIDEpOw0KICAgICAgICB0aGlzLiRzZXQoYWksICdzZWxlY3RlZENhcGFiaWxpdGllcycsIG5ld0NhcGFiaWxpdGllcyk7DQogICAgICB9DQogICAgICBjb25zb2xlLmxvZygn5YiH5o2i5ZCOOicsIGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzKTsNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7IC8vIOW8uuWItuabtOaWsOinhuWbvg0KICAgIH0sDQogICAgZ2V0U3RhdHVzVGV4dChzdGF0dXMpIHsNCiAgICAgIHN3aXRjaCAoc3RhdHVzKSB7DQogICAgICAgIGNhc2UgJ2lkbGUnOiByZXR1cm4gJ+etieW+heS4rSc7DQogICAgICAgIGNhc2UgJ3J1bm5pbmcnOiByZXR1cm4gJ+ato+WcqOaJp+ihjCc7DQogICAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6IHJldHVybiAn5bey5a6M5oiQJzsNCiAgICAgICAgY2FzZSAnZmFpbGVkJzogcmV0dXJuICfmiafooYzlpLHotKUnOw0KICAgICAgICBkZWZhdWx0OiByZXR1cm4gJ+acquefpeeKtuaAgSc7DQogICAgICB9DQogICAgfSwNCiAgICBnZXRTdGF0dXNJY29uKHN0YXR1cykgew0KICAgICAgc3dpdGNoIChzdGF0dXMpIHsNCiAgICAgICAgY2FzZSAnaWRsZSc6IHJldHVybiAnZWwtaWNvbi10aW1lJzsNCiAgICAgICAgY2FzZSAncnVubmluZyc6IHJldHVybiAnZWwtaWNvbi1sb2FkaW5nJzsNCiAgICAgICAgY2FzZSAnY29tcGxldGVkJzogcmV0dXJuICdlbC1pY29uLWNoZWNrIHN1Y2Nlc3MtaWNvbic7DQogICAgICAgIGNhc2UgJ2ZhaWxlZCc6IHJldHVybiAnZWwtaWNvbi1jbG9zZSBlcnJvci1pY29uJzsNCiAgICAgICAgZGVmYXVsdDogcmV0dXJuICdlbC1pY29uLXF1ZXN0aW9uJzsNCiAgICAgIH0NCiAgICB9LA0KICAgIHJlbmRlck1hcmtkb3duKHRleHQpIHsNCiAgICAgIHJldHVybiBtYXJrZWQodGV4dCk7DQogICAgfSwNCiAgICAvLyBIVE1M6L2s57qv5paH5pysDQogICAgaHRtbFRvVGV4dChodG1sKSB7DQogICAgICBjb25zdCB0ZW1wRGl2ID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnZGl2Jyk7DQogICAgICB0ZW1wRGl2LmlubmVySFRNTCA9IGh0bWw7DQogICAgICByZXR1cm4gdGVtcERpdi50ZXh0Q29udGVudCB8fCB0ZW1wRGl2LmlubmVyVGV4dCB8fCAnJzsNCiAgICB9LA0KDQogICAgLy8gSFRNTOi9rE1hcmtkb3duDQogICAgaHRtbFRvTWFya2Rvd24oaHRtbCkgew0KICAgICAgcmV0dXJuIHRoaXMudHVybmRvd25TZXJ2aWNlLnR1cm5kb3duKGh0bWwpOw0KICAgIH0sDQoNCiAgICBjb3B5UmVzdWx0KGNvbnRlbnQpIHsNCiAgICAgIC8vIOWwhkhUTUzovazmjaLkuLrnuq/mlofmnKwNCiAgICAgIGNvbnN0IHBsYWluVGV4dCA9IHRoaXMuaHRtbFRvVGV4dChjb250ZW50KTsNCiAgICAgIGNvbnN0IHRleHRhcmVhID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgndGV4dGFyZWEnKTsNCiAgICAgIHRleHRhcmVhLnZhbHVlID0gcGxhaW5UZXh0Ow0KICAgICAgZG9jdW1lbnQuYm9keS5hcHBlbmRDaGlsZCh0ZXh0YXJlYSk7DQogICAgICB0ZXh0YXJlYS5zZWxlY3QoKTsNCiAgICAgIGRvY3VtZW50LmV4ZWNDb21tYW5kKCdjb3B5Jyk7DQogICAgICBkb2N1bWVudC5ib2R5LnJlbW92ZUNoaWxkKHRleHRhcmVhKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5bey5aSN5Yi257qv5paH5pys5Yiw5Ymq6LS05p2/Jyk7DQogICAgfSwNCg0KICAgIGV4cG9ydFJlc3VsdChyZXN1bHQpIHsNCiAgICAgIC8vIOWwhkhUTUzovazmjaLkuLpNYXJrZG93bg0KICAgICAgY29uc3QgbWFya2Rvd24gPSByZXN1bHQuY29udGVudDsNCiAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbbWFya2Rvd25dLCB7IHR5cGU6ICd0ZXh0L21hcmtkb3duJyB9KTsNCiAgICAgIGNvbnN0IGxpbmsgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7DQogICAgICBsaW5rLmhyZWYgPSBVUkwuY3JlYXRlT2JqZWN0VVJMKGJsb2IpOw0KICAgICAgbGluay5kb3dubG9hZCA9IGAke3Jlc3VsdC5haU5hbWV9X+e7k+aenF8ke25ldyBEYXRlKCkudG9JU09TdHJpbmcoKS5zbGljZSgwLCAxMCl9Lm1kYDsNCiAgICAgIGxpbmsuY2xpY2soKTsNCiAgICAgIFVSTC5yZXZva2VPYmplY3RVUkwobGluay5ocmVmKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5bey5a+85Ye6TWFya2Rvd27mlofku7YnKTsNCiAgICB9LA0KDQogICAgb3BlblNoYXJlVXJsKHNoYXJlVXJsKSB7DQogICAgICBpZiAoc2hhcmVVcmwpIHsNCiAgICAgICAgd2luZG93Lm9wZW4oc2hhcmVVcmwsICdfYmxhbmsnKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygn5pqC5peg5Y6f6ZO+5o6lJyk7DQogICAgICB9DQogICAgfSwNCiAgICBzaG93TGFyZ2VJbWFnZShpbWFnZVVybCkgew0KICAgICAgdGhpcy5jdXJyZW50TGFyZ2VJbWFnZSA9IGltYWdlVXJsOw0KICAgICAgdGhpcy5zaG93SW1hZ2VEaWFsb2cgPSB0cnVlOw0KICAgICAgLy8g5om+5Yiw5b2T5YmN5Zu+54mH55qE57Si5byV77yM6K6+572u6L2u5pKt5Zu+55qE5Yid5aeL5L2N572uDQogICAgICBjb25zdCBjdXJyZW50SW5kZXggPSB0aGlzLnNjcmVlbnNob3RzLmluZGV4T2YoaW1hZ2VVcmwpOw0KICAgICAgaWYgKGN1cnJlbnRJbmRleCAhPT0gLTEpIHsNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGNvbnN0IGNhcm91c2VsID0gdGhpcy4kZWwucXVlcnlTZWxlY3RvcignLmltYWdlLWRpYWxvZyAuZWwtY2Fyb3VzZWwnKTsNCiAgICAgICAgICBpZiAoY2Fyb3VzZWwgJiYgY2Fyb3VzZWwuX192dWVfXykgew0KICAgICAgICAgICAgY2Fyb3VzZWwuX192dWVfXy5zZXRBY3RpdmVJdGVtKGN1cnJlbnRJbmRleCk7DQogICAgICAgICAgfQ0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KICAgIGNsb3NlTGFyZ2VJbWFnZSgpIHsNCiAgICAgIHRoaXMuc2hvd0ltYWdlRGlhbG9nID0gZmFsc2U7DQogICAgICB0aGlzLmN1cnJlbnRMYXJnZUltYWdlID0gJyc7DQogICAgfSwNCiAgICAvLyBXZWJTb2NrZXQg55u45YWz5pa55rOVDQogICAgaW5pdFdlYlNvY2tldChpZCkgew0KICAgICAgY29uc3Qgd3NVcmwgPSBwcm9jZXNzLmVudi5WVUVfQVBQX1dTX0FQSSArIGBteXBjLSR7aWR9YDsNCiAgICAgIGNvbnNvbGUubG9nKCdXZWJTb2NrZXQgVVJMOicsIHByb2Nlc3MuZW52LlZVRV9BUFBfV1NfQVBJKTsNCiAgICAgIHdlYnNvY2tldENsaWVudC5jb25uZWN0KHdzVXJsLCAoZXZlbnQpID0+IHsNCiAgICAgICAgc3dpdGNoIChldmVudC50eXBlKSB7DQogICAgICAgICAgY2FzZSAnb3Blbic6DQogICAgICAgICAgICAvLyB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJycpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAnbWVzc2FnZSc6DQogICAgICAgICAgICB0aGlzLmhhbmRsZVdlYlNvY2tldE1lc3NhZ2UoZXZlbnQuZGF0YSk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICdjbG9zZSc6DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ1dlYlNvY2tldOi/nuaOpeW3suWFs+mXrScpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAnZXJyb3InOg0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcignV2ViU29ja2V06L+e5o6l6ZSZ6K+vJyk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICdyZWNvbm5lY3RfZmFpbGVkJzoNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ1dlYlNvY2tldOmHjei/nuWksei0pe+8jOivt+WIt+aWsOmhtemdoumHjeivlScpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQoNCiAgICBoYW5kbGVXZWJTb2NrZXRNZXNzYWdlKGRhdGEpIHsNCg0KICAgICAgY29uc3QgZGF0YXN0ciA9IGRhdGE7DQogICAgICBjb25zdCBkYXRhT2JqID0gSlNPTi5wYXJzZShkYXRhc3RyKTsNCg0KICAgICAgLy8g5aSE55CGY2hhdElk5raI5oGvDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAnUkVUVVJOX1lCVDFfQ0hBVElEJyAmJiBkYXRhT2JqLmNoYXRJZCkgew0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnRvbmVDaGF0SWQgPSBkYXRhT2JqLmNoYXRJZDsNCiAgICAgIH0gZWxzZSBpZiAoZGF0YU9iai50eXBlID09PSAnUkVUVVJOX1lCRFNfQ0hBVElEJyAmJiBkYXRhT2JqLmNoYXRJZCkgew0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnliRHNDaGF0SWQgPSBkYXRhT2JqLmNoYXRJZDsNCiAgICAgIH0gZWxzZSBpZiAoZGF0YU9iai50eXBlID09PSAnUkVUVVJOX0RCX0NIQVRJRCcgJiYgZGF0YU9iai5jaGF0SWQpIHsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS5kYkNoYXRJZCA9IGRhdGFPYmouY2hhdElkOw0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIbov5vluqbml6Xlv5fmtojmga8NCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICdSRVRVUk5fUENfVEFTS19MT0cnICYmIGRhdGFPYmouYWlOYW1lKSB7DQogICAgICAgIGNvbnN0IHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoYWkgPT4gYWkubmFtZSA9PT0gZGF0YU9iai5haU5hbWUpOw0KICAgICAgICBpZiAodGFyZ2V0QUkpIHsNCiAgICAgICAgICAvLyDlsIbmlrDov5vluqbmt7vliqDliLDmlbDnu4TlvIDlpLQNCiAgICAgICAgICB0YXJnZXRBSS5wcm9ncmVzc0xvZ3MudW5zaGlmdCh7DQogICAgICAgICAgICBjb250ZW50OiBkYXRhT2JqLmNvbnRlbnQsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCksDQogICAgICAgICAgICBpc0NvbXBsZXRlZDogZmFsc2UNCiAgICAgICAgICB9KTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOWkhOeQhuaIquWbvua2iOaBrw0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gJ1JFVFVSTl9QQ19UQVNLX0lNRycgJiYgZGF0YU9iai51cmwpIHsNCiAgICAgICAgLy8g5bCG5paw55qE5oiq5Zu+5re75Yqg5Yiw5pWw57uE5byA5aS0DQogICAgICAgIHRoaXMuc2NyZWVuc2hvdHMudW5zaGlmdChkYXRhT2JqLnVybCk7DQogICAgICAgIHJldHVybjsNCiAgICAgIH0NCg0KICAgICAgICAgICAgICAvLyDlpITnkIbmmbrog73or4TliIbnu5PmnpwNCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICdSRVRVUk5fV0tQRl9SRVMnKSB7DQogICAgICAgIGNvbnN0IHdrcGZBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKGFpID0+IGFpLm5hbWUgPT09ICfmmbrog73or4TliIYnKTsNCiAgICAgICAgaWYgKHdrcGZBSSkgew0KICAgICAgICAgIHRoaXMuJHNldCh3a3BmQUksICdzdGF0dXMnLCAnY29tcGxldGVkJyk7DQogICAgICAgICAgaWYgKHdrcGZBSS5wcm9ncmVzc0xvZ3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgdGhpcy4kc2V0KHdrcGZBSS5wcm9ncmVzc0xvZ3NbMF0sICdpc0NvbXBsZXRlZCcsIHRydWUpOw0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyDmt7vliqDor4TliIbnu5PmnpzliLByZXN1bHRz5pyA5YmN6Z2iDQogICAgICAgICAgdGhpcy5yZXN1bHRzLnVuc2hpZnQoew0KICAgICAgICAgICAgYWlOYW1lOiAn5pm66IO96K+E5YiGJywNCiAgICAgICAgICAgIGNvbnRlbnQ6IGRhdGFPYmouZHJhZnRDb250ZW50LA0KICAgICAgICAgICAgc2hhcmVVcmw6IGRhdGFPYmouc2hhcmVVcmwgfHwgJycsDQogICAgICAgICAgICBzaGFyZUltZ1VybDogZGF0YU9iai5zaGFyZUltZ1VybCB8fCAnJywNCiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKQ0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuYWN0aXZlUmVzdWx0VGFiID0gJ3Jlc3VsdC0wJzsNCg0KICAgICAgICAgIC8vIOaZuuiDveivhOWIhuWujOaIkOaXtu+8jOWGjeasoeS/neWtmOWOhuWPsuiusOW9lQ0KICAgICAgICAgIHRoaXMuc2F2ZUhpc3RvcnkoKTsNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgIC8vIOagueaNrua2iOaBr+exu+Wei+abtOaWsOWvueW6lEFJ55qE54q25oCB5ZKM57uT5p6cDQogICAgICBsZXQgdGFyZ2V0QUkgPSBudWxsOw0KICAgICAgc3dpdGNoIChkYXRhT2JqLnR5cGUpIHsNCiAgICAgICAgY2FzZSAnUkVUVVJOX1lCVDFfUkVTJzoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pS25Yiw5raI5oGvOicsIGRhdGEpOw0KICAgICAgICAgIHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoYWkgPT4gYWkubmFtZSA9PT0gJ+iFvuiur+WFg+WunVQxJyk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ1JFVFVSTl9ZQkRTX1JFUyc6DQogICAgICAgICAgY29uc29sZS5sb2coJ+aUtuWIsOa2iOaBrzonLCBkYXRhKTsNCiAgICAgICAgICB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKGFpID0+IGFpLm5hbWUgPT09ICfohb7orq/lhYPlrp1EUycpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdSRVRVUk5fREJfUkVTJzoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pS25Yiw5raI5oGvOicsIGRhdGEpOw0KICAgICAgICAgIHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoYWkgPT4gYWkubmFtZSA9PT0gJ+ixhuWMhScpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdSRVRVUk5fV1hfUkVTJzoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pS25Yiw5raI5oGvOicsIGRhdGEpOw0KICAgICAgICAgIHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoYWkgPT4gYWkubmFtZSA9PT0gJ+aWh+W/g+S4gOiogCcpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdSRVRVUk5fVFVSQk9TX1JFUyc6DQogICAgICAgICAgY29uc29sZS5sb2coJ+aUtuWIsOa2iOaBrzonLCBkYXRhKTsNCiAgICAgICAgICB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKGFpID0+IGFpLm5hbWUgPT09ICdUdXJib1NA5YWD5ZmoJyk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIGNhc2UgJ1JFVFVSTl9UVVJCT1NfTEFSR0VfUkVTJzoNCiAgICAgICAgICBjb25zb2xlLmxvZygn5pS25Yiw5raI5oGvOicsIGRhdGEpOw0KICAgICAgICAgIHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoYWkgPT4gYWkubmFtZSA9PT0gJ1R1cmJvU+mVv+aWh+eJiEDlhYPlmagnKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgLy8gY2FzZSAnUkVUVVJOX01JTklfTUFYX1JFUyc6DQogICAgICAgIC8vICAgdGFyZ2V0QUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZChhaSA9PiBhaS5uYW1lID09PSAnTWluaU1heEDlhYPlmagnKTsNCiAgICAgICAgLy8gICBicmVhazsNCiAgICAgIH0NCg0KICAgICAgaWYgKHRhcmdldEFJKSB7DQogICAgICAgIC8vIOabtOaWsEFJ54q25oCB5Li65bey5a6M5oiQDQogICAgICAgIHRoaXMuJHNldCh0YXJnZXRBSSwgJ3N0YXR1cycsICdjb21wbGV0ZWQnKTsNCg0KICAgICAgICAvLyDlsIbmnIDlkI7kuIDmnaHov5vluqbmtojmga/moIforrDkuLrlt7LlrozmiJANCiAgICAgICAgaWYgKHRhcmdldEFJLnByb2dyZXNzTG9ncy5sZW5ndGggPiAwKSB7DQogICAgICAgICAgdGhpcy4kc2V0KHRhcmdldEFJLnByb2dyZXNzTG9nc1swXSwgJ2lzQ29tcGxldGVkJywgdHJ1ZSk7DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmt7vliqDnu5PmnpzliLDmlbDnu4TlvIDlpLQNCiAgICAgICAgY29uc3QgcmVzdWx0SW5kZXggPSB0aGlzLnJlc3VsdHMuZmluZEluZGV4KHIgPT4gci5haU5hbWUgPT09IHRhcmdldEFJLm5hbWUpOw0KICAgICAgICBpZiAocmVzdWx0SW5kZXggPT09IC0xKSB7DQogICAgICAgICAgdGhpcy5yZXN1bHRzLnVuc2hpZnQoew0KICAgICAgICAgICAgYWlOYW1lOiB0YXJnZXRBSS5uYW1lLA0KICAgICAgICAgICAgY29udGVudDogZGF0YU9iai5kcmFmdENvbnRlbnQsDQogICAgICAgICAgICBzaGFyZVVybDogZGF0YU9iai5zaGFyZVVybCB8fCAnJywNCiAgICAgICAgICAgIHNoYXJlSW1nVXJsOiBkYXRhT2JqLnNoYXJlSW1nVXJsIHx8ICcnLA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpDQogICAgICAgICAgfSk7DQogICAgICAgICAgdGhpcy5hY3RpdmVSZXN1bHRUYWIgPSAncmVzdWx0LTAnOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMucmVzdWx0cy5zcGxpY2UocmVzdWx0SW5kZXgsIDEpOw0KICAgICAgICAgIHRoaXMucmVzdWx0cy51bnNoaWZ0KHsNCiAgICAgICAgICAgIGFpTmFtZTogdGFyZ2V0QUkubmFtZSwNCiAgICAgICAgICAgIGNvbnRlbnQ6IGRhdGFPYmouZHJhZnRDb250ZW50LA0KICAgICAgICAgICAgc2hhcmVVcmw6IGRhdGFPYmouc2hhcmVVcmwgfHwgJycsDQogICAgICAgICAgICBzaGFyZUltZ1VybDogZGF0YU9iai5zaGFyZUltZ1VybCB8fCAnJywNCiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKQ0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuYWN0aXZlUmVzdWx0VGFiID0gJ3Jlc3VsdC0wJzsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnNhdmVIaXN0b3J5KCk7DQogICAgICB9DQoNCiAgICAgIC8vIOajgOafpeaYr+WQpuaJgOacieS7u+WKoemDveW3suWujOaIkA0KICAgICAgLy8gY29uc3QgYWxsQ29tcGxldGVkID0gdGhpcy5lbmFibGVkQUlzLmV2ZXJ5KGFpID0+DQogICAgICAvLyAgIGFpLnN0YXR1cyA9PT0gJ2NvbXBsZXRlZCcgfHwgYWkuc3RhdHVzID09PSAnZmFpbGVkJw0KICAgICAgLy8gKTsNCg0KICAgICAgLy8gaWYgKGFsbENvbXBsZXRlZCkgew0KICAgICAgLy8NCiAgICAgIC8vIH0NCiAgICB9LA0KDQogICAgY2xvc2VXZWJTb2NrZXQoKSB7DQogICAgICB3ZWJzb2NrZXRDbGllbnQuY2xvc2UoKTsNCiAgICB9LA0KDQogICAgc2VuZE1lc3NhZ2UoZGF0YSkgew0KICAgICAgaWYgKHdlYnNvY2tldENsaWVudC5zZW5kKGRhdGEpKSB7DQogICAgICAgIC8vIOa7muWKqOWIsOW6lemDqA0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5zY3JvbGxUb0JvdHRvbSgpOw0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ1dlYlNvY2tldOacqui/nuaOpScpOw0KICAgICAgfQ0KICAgIH0sDQogICAgdG9nZ2xlQUlFeHBhbnNpb24oYWkpIHsNCiAgICAgIHRoaXMuJHNldChhaSwgJ2lzRXhwYW5kZWQnLCAhYWkuaXNFeHBhbmRlZCk7DQogICAgfSwNCg0KICAgIGZvcm1hdFRpbWUodGltZXN0YW1wKSB7DQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodGltZXN0YW1wKTsNCiAgICAgIHJldHVybiBkYXRlLnRvTG9jYWxlVGltZVN0cmluZygnemgtQ04nLCB7DQogICAgICAgIGhvdXI6ICcyLWRpZ2l0JywNCiAgICAgICAgbWludXRlOiAnMi1kaWdpdCcsDQogICAgICAgIHNlY29uZDogJzItZGlnaXQnLA0KICAgICAgICBob3VyMTI6IGZhbHNlDQogICAgICB9KTsNCiAgICB9LA0KICAgIHNob3dTY29yZURpYWxvZygpIHsNCiAgICAgIHRoaXMuc2NvcmVEaWFsb2dWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMuc2VsZWN0ZWRSZXN1bHRzID0gW107DQogICAgfSwNCg0KICAgIGhhbmRsZVNjb3JlKCkgew0KICAgICAgaWYgKCF0aGlzLmNhblNjb3JlKSByZXR1cm47DQoNCiAgICAgIC8vIOiOt+WPlumAieS4reeahOe7k+aenOWGheWuueW5tuaMieeFp+aMh+WumuagvOW8j+aLvOaOpQ0KICAgICAgY29uc3Qgc2VsZWN0ZWRDb250ZW50cyA9IHRoaXMucmVzdWx0cw0KICAgICAgICAuZmlsdGVyKHJlc3VsdCA9PiB0aGlzLnNlbGVjdGVkUmVzdWx0cy5pbmNsdWRlcyhyZXN1bHQuYWlOYW1lKSkNCiAgICAgICAgLm1hcChyZXN1bHQgPT4gew0KICAgICAgICAgIC8vIOWwhkhUTUzlhoXlrrnovazmjaLkuLrnuq/mlofmnKwNCiAgICAgICAgICBjb25zdCBwbGFpbkNvbnRlbnQgPSB0aGlzLmh0bWxUb1RleHQocmVzdWx0LmNvbnRlbnQpOw0KICAgICAgICAgIHJldHVybiBgJHtyZXN1bHQuYWlOYW1lfeWIneeov++8mlxuJHtwbGFpbkNvbnRlbnR9XG5gOw0KICAgICAgICB9KQ0KICAgICAgICAuam9pbignXG4nKTsNCg0KICAgICAgLy8g5p6E5bu65a6M5pW055qE6K+E5YiG5o+Q56S65YaF5a65DQogICAgICBjb25zdCBmdWxsUHJvbXB0ID0gYCR7dGhpcy5zY29yZVByb21wdH1cbiR7c2VsZWN0ZWRDb250ZW50c31gOw0KDQogICAgICAvLyDmnoTlu7ror4TliIbor7fmsYINCiAgICAgIGNvbnN0IHNjb3JlUmVxdWVzdCA9IHsNCiAgICAgICAganNvbnJwYzogJzIuMCcsDQogICAgICAgIGlkOiB1dWlkdjQoKSwNCiAgICAgICAgbWV0aG9kOiAnQUnor4TliIYnLA0KICAgICAgICBwYXJhbXM6IHsNCiAgICAgICAgICB0YXNrSWQ6IHV1aWR2NCgpLA0KICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZCwNCiAgICAgICAgICB1c2VyUHJvbXB0OiBmdWxsUHJvbXB0LA0KICAgICAgICAgIHJvbGVzOiAnemotZGItc2RzaycgLy8g6buY6K6k5L2/55So6LGG5YyF6L+b6KGM6K+E5YiGDQogICAgICAgIH0NCiAgICAgIH07DQoNCiAgICAgIC8vIOWPkemAgeivhOWIhuivt+axgg0KICAgICAgY29uc29sZS5sb2coIuWPguaVsCIsIHNjb3JlUmVxdWVzdCkNCiAgICAgIHRoaXMubWVzc2FnZShzY29yZVJlcXVlc3QpOw0KICAgICAgdGhpcy5zY29yZURpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCg0KICAgICAgLy8g5Yib5bu65pm66IO96K+E5YiGQUnoioLngrkNCiAgICAgIGNvbnN0IHdrcGZBSSA9IHsNCiAgICAgICAgbmFtZTogJ+aZuuiDveivhOWIhicsDQogICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgIGNhcGFiaWxpdGllczogW10sDQogICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgc3RhdHVzOiAncnVubmluZycsDQogICAgICAgIHByb2dyZXNzTG9nczogWw0KICAgICAgICAgIHsNCiAgICAgICAgICAgIGNvbnRlbnQ6ICfmmbrog73or4TliIbku7vliqHlt7Lmj5DkuqTvvIzmraPlnKjor4TliIYuLi4nLA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgaXNDb21wbGV0ZWQ6IGZhbHNlLA0KICAgICAgICAgICAgdHlwZTogJ+aZuuiDveivhOWIhicNCiAgICAgICAgICB9DQogICAgICAgIF0sDQogICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgIH07DQoNCiAgICAgIC8vIOajgOafpeaYr+WQpuW3suWtmOWcqOaZuuiDveivhOWIhg0KICAgICAgY29uc3QgZXhpc3RJbmRleCA9IHRoaXMuZW5hYmxlZEFJcy5maW5kSW5kZXgoYWkgPT4gYWkubmFtZSA9PT0gJ+aZuuiDveivhOWIhicpOw0KICAgICAgaWYgKGV4aXN0SW5kZXggPT09IC0xKSB7DQogICAgICAgIC8vIOWmguaenOS4jeWtmOWcqO+8jOa3u+WKoOWIsOaVsOe7hOW8gOWktA0KICAgICAgICB0aGlzLmVuYWJsZWRBSXMudW5zaGlmdCh3a3BmQUkpOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLy8g5aaC5p6c5bey5a2Y5Zyo77yM5pu05paw54q25oCB5ZKM5pel5b+XDQogICAgICAgIHRoaXMuZW5hYmxlZEFJc1tleGlzdEluZGV4XSA9IHdrcGZBSTsNCiAgICAgICAgLy8g5bCG5pm66IO96K+E5YiG56e75Yiw5pWw57uE5byA5aS0DQogICAgICAgIGNvbnN0IHdrcGYgPSB0aGlzLmVuYWJsZWRBSXMuc3BsaWNlKGV4aXN0SW5kZXgsIDEpWzBdOw0KICAgICAgICB0aGlzLmVuYWJsZWRBSXMudW5zaGlmdCh3a3BmKTsNCiAgICAgIH0NCg0KICAgICAgdGhpcy4kZm9yY2VVcGRhdGUoKTsNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn6K+E5YiG6K+35rGC5bey5Y+R6YCB77yM6K+3562J5b6F57uT5p6cJyk7DQogICAgfSwNCiAgICAvLyDmmL7npLrljoblj7LorrDlvZXmir3lsYkNCiAgICBzaG93SGlzdG9yeURyYXdlcigpIHsNCiAgICAgIHRoaXMuaGlzdG9yeURyYXdlclZpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5sb2FkQ2hhdEhpc3RvcnkoMSk7DQogICAgfSwNCg0KICAgIC8vIOWFs+mXreWOhuWPsuiusOW9leaKveWxiQ0KICAgIGhhbmRsZUhpc3RvcnlEcmF3ZXJDbG9zZSgpIHsNCiAgICAgIHRoaXMuaGlzdG9yeURyYXdlclZpc2libGUgPSBmYWxzZTsNCiAgICB9LA0KDQogICAgLy8g5Yqg6L295Y6G5Y+y6K6w5b2VDQogICAgYXN5bmMgbG9hZENoYXRIaXN0b3J5KGlzQWxsKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBnZXRDaGF0SGlzdG9yeSh0aGlzLnVzZXJJZCwgaXNBbGwpOw0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuY2hhdEhpc3RvcnkgPSByZXMuZGF0YSB8fCBbXTsNCiAgICAgICAgfQ0KICAgICAgfSBjYXRjaCAoZXJyb3IpIHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5Yqg6L295Y6G5Y+y6K6w5b2V5aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5Yqg6L295Y6G5Y+y6K6w5b2V5aSx6LSlJyk7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOagvOW8j+WMluWOhuWPsuiusOW9leaXtumXtA0KICAgIGZvcm1hdEhpc3RvcnlUaW1lKHRpbWVzdGFtcCkgew0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVzdGFtcCk7DQogICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoJ3poLUNOJywgew0KICAgICAgICBob3VyOiAnMi1kaWdpdCcsDQogICAgICAgIG1pbnV0ZTogJzItZGlnaXQnLA0KICAgICAgICBob3VyMTI6IGZhbHNlDQogICAgICB9KTsNCiAgICB9LA0KDQogICAgLy8g6I635Y+W5Y6G5Y+y6K6w5b2V5pel5pyf5YiG57uEDQogICAgZ2V0SGlzdG9yeURhdGUodGltZXN0YW1wKSB7DQogICAgICBjb25zdCBkYXRlID0gbmV3IERhdGUodGltZXN0YW1wKTsNCiAgICAgIGNvbnN0IHRvZGF5ID0gbmV3IERhdGUoKTsNCiAgICAgIGNvbnN0IHllc3RlcmRheSA9IG5ldyBEYXRlKHRvZGF5KTsNCiAgICAgIHllc3RlcmRheS5zZXREYXRlKHllc3RlcmRheS5nZXREYXRlKCkgLSAxKTsNCiAgICAgIGNvbnN0IHR3b0RheXNBZ28gPSBuZXcgRGF0ZSh0b2RheSk7DQogICAgICB0d29EYXlzQWdvLnNldERhdGUodHdvRGF5c0Fnby5nZXREYXRlKCkgLSAyKTsNCiAgICAgIGNvbnN0IHRocmVlRGF5c0FnbyA9IG5ldyBEYXRlKHRvZGF5KTsNCiAgICAgIHRocmVlRGF5c0Fnby5zZXREYXRlKHRocmVlRGF5c0Fnby5nZXREYXRlKCkgLSAzKTsNCg0KICAgICAgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHRvZGF5LnRvRGF0ZVN0cmluZygpKSB7DQogICAgICAgIHJldHVybiAn5LuK5aSpJzsNCiAgICAgIH0gZWxzZSBpZiAoZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0geWVzdGVyZGF5LnRvRGF0ZVN0cmluZygpKSB7DQogICAgICAgIHJldHVybiAn5pio5aSpJzsNCiAgICAgIH0gZWxzZSBpZiAoZGF0ZS50b0RhdGVTdHJpbmcoKSA9PT0gdHdvRGF5c0Fnby50b0RhdGVTdHJpbmcoKSkgew0KICAgICAgICByZXR1cm4gJ+S4pOWkqeWJjSc7DQogICAgICB9IGVsc2UgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHRocmVlRGF5c0Fnby50b0RhdGVTdHJpbmcoKSkgew0KICAgICAgICByZXR1cm4gJ+S4ieWkqeWJjSc7DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZURhdGVTdHJpbmcoJ3poLUNOJywgew0KICAgICAgICAgIHllYXI6ICdudW1lcmljJywNCiAgICAgICAgICBtb250aDogJ2xvbmcnLA0KICAgICAgICAgIGRheTogJ251bWVyaWMnDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliqDovb3ljoblj7LorrDlvZXpobkNCiAgICBsb2FkSGlzdG9yeUl0ZW0oaXRlbSkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgaGlzdG9yeURhdGEgPSBKU09OLnBhcnNlKGl0ZW0uZGF0YSk7DQogICAgICAgIC8vIOaBouWkjUFJ6YCJ5oup6YWN572uDQogICAgICAgIHRoaXMuYWlMaXN0ID0gaGlzdG9yeURhdGEuYWlMaXN0IHx8IHRoaXMuYWlMaXN0Ow0KICAgICAgICAvLyDmgaLlpI3mj5DnpLror43ovpPlhaUNCiAgICAgICAgdGhpcy5wcm9tcHRJbnB1dCA9IGhpc3RvcnlEYXRhLnByb21wdElucHV0IHx8ICcnOw0KICAgICAgICAvLyDmgaLlpI3ku7vliqHmtYHnqIsNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzID0gaGlzdG9yeURhdGEuZW5hYmxlZEFJcyB8fCBbXTsNCiAgICAgICAgLy8g5oGi5aSN5Li75py65Y+v6KeG5YyWDQogICAgICAgIHRoaXMuc2NyZWVuc2hvdHMgPSBoaXN0b3J5RGF0YS5zY3JlZW5zaG90cyB8fCBbXTsNCiAgICAgICAgLy8g5oGi5aSN5omn6KGM57uT5p6cDQogICAgICAgIHRoaXMucmVzdWx0cyA9IGhpc3RvcnlEYXRhLnJlc3VsdHMgfHwgW107DQogICAgICAgIC8vIOaBouWkjWNoYXRJZA0KICAgICAgICB0aGlzLmNoYXRJZCA9IGl0ZW0uY2hhdElkIHx8IHRoaXMuY2hhdElkOw0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnRvbmVDaGF0SWQgPSBpdGVtLnRvbmVDaGF0SWQgfHwgJyc7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEueWJEc0NoYXRJZCA9IGl0ZW0ueWJEc0NoYXRJZCB8fCAnJzsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS5kYkNoYXRJZCA9IGl0ZW0uZGJDaGF0SWQgfHwgJyc7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEuaXNOZXdDaGF0ID0gZmFsc2U7DQoNCiAgICAgICAgLy8g5bGV5byA55u45YWz5Yy65Z+fDQogICAgICAgIHRoaXMuYWN0aXZlQ29sbGFwc2VzID0gWydhaS1zZWxlY3Rpb24nLCAncHJvbXB0LWlucHV0J107DQogICAgICAgIHRoaXMudGFza1N0YXJ0ZWQgPSB0cnVlOw0KDQogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5Y6G5Y+y6K6w5b2V5Yqg6L295oiQ5YqfJyk7DQogICAgICAgIHRoaXMuaGlzdG9yeURyYXdlclZpc2libGUgPSBmYWxzZTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veWOhuWPsuiusOW9leWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veWOhuWPsuiusOW9leWksei0pScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDkv53lrZjljoblj7LorrDlvZUNCiAgICBhc3luYyBzYXZlSGlzdG9yeSgpIHsNCiAgICAgIC8vIGlmICghdGhpcy50YXNrU3RhcnRlZCB8fCB0aGlzLmVuYWJsZWRBSXMuc29tZShhaSA9PiBhaS5zdGF0dXMgPT09ICdydW5uaW5nJykpIHsNCiAgICAgIC8vICAgcmV0dXJuOw0KICAgICAgLy8gfQ0KDQogICAgICBjb25zdCBoaXN0b3J5RGF0YSA9IHsNCiAgICAgICAgYWlMaXN0OiB0aGlzLmFpTGlzdCwNCiAgICAgICAgcHJvbXB0SW5wdXQ6IHRoaXMucHJvbXB0SW5wdXQsDQogICAgICAgIGVuYWJsZWRBSXM6IHRoaXMuZW5hYmxlZEFJcywNCiAgICAgICAgc2NyZWVuc2hvdHM6IHRoaXMuc2NyZWVuc2hvdHMsDQogICAgICAgIHJlc3VsdHM6IHRoaXMucmVzdWx0cywNCiAgICAgICAgY2hhdElkOiB0aGlzLmNoYXRJZCwNCiAgICAgICAgdG9uZUNoYXRJZDogdGhpcy51c2VySW5mb1JlcS50b25lQ2hhdElkLA0KICAgICAgICB5YkRzQ2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLnliRHNDaGF0SWQsDQogICAgICAgIGRiQ2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLmRiQ2hhdElkDQogICAgICB9Ow0KDQogICAgICB0cnkgew0KICAgICAgICBhd2FpdCBzYXZlVXNlckNoYXREYXRhKHsNCiAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgIHVzZXJQcm9tcHQ6IHRoaXMucHJvbXB0SW5wdXQsDQogICAgICAgICAgZGF0YTogSlNPTi5zdHJpbmdpZnkoaGlzdG9yeURhdGEpLA0KICAgICAgICAgIGNoYXRJZDogdGhpcy5jaGF0SWQsDQogICAgICAgICAgdG9uZUNoYXRJZDogdGhpcy51c2VySW5mb1JlcS50b25lQ2hhdElkLA0KICAgICAgICAgIHliRHNDaGF0SWQ6IHRoaXMudXNlckluZm9SZXEueWJEc0NoYXRJZCwNCiAgICAgICAgICBkYkNoYXRJZDogdGhpcy51c2VySW5mb1JlcS5kYkNoYXRJZA0KICAgICAgICB9KTsNCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+S/neWtmOWOhuWPsuiusOW9leWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+S/neWtmOWOhuWPsuiusOW9leWksei0pScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDkv67mlLnmipjlj6DliIfmjaLmlrnms5UNCiAgICB0b2dnbGVIaXN0b3J5RXhwYW5zaW9uKGl0ZW0pIHsNCiAgICAgIHRoaXMuJHNldCh0aGlzLmV4cGFuZGVkSGlzdG9yeUl0ZW1zLCBpdGVtLmNoYXRJZCwgIXRoaXMuZXhwYW5kZWRIaXN0b3J5SXRlbXNbaXRlbS5jaGF0SWRdKTsNCiAgICB9LA0KDQogICAgLy8g5Yib5bu65paw5a+56K+dDQogICAgY3JlYXRlTmV3Q2hhdCgpIHsNCiAgICAgIC8vIOmHjee9ruaJgOacieaVsOaNrg0KICAgICAgdGhpcy5jaGF0SWQgPSB1dWlkdjQoKTsNCiAgICAgIHRoaXMuaXNOZXdDaGF0ID0gdHJ1ZTsNCiAgICAgIHRoaXMucHJvbXB0SW5wdXQgPSAnJzsNCiAgICAgIHRoaXMudGFza1N0YXJ0ZWQgPSBmYWxzZTsNCiAgICAgIHRoaXMuc2NyZWVuc2hvdHMgPSBbXTsNCiAgICAgIHRoaXMucmVzdWx0cyA9IFtdOw0KICAgICAgdGhpcy5lbmFibGVkQUlzID0gW107DQogICAgICB0aGlzLnVzZXJJbmZvUmVxID0gew0KICAgICAgICB1c2VyUHJvbXB0OiAnJywNCiAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZCwNCiAgICAgICAgdGFza0lkOiAnJywNCiAgICAgICAgcm9sZXM6ICcnLA0KICAgICAgICB0b25lQ2hhdElkOiAnJywNCiAgICAgICAgeWJEc0NoYXRJZDogJycsDQogICAgICAgIGRiQ2hhdElkOiAnJywNCiAgICAgICAgaXNOZXdDaGF0OiB0cnVlDQogICAgICB9Ow0KICAgICAgLy8g6YeN572uQUnliJfooajkuLrliJ3lp4vnirbmgIENCiAgICAgIHRoaXMuYWlMaXN0ID0gWw0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ1R1cmJvU0DlhYPlmagnLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICdUdXJib1Pplb/mlofniYhA5YWD5ZmoJywNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZycpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogW10sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIC8vIHsNCiAgICAgICAgLy8gICBuYW1lOiAnTWluaU1heEDlhYPlmagnLA0KICAgICAgICAvLyAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgIC8vICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgIC8vICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgLy8gICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgLy8gICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAvLyAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgLy8gfSwNCiAgICAgICAgLy8gew0KICAgICAgICAvLyAgIG5hbWU6ICdLSU1JQOWFg+WZqCcsDQogICAgICAgIC8vICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgLy8gICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAvLyAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAvLyAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAvLyAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgIC8vICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICAvLyB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+iFvuiur+WFg+WunVQxJywNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZycpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogWw0KICAgICAgICAgICAgeyBsYWJlbDogJ+a3seW6puaAneiAgycsIHZhbHVlOiAnZGVlcF90aGlua2luZycgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfogZTnvZHmkJzntKInLCB2YWx1ZTogJ3dlYl9zZWFyY2gnIH0NCiAgICAgICAgICBdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbJ2RlZXBfdGhpbmtpbmcnLCd3ZWJfc2VhcmNoJ10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfohb7orq/lhYPlrp1EUycsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICfmt7HluqbmgJ3ogIMnLCB2YWx1ZTogJ2RlZXBfdGhpbmtpbmcnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn6IGU572R5pCc57SiJywgdmFsdWU6ICd3ZWJfc2VhcmNoJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWydkZWVwX3RoaW5raW5nJywnd2ViX3NlYXJjaCddLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6LGG5YyFJywNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS/osYbljIUucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5rex5bqm5oCd6ICDJywgdmFsdWU6ICdkZWVwX3RoaW5raW5nJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWydkZWVwX3RoaW5raW5nJ10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfmloflv4PkuIDoqIAnLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5paH5b+DNC41IFR1cmJvJywgdmFsdWU6ICd3eC00LjUnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn5paH5b+DWDEgVHVyYm8nLCB2YWx1ZTogJ3d4LXgxJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWyd3eC00LjUnXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9DQogICAgICBdOw0KICAgICAgLy8g5bGV5byA55u45YWz5Yy65Z+fDQogICAgICB0aGlzLmFjdGl2ZUNvbGxhcHNlcyA9IFsnYWktc2VsZWN0aW9uJywgJ3Byb21wdC1pbnB1dCddOw0KDQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWIm+W7uuaWsOWvueivnScpOw0KICAgIH0sDQoNCiAgICAvLyDliqDovb3kuIrmrKHkvJror50NCiAgICBhc3luYyBsb2FkTGFzdENoYXQoKSB7DQogICAgICB0cnkgew0KICAgICAgICBjb25zdCByZXMgPSBhd2FpdCBnZXRDaGF0SGlzdG9yeSh0aGlzLnVzZXJJZCwwKTsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDAgJiYgcmVzLmRhdGEgJiYgcmVzLmRhdGEubGVuZ3RoID4gMCkgew0KICAgICAgICAgIC8vIOiOt+WPluacgOaWsOeahOS8muivneiusOW9lQ0KICAgICAgICAgIGNvbnN0IGxhc3RDaGF0ID0gcmVzLmRhdGFbMF07DQogICAgICAgICAgdGhpcy5sb2FkSGlzdG9yeUl0ZW0obGFzdENoYXQpOw0KICAgICAgICB9DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3kuIrmrKHkvJror53lpLHotKU6JywgZXJyb3IpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDliKTmlq3mmK/lkKbkuLrlm77niYfmlofku7YNCiAgICBpc0ltYWdlRmlsZSh1cmwpIHsNCiAgICAgIGlmICghdXJsKSByZXR1cm4gZmFsc2U7DQogICAgICBjb25zdCBpbWFnZUV4dGVuc2lvbnMgPSBbJy5qcGcnLCAnLmpwZWcnLCAnLnBuZycsICcuZ2lmJywgJy5ibXAnLCAnLndlYnAnLCAnLnN2ZyddOw0KICAgICAgY29uc3QgdXJsTG93ZXIgPSB1cmwudG9Mb3dlckNhc2UoKTsNCiAgICAgIHJldHVybiBpbWFnZUV4dGVuc2lvbnMuc29tZShleHQgPT4gdXJsTG93ZXIuaW5jbHVkZXMoZXh0KSk7DQogICAgfSwNCg0KICAgIC8vIOWIpOaWreaYr+WQpuS4ulBERuaWh+S7tg0KICAgIGlzUGRmRmlsZSh1cmwpIHsNCiAgICAgIGlmICghdXJsKSByZXR1cm4gZmFsc2U7DQogICAgICByZXR1cm4gdXJsLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJy5wZGYnKTsNCiAgICB9LA0KDQogICAgLy8g5qC55o2uQUnlkI3np7Dojrflj5blm77niYfmoLflvI8NCiAgICBnZXRJbWFnZVN0eWxlKGFpTmFtZSkgew0KICAgICAgY29uc3Qgd2lkdGhNYXAgPSB7DQogICAgICAgICdUdXJib1NA5YWD5ZmoJzogJzcwMHB4JywNCiAgICAgICAgJ+iFvuiur+WFg+WunURTJzogJzcwMHB4JywNCiAgICAgICAgJ1R1cmJvU+mVv+aWh+eJiEDlhYPlmagnOiAnNzAwcHgnLA0KICAgICAgICAn6IW+6K6v5YWD5a6dVDEnOiAnNzAwcHgnLA0KICAgICAgICAn6LGG5YyFJzogJzU2MHB4JywNCiAgICAgICAgJ+aWh+W/g+S4gOiogCc6ICc3MDBweCcNCiAgICAgIH07DQoNCiAgICAgIGNvbnN0IHdpZHRoID0gd2lkdGhNYXBbYWlOYW1lXSB8fCAnNTYwcHgnOyAvLyDpu5jorqTlrr3luqYNCg0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgd2lkdGg6IHdpZHRoLA0KICAgICAgICBoZWlnaHQ6ICdhdXRvJw0KICAgICAgfTsNCiAgICB9LA0KDQogICAgLy8g5oqV6YCS5Yiw5YWs5LyX5Y+3DQogICAgaGFuZGxlUHVzaFRvV2VjaGF0KHJlc3VsdCkgew0KICAgICAgaWYgKHRoaXMucHVzaGluZ1RvV2VjaGF0KSByZXR1cm47IC8vIOmYsuatoumHjeWkjeeCueWHuw0KDQogICAgICB0aGlzLnB1c2hpbmdUb1dlY2hhdCA9IHRydWU7IC8vIOW8gOWni2xvYWRpbmcNCiAgICAgIHRoaXMucHVzaE9mZmljZU51bSArPSAxOyAvLyDpgJLlop7nvJblj7cNCg0KICAgICAgY29uc3QgcGFyYW1zID0gew0KICAgICAgICBjb250ZW50VGV4dDogcmVzdWx0LmNvbnRlbnQsDQogICAgICAgIHNoYXJlVXJsOiByZXN1bHQuc2hhcmVVcmwsDQogICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgIG51bTogdGhpcy5wdXNoT2ZmaWNlTnVtLA0KICAgICAgICBhaU5hbWU6IHJlc3VsdC5haU5hbWUNCiAgICAgIH07DQoNCiAgICAgIHB1c2hBdXRvT2ZmaWNlKHBhcmFtcykudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCkgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5oqV6YCS5Yiw5YWs5LyX5Y+35oiQ5Yqf77yBJyk7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcihyZXMubXNnIHx8ICfmipXpgJLlpLHotKXvvIzor7fph43or5UnKTsNCiAgICAgICAgfQ0KICAgICAgfSkuY2F0Y2goZXJyb3IgPT4gew0KICAgICAgICBjb25zb2xlLmVycm9yKCfmipXpgJLliLDlhazkvJflj7flpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfmipXpgJLlpLHotKXvvIzor7fph43or5UnKTsNCiAgICAgIH0pLmZpbmFsbHkoKCkgPT4gew0KICAgICAgICB0aGlzLnB1c2hpbmdUb1dlY2hhdCA9IGZhbHNlOyAvLyDnu5PmnZ9sb2FkaW5nDQogICAgICB9KTsNCiAgICB9LA0KDQoNCiAgfQ0KfTsNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA", "file": "index.vue", "sourceRoot": "src/views/wechat/chrome", "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\">\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img :src=\"require('../../../assets/ai/celan.png')\" alt=\"历史记录\" class=\"history-icon\">\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer title=\"历史会话记录\" :visible.sync=\"historyDrawerVisible\" direction=\"rtl\" size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\">\r\n      <div class=\"history-content\">\r\n        <div v-for=\"(group, date) in groupedHistory\" :key=\"date\" class=\"history-group\">\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div v-for=\"(item, index) in group\" :key=\"index\" class=\"history-item\">\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i :class=\"['el-icon-arrow-right', {'is-expanded': item.isExpanded}]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">{{ formatHistoryTime(item.createTime) }}</div>\r\n              </div>\r\n              <div v-if=\"item.children && item.children.length > 0 && item.isExpanded\" class=\"history-children\">\r\n                <div v-for=\"(child, childIndex) in item.children\" :key=\"childIndex\" class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\">\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">{{ formatHistoryTime(child.createTime) }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card v-for=\"(ai, index) in aiList\" :key=\"index\" class=\"ai-card\" shadow=\"hover\">\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\">\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch v-model=\"ai.enabled\" active-color=\"#13ce66\" inactive-color=\"#ff4949\">\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <div class=\"button-capability-group\">\r\n                    <el-button v-for=\"capability in ai.capabilities\" :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\" :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\" class=\"capability-button\">\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input type=\"textarea\" :rows=\"5\" placeholder=\"请输入提示词，支持Markdown格式\" v-model=\"promptInput\" resize=\"none\"\r\n              class=\"prompt-input\">\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button type=\"primary\" @click=\"sendPrompt\" :disabled=\"!canSend\" class=\"send-button\">\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div v-for=\"(ai, index) in enabledAIs\" :key=\"index\" class=\"task-item\">\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i :class=\"['el-icon-arrow-right', {'is-expanded': ai.isExpanded}]\"></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{ getStatusText(ai.status) }}</span>\r\n                      <i :class=\"getStatusIcon(ai.status)\" class=\"status-icon\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div class=\"progress-timeline\" v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\">\r\n                    <div class=\"timeline-scroll\">\r\n                      <div v-for=\"(log, logIndex) in ai.progressLogs\" :key=\"logIndex\" class=\"progress-item\" :class=\"{\r\n                             'completed': log.isCompleted || logIndex > 0,\r\n                             'current': !log.isCompleted && logIndex === 0\r\n                           }\">\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div class=\"progress-line\" v-if=\"logIndex < ai.progressLogs.length - 1\"></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch v-model=\"autoPlay\" active-text=\"自动轮播\" inactive-text=\"手动切换\">\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"700px\">\r\n                  <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n                    <img :src=\"screenshot\" alt=\"执行截图\" class=\"screenshot-image\" @click=\"showLargeImage(screenshot)\">\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" :name=\"'result-' + index\">\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-link\" @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\">\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-s-promotion\" @click=\"handlePushToWechat(result)\"\r\n                    class=\"push-wechat-btn\" :loading=\"pushingToWechat\" :disabled=\"pushingToWechat\">\r\n                    投递到公众号\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img v-if=\"isImageFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" alt=\"分享图片\" class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\">\r\n                <!-- 渲染PDF -->\r\n                <iframe v-else-if=\"isPdfFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" class=\"share-pdf\"\r\n                  frameborder=\"0\">\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-document\" @click=\"openShareUrl(result.shareImgUrl)\">\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div v-else class=\"markdown-content\" v-html=\"renderMarkdown(result.content)\"></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"small\" type=\"primary\" @click=\"copyResult(result.content)\">复制（纯文本）</el-button>\r\n                <el-button size=\"small\" type=\"success\" @click=\"exportResult(result)\">导出（MD文件）</el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog :visible.sync=\"showImageDialog\" width=\"90%\" :show-close=\"true\" :modal=\"true\" center class=\"image-dialog\"\r\n      :append-to-body=\"true\" @close=\"closeLargeImage\">\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\" class=\"single-image-container\">\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\">\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel v-else :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"80vh\">\r\n          <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\">\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog title=\"智能评分\" :visible.sync=\"scoreDialogVisible\" width=\"60%\" height=\"65%\" :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\">\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input type=\"textarea\" :rows=\"10\" placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\" v-model=\"scorePrompt\"\r\n            resize=\"none\" class=\"score-prompt-input\">\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" class=\"result-checkbox\">\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n\r\n\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {marked} from 'marked';\r\nimport {message, saveUserChatData, getChatHistory, pushAutoOffice} from \"@/api/wechat/aigc\";\r\nimport {\r\n\t\tv4 as uuidv4\r\n\t} from 'uuid';\r\nimport websocketClient from '@/utils/websocket';\r\nimport store from '@/store';\r\nimport TurndownService from 'turndown';\r\n\r\nexport default {\r\n  name: 'AIManagementPlatform',\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: '',\r\n        userId: '',\r\n        corpId: '',\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: '',\r\n        params: {}\r\n      },\r\n      aiList: [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: '搜狗搜索@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '文心一言',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '文心4.5 Turbo', value: 'wx-4.5' },\r\n            { label: '文心X1 Turbo', value: 'wx-x1' }\r\n          ],\r\n          selectedCapabilities: ['wx-4.5'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: '',\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: 'result-0',\r\n      activeCollapses: ['ai-selection', 'prompt-input'], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: '',\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: 'atx',\r\n        codeBlockStyle: 'fenced',\r\n        emDelimiter: '*'\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇公众号章，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return this.promptInput.trim().length > 0 && this.aiList.some(ai => ai.enabled);\r\n    },\r\n    canScore() {\r\n      return this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach(item => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach(chatGroup => {\r\n        // 按时间排序\r\n        chatGroup.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map(child => ({\r\n            ...child,\r\n            isParent: false\r\n          }))\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    }\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots =[];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = '';\r\n\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter(ai => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach(ai => {\r\n        this.$set(ai, 'status', 'running');\r\n      });\r\n\r\n      this.enabledAIs.forEach(ai => {\r\n        if(ai.name === '腾讯元宝T1'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === '腾讯元宝DS'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === 'TurboS@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-trubos-agent,';\r\n        }\r\n        if(ai.name === 'TurboS长文版@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-turbos-large-agent,';\r\n        }\r\n        if(ai.name === 'MiniMax@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-mini-max-agent,';\r\n        }\r\n        // if(ai.name === '搜狗搜索@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-sogou-agent,';\r\n        // }\r\n        // if(ai.name === 'KIMI@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-lwss-agent,';\r\n        // }\r\n        if(ai.name === '豆包'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db,';\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db-sdsk,';\r\n          }\r\n        }\r\n        if(ai.name === '文心一言'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'wx-,';\r\n          if (ai.selectedCapabilities.includes(\"wx-4.5\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'wx-4.5,';\r\n          }\r\n          if (ai.selectedCapabilities.includes(\"wx-x1\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'wx-x1,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq)\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\"\r\n      this.jsonRpcReqest.params = this.userInfoReq\r\n      this.message(this.jsonRpcReqest)\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then(res => {\r\n        if (res.code == 201) {\r\n          uni.showToast({\r\n            title: res.messages,\r\n            icon: 'none',\r\n            duration: 1500,\r\n          });\r\n        }\r\n      })\r\n\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log('切换前:', ai.selectedCapabilities);\r\n      if (index === -1) {\r\n        // 如果不存在，则添加\r\n        this.$set(ai.selectedCapabilities, ai.selectedCapabilities.length, capabilityValue);\r\n      } else {\r\n        // 如果已存在，则移除\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        newCapabilities.splice(index, 1);\r\n        this.$set(ai, 'selectedCapabilities', newCapabilities);\r\n      }\r\n      console.log('切换后:', ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 'idle': return '等待中';\r\n        case 'running': return '正在执行';\r\n        case 'completed': return '已完成';\r\n        case 'failed': return '执行失败';\r\n        default: return '未知状态';\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case 'idle': return 'el-icon-time';\r\n        case 'running': return 'el-icon-loading';\r\n        case 'completed': return 'el-icon-check success-icon';\r\n        case 'failed': return 'el-icon-close error-icon';\r\n        default: return 'el-icon-question';\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement('div');\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || '';\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement('textarea');\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textarea);\r\n      this.$message.success('已复制纯文本到剪贴板');\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: 'text/markdown' });\r\n      const link = document.createElement('a');\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date().toISOString().slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success('已导出Markdown文件');\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, '_blank');\r\n      } else {\r\n        this.$message.warning('暂无原链接');\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector('.image-dialog .el-carousel');\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = '';\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log('WebSocket URL:', process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case 'open':\r\n            // this.$message.success('');\r\n            break;\r\n          case 'message':\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case 'close':\r\n            this.$message.warning('WebSocket连接已关闭');\r\n            break;\r\n          case 'error':\r\n            this.$message.error('WebSocket连接错误');\r\n            break;\r\n          case 'reconnect_failed':\r\n            this.$message.error('WebSocket重连失败，请刷新页面重试');\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === 'RETURN_YBT1_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_YBDS_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_DB_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_LOG' && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n        if (targetAI) {\r\n          // 将新进度添加到数组开头\r\n          targetAI.progressLogs.unshift({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            isCompleted: false\r\n          });\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理截图消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_IMG' && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n              // 处理智能评分结果\r\n      if (dataObj.type === 'RETURN_WKPF_RES') {\r\n        const wkpfAI = this.enabledAIs.find(ai => ai.name === '智能评分');\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, 'status', 'completed');\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: '智能评分',\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case 'RETURN_YBT1_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝T1');\r\n          break;\r\n        case 'RETURN_YBDS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝DS');\r\n          break;\r\n        case 'RETURN_DB_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '豆包');\r\n          break;\r\n        case 'RETURN_WX_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '文心一言');\r\n          break;\r\n        case 'RETURN_TURBOS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS@元器');\r\n          break;\r\n        case 'RETURN_TURBOS_LARGE_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS长文版@元器');\r\n          break;\r\n        // case 'RETURN_MINI_MAX_RES':\r\n        //   targetAI = this.enabledAIs.find(ai => ai.name === 'MiniMax@元器');\r\n        //   break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, 'status', 'completed');\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], 'isCompleted', true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(r => r.aiName === targetAI.name);\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n      // 检查是否所有任务都已完成\r\n      // const allCompleted = this.enabledAIs.every(ai =>\r\n      //   ai.status === 'completed' || ai.status === 'failed'\r\n      // );\r\n\r\n      // if (allCompleted) {\r\n      //\r\n      // }\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error('WebSocket未连接');\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, 'isExpanded', !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter(result => this.selectedResults.includes(result.aiName))\r\n        .map(result => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join('\\n');\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: 'AI评分',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: 'zj-db-sdsk' // 默认使用豆包进行评分\r\n        }\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest)\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: '智能评分',\r\n        avatar: require('../../../assets/ai/yuanbao.png'),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: 'running',\r\n        progressLogs: [\r\n          {\r\n            content: '智能评分任务已提交，正在评分...',\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: '智能评分'\r\n          }\r\n        ],\r\n        isExpanded: true\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能评分');\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success('评分请求已发送，请等待结果');\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return '今天';\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return '昨天';\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return '两天前';\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return '三天前';\r\n      } else {\r\n        return date.toLocaleDateString('zh-CN', {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric'\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || '';\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || '';\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || '';\r\n        this.userInfoReq.dbChatId = item.dbChatId || '';\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success('历史记录加载成功');\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId\r\n        });\r\n      } catch (error) {\r\n        console.error('保存历史记录失败:', error);\r\n        this.$message.error('保存历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(this.expandedHistoryItems, item.chatId, !this.expandedHistoryItems[item.chatId]);\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = '';\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: '',\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '文心一言',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '文心4.5 Turbo', value: 'wx-4.5' },\r\n            { label: '文心X1 Turbo', value: 'wx-x1' }\r\n          ],\r\n          selectedCapabilities: ['wx-4.5'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n\r\n      this.$message.success('已创建新对话');\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId,0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载上次会话失败:', error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some(ext => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes('.pdf');\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        'TurboS@元器': '700px',\r\n        '腾讯元宝DS': '700px',\r\n        'TurboS长文版@元器': '700px',\r\n        '腾讯元宝T1': '700px',\r\n        '豆包': '560px',\r\n        '文心一言': '700px'\r\n      };\r\n\r\n      const width = widthMap[aiName] || '560px'; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: 'auto'\r\n      };\r\n    },\r\n\r\n    // 投递到公众号\r\n    handlePushToWechat(result) {\r\n      if (this.pushingToWechat) return; // 防止重复点击\r\n\r\n      this.pushingToWechat = true; // 开始loading\r\n      this.pushOfficeNum += 1; // 递增编号\r\n\r\n      const params = {\r\n        contentText: result.content,\r\n        shareUrl: result.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: result.aiName\r\n      };\r\n\r\n      pushAutoOffice(params).then(res => {\r\n        if (res.code === 200) {\r\n          this.$message.success('投递到公众号成功！');\r\n        } else {\r\n          this.$message.error(res.msg || '投递失败，请重试');\r\n        }\r\n      }).catch(error => {\r\n        console.error('投递到公众号失败:', error);\r\n        this.$message.error('投递失败，请重试');\r\n      }).finally(() => {\r\n        this.pushingToWechat = false; // 结束loading\r\n      });\r\n    },\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding:20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card, .screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn, .push-wechat-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409EFF;\r\n  border-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409EFF;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n</style>\r\n"]}]}