{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1751871074631}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751784291203}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQge21hcmtlZH0gZnJvbSAnbWFya2VkJzsNCmltcG9ydCB7bWVzc2FnZSwgc2F2ZVVzZXJDaGF0RGF0YSwgZ2V0Q2hhdEhpc3RvcnksIHB1c2hBdXRvT2ZmaWNlfSBmcm9tICJAL2FwaS93ZWNoYXQvYWlnYyI7DQppbXBvcnQgew0KCQl2NCBhcyB1dWlkdjQNCgl9IGZyb20gJ3V1aWQnOw0KaW1wb3J0IHdlYnNvY2tldENsaWVudCBmcm9tICdAL3V0aWxzL3dlYnNvY2tldCc7DQppbXBvcnQgc3RvcmUgZnJvbSAnQC9zdG9yZSc7DQppbXBvcnQgVHVybmRvd25TZXJ2aWNlIGZyb20gJ3R1cm5kb3duJzsNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAnQUlNYW5hZ2VtZW50UGxhdGZvcm0nLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICB1c2VySWQ6IHN0b3JlLnN0YXRlLnVzZXIuaWQsDQogICAgICBjb3JwSWQ6IHN0b3JlLnN0YXRlLnVzZXIuY29ycF9pZCwNCiAgICAgIGNoYXRJZDogdXVpZHY0KCksDQogICAgICBleHBhbmRlZEhpc3RvcnlJdGVtczoge30sDQogICAgICB1c2VySW5mb1JlcTogew0KICAgICAgICB1c2VyUHJvbXB0OiAnJywNCiAgICAgICAgdXNlcklkOiAnJywNCiAgICAgICAgY29ycElkOiAnJywNCiAgICAgICAgdGFza0lkOiAnJywNCiAgICAgICAgcm9sZXM6ICcnLA0KICAgICAgICB0b25lQ2hhdElkOiAnJywNCiAgICAgICAgeWJEc0NoYXRJZDogJycsDQogICAgICAgIGRiQ2hhdElkOiAnJywNCiAgICAgICAgaXNOZXdDaGF0OiB0cnVlDQogICAgICB9LA0KICAgICAganNvblJwY1JlcWVzdDogew0KICAgICAgICBqc29ucnBjOiAnMi4wJywNCiAgICAgICAgaWQ6IHV1aWR2NCgpLA0KICAgICAgICBtZXRob2Q6ICcnLA0KICAgICAgICBwYXJhbXM6IHt9DQogICAgICB9LA0KICAgICAgYWlMaXN0OiBbDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAnVHVyYm9TQOWFg+WZqCcsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ1R1cmJvU+mVv+aWh+eJiEDlhYPlmagnLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgLy8gew0KICAgICAgICAvLyAgIG5hbWU6ICdNaW5pTWF4QOWFg+WZqCcsDQogICAgICAgIC8vICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgLy8gICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAvLyAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAvLyAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAvLyAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgIC8vICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICAvLyB9LA0KICAgICAgICAvLyB7DQogICAgICAgIC8vICAgbmFtZTogJ+aQnOeLl+aQnOe0okDlhYPlmagnLA0KICAgICAgICAvLyAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgIC8vICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgIC8vICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgLy8gICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgLy8gICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAvLyAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgLy8gfSwNCiAgICAgICAgLy8gew0KICAgICAgICAvLyAgIG5hbWU6ICdLSU1JQOWFg+WZqCcsDQogICAgICAgIC8vICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgLy8gICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAvLyAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAvLyAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAvLyAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgIC8vICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICAvLyB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+iFvuiur+WFg+WunVQxJywNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZycpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogWw0KICAgICAgICAgICAgeyBsYWJlbDogJ+a3seW6puaAneiAgycsIHZhbHVlOiAnZGVlcF90aGlua2luZycgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfogZTnvZHmkJzntKInLCB2YWx1ZTogJ3dlYl9zZWFyY2gnIH0NCiAgICAgICAgICBdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbJ2RlZXBfdGhpbmtpbmcnLCd3ZWJfc2VhcmNoJ10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfohb7orq/lhYPlrp1EUycsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICfmt7HluqbmgJ3ogIMnLCB2YWx1ZTogJ2RlZXBfdGhpbmtpbmcnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn6IGU572R5pCc57SiJywgdmFsdWU6ICd3ZWJfc2VhcmNoJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWydkZWVwX3RoaW5raW5nJywnd2ViX3NlYXJjaCddLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6LGG5YyFJywNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS/osYbljIUucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5rex5bqm5oCd6ICDJywgdmFsdWU6ICdkZWVwX3RoaW5raW5nJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWydkZWVwX3RoaW5raW5nJ10sDQogICAgICAgICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfmloflv4PkuIDoqIAnLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3dlbnhpbi5wbmcnKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICfmloflv4M0LjUgVHVyYm8nLCB2YWx1ZTogJ3d4LTQuNScgfSwNCiAgICAgICAgICAgIHsgbGFiZWw6ICfmloflv4NYMSBUdXJibycsIHZhbHVlOiAnd3gteDEnIH0NCiAgICAgICAgICBdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbJ3d4LTQuNSddLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIH0NCiAgICAgIF0sDQogICAgICBwcm9tcHRJbnB1dDogJycsDQogICAgICB0YXNrU3RhcnRlZDogZmFsc2UsDQogICAgICBhdXRvUGxheTogZmFsc2UsDQogICAgICBzY3JlZW5zaG90czogW10sDQogICAgICByZXN1bHRzOiBbXSwNCiAgICAgIGFjdGl2ZVJlc3VsdFRhYjogJ3Jlc3VsdC0wJywNCiAgICAgIGFjdGl2ZUNvbGxhcHNlczogWydhaS1zZWxlY3Rpb24nLCAncHJvbXB0LWlucHV0J10sIC8vIOm7mOiupOWxleW8gOi/meS4pOS4quWMuuWfnw0KICAgICAgc2hvd0ltYWdlRGlhbG9nOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRMYXJnZUltYWdlOiAnJywNCiAgICAgIGVuYWJsZWRBSXM6IFtdLA0KICAgICAgdHVybmRvd25TZXJ2aWNlOiBuZXcgVHVybmRvd25TZXJ2aWNlKHsNCiAgICAgICAgaGVhZGluZ1N0eWxlOiAnYXR4JywNCiAgICAgICAgY29kZUJsb2NrU3R5bGU6ICdmZW5jZWQnLA0KICAgICAgICBlbURlbGltaXRlcjogJyonDQogICAgICB9KSwNCiAgICAgIHNjb3JlRGlhbG9nVmlzaWJsZTogZmFsc2UsDQogICAgICBzZWxlY3RlZFJlc3VsdHM6IFtdLA0KICAgICAgc2NvcmVQcm9tcHQ6IGDor7fkvaDmt7HluqbpmIXor7vku6XkuIvlh6Dnr4flhazkvJflj7fnq6DvvIzku47lpJrkuKrnu7Tluqbov5vooYzpgJDpobnmiZPliIbvvIzovpPlh7ror4TliIbnu5PmnpzjgILlubblnKjku6XkuIvlkITnr4fmlofnq6DnmoTln7rnoYDkuIrljZrph4fkvJfplb/vvIznu7zlkIjmlbTnkIbkuIDnr4fmm7TlhajpnaLnmoTmlofnq6DjgIJgLA0KICAgICAgaGlzdG9yeURyYXdlclZpc2libGU6IGZhbHNlLA0KICAgICAgY2hhdEhpc3Rvcnk6IFtdLA0KICAgICAgcHVzaE9mZmljZU51bTogMCwgLy8g5oqV6YCS5Yiw5YWs5LyX5Y+355qE6YCS5aKe57yW5Y+3DQogICAgICBwdXNoaW5nVG9XZWNoYXQ6IGZhbHNlLCAvLyDmipXpgJLliLDlhazkvJflj7fnmoRsb2FkaW5n54q25oCBDQogICAgfTsNCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCiAgICBjYW5TZW5kKCkgew0KICAgICAgcmV0dXJuIHRoaXMucHJvbXB0SW5wdXQudHJpbSgpLmxlbmd0aCA+IDAgJiYgdGhpcy5haUxpc3Quc29tZShhaSA9PiBhaS5lbmFibGVkKTsNCiAgICB9LA0KICAgIGNhblNjb3JlKCkgew0KICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0ZWRSZXN1bHRzLmxlbmd0aCA+IDAgJiYgdGhpcy5zY29yZVByb21wdC50cmltKCkubGVuZ3RoID4gMDsNCiAgICB9LA0KICAgIGdyb3VwZWRIaXN0b3J5KCkgew0KICAgICAgY29uc3QgZ3JvdXBzID0ge307DQogICAgICBjb25zdCBjaGF0R3JvdXBzID0ge307DQoNCiAgICAgIC8vIOmmluWFiOaMiWNoYXRJZOWIhue7hA0KICAgICAgdGhpcy5jaGF0SGlzdG9yeS5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICBpZiAoIWNoYXRHcm91cHNbaXRlbS5jaGF0SWRdKSB7DQogICAgICAgICAgY2hhdEdyb3Vwc1tpdGVtLmNoYXRJZF0gPSBbXTsNCiAgICAgICAgfQ0KICAgICAgICBjaGF0R3JvdXBzW2l0ZW0uY2hhdElkXS5wdXNoKGl0ZW0pOw0KICAgICAgfSk7DQoNCiAgICAgIC8vIOeEtuWQjuaMieaXpeacn+WIhue7hO+8jOW5tuWkhOeQhueItuWtkOWFs+ezuw0KICAgICAgT2JqZWN0LnZhbHVlcyhjaGF0R3JvdXBzKS5mb3JFYWNoKGNoYXRHcm91cCA9PiB7DQogICAgICAgIC8vIOaMieaXtumXtOaOkuW6jw0KICAgICAgICBjaGF0R3JvdXAuc29ydCgoYSwgYikgPT4gbmV3IERhdGUoYS5jcmVhdGVUaW1lKSAtIG5ldyBEYXRlKGIuY3JlYXRlVGltZSkpOw0KDQogICAgICAgIC8vIOiOt+WPluacgOaXqeeahOiusOW9leS9nOS4uueItue6pw0KICAgICAgICBjb25zdCBwYXJlbnRJdGVtID0gY2hhdEdyb3VwWzBdOw0KICAgICAgICBjb25zdCBkYXRlID0gdGhpcy5nZXRIaXN0b3J5RGF0ZShwYXJlbnRJdGVtLmNyZWF0ZVRpbWUpOw0KDQogICAgICAgIGlmICghZ3JvdXBzW2RhdGVdKSB7DQogICAgICAgICAgZ3JvdXBzW2RhdGVdID0gW107DQogICAgICAgIH0NCg0KICAgICAgICAvLyDmt7vliqDniLbnuqforrDlvZUNCiAgICAgICAgZ3JvdXBzW2RhdGVdLnB1c2goew0KICAgICAgICAgIC4uLnBhcmVudEl0ZW0sDQogICAgICAgICAgaXNQYXJlbnQ6IHRydWUsDQogICAgICAgICAgaXNFeHBhbmRlZDogdGhpcy5leHBhbmRlZEhpc3RvcnlJdGVtc1twYXJlbnRJdGVtLmNoYXRJZF0gfHwgZmFsc2UsDQogICAgICAgICAgY2hpbGRyZW46IGNoYXRHcm91cC5zbGljZSgxKS5tYXAoY2hpbGQgPT4gKHsNCiAgICAgICAgICAgIC4uLmNoaWxkLA0KICAgICAgICAgICAgaXNQYXJlbnQ6IGZhbHNlDQogICAgICAgICAgfSkpDQogICAgICAgIH0pOw0KICAgICAgfSk7DQoNCiAgICAgIHJldHVybiBncm91cHM7DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIGNvbnNvbGUubG9nKHRoaXMudXNlcklkKTsNCiAgICBjb25zb2xlLmxvZyh0aGlzLmNvcnBJZCk7DQogICAgdGhpcy5pbml0V2ViU29ja2V0KHRoaXMudXNlcklkKTsNCiAgICB0aGlzLmxvYWRDaGF0SGlzdG9yeSgwKTsgLy8g5Yqg6L295Y6G5Y+y6K6w5b2VDQogICAgdGhpcy5sb2FkTGFzdENoYXQoKTsgLy8g5Yqg6L295LiK5qyh5Lya6K+dDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBzZW5kUHJvbXB0KCkgew0KICAgICAgaWYgKCF0aGlzLmNhblNlbmQpIHJldHVybjsNCg0KICAgICAgdGhpcy5zY3JlZW5zaG90cyA9W107DQogICAgICAvLyDmipjlj6DmiYDmnInljLrln58NCiAgICAgIHRoaXMuYWN0aXZlQ29sbGFwc2VzID0gW107DQoNCiAgICAgIHRoaXMudGFza1N0YXJ0ZWQgPSB0cnVlOw0KICAgICAgdGhpcy5yZXN1bHRzID0gW107IC8vIOa4heepuuS5i+WJjeeahOe7k+aenA0KDQogICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gJyc7DQoNCg0KICAgICAgdGhpcy51c2VySW5mb1JlcS50YXNrSWQgPSB1dWlkdjQoKTsNCiAgICAgIHRoaXMudXNlckluZm9SZXEudXNlcklkID0gdGhpcy51c2VySWQ7DQogICAgICB0aGlzLnVzZXJJbmZvUmVxLmNvcnBJZCA9IHRoaXMuY29ycElkOw0KICAgICAgdGhpcy51c2VySW5mb1JlcS51c2VyUHJvbXB0ID0gdGhpcy5wcm9tcHRJbnB1dDsNCg0KICAgICAgLy8g6I635Y+W5ZCv55So55qEQUnliJfooajlj4rlhbbnirbmgIENCiAgICAgIHRoaXMuZW5hYmxlZEFJcyA9IHRoaXMuYWlMaXN0LmZpbHRlcihhaSA9PiBhaS5lbmFibGVkKTsNCg0KICAgICAgLy8g5bCG5omA5pyJ5ZCv55So55qEQUnnirbmgIHorr7nva7kuLrov5DooYzkuK0NCiAgICAgIHRoaXMuZW5hYmxlZEFJcy5mb3JFYWNoKGFpID0+IHsNCiAgICAgICAgdGhpcy4kc2V0KGFpLCAnc3RhdHVzJywgJ3J1bm5pbmcnKTsNCiAgICAgIH0pOw0KDQogICAgICB0aGlzLmVuYWJsZWRBSXMuZm9yRWFjaChhaSA9PiB7DQogICAgICAgIGlmKGFpLm5hbWUgPT09ICfohb7orq/lhYPlrp1UMScpew0KICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3liLWh1bnl1YW4tcHQsJzsNCiAgICAgICAgICBpZihhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmNsdWRlcygiZGVlcF90aGlua2luZyIpKXsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3liLWh1bnl1YW4tc2RzaywnOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmNsdWRlcygid2ViX3NlYXJjaCIpKXsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3liLWh1bnl1YW4tbHdzcywnOw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZihhaS5uYW1lID09PSAn6IW+6K6v5YWD5a6dRFMnKXsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICd5Yi1kZWVwc2Vlay1wdCwnOw0KICAgICAgICAgIGlmKGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzLmluY2x1ZGVzKCJkZWVwX3RoaW5raW5nIikpew0KICAgICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAneWItZGVlcHNlZWstc2RzaywnOw0KICAgICAgICAgIH0NCiAgICAgICAgICBpZihhaS5zZWxlY3RlZENhcGFiaWxpdGllcy5pbmNsdWRlcygid2ViX3NlYXJjaCIpKXsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3liLWRlZXBzZWVrLWx3c3MsJzsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgaWYoYWkubmFtZSA9PT0gJ1R1cmJvU0DlhYPlmagnKXsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICdjdWJlLXRydWJvcy1hZ2VudCwnOw0KICAgICAgICB9DQogICAgICAgIGlmKGFpLm5hbWUgPT09ICdUdXJib1Pplb/mlofniYhA5YWD5ZmoJyl7DQogICAgICAgICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAnY3ViZS10dXJib3MtbGFyZ2UtYWdlbnQsJzsNCiAgICAgICAgfQ0KICAgICAgICBpZihhaS5uYW1lID09PSAnTWluaU1heEDlhYPlmagnKXsNCiAgICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICdjdWJlLW1pbmktbWF4LWFnZW50LCc7DQogICAgICAgIH0NCiAgICAgICAgLy8gaWYoYWkubmFtZSA9PT0gJ+aQnOeLl+aQnOe0okDlhYPlmagnKXsNCiAgICAgICAgLy8gICB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzID0gdGhpcy51c2VySW5mb1JlcS5yb2xlcyArICdjdWJlLXNvZ291LWFnZW50LCc7DQogICAgICAgIC8vIH0NCiAgICAgICAgLy8gaWYoYWkubmFtZSA9PT0gJ0tJTUlA5YWD5ZmoJyl7DQogICAgICAgIC8vICAgdGhpcy51c2VySW5mb1JlcS5yb2xlcyA9IHRoaXMudXNlckluZm9SZXEucm9sZXMgKyAnY3ViZS1sd3NzLWFnZW50LCc7DQogICAgICAgIC8vIH0NCiAgICAgICAgaWYoYWkubmFtZSA9PT0gJ+ixhuWMhScpew0KICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3pqLWRiLCc7DQogICAgICAgICAgaWYgKGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzLmluY2x1ZGVzKCJkZWVwX3RoaW5raW5nIikpIHsNCiAgICAgICAgICAgIHRoaXMudXNlckluZm9SZXEucm9sZXMgPSB0aGlzLnVzZXJJbmZvUmVxLnJvbGVzICsgJ3pqLWRiLXNkc2ssJzsNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pOw0KDQogICAgICBjb25zb2xlLmxvZygi5Y+C5pWw77yaIiwgdGhpcy51c2VySW5mb1JlcSkNCg0KICAgICAgLy/osIPnlKjlkI7nq6/mjqXlj6MNCiAgICAgIHRoaXMuanNvblJwY1JlcWVzdC5tZXRob2QgPSAi5L2/55SoRjhTIg0KICAgICAgdGhpcy5qc29uUnBjUmVxZXN0LnBhcmFtcyA9IHRoaXMudXNlckluZm9SZXENCiAgICAgIHRoaXMubWVzc2FnZSh0aGlzLmpzb25ScGNSZXFlc3QpDQogICAgICB0aGlzLnVzZXJJbmZvUmVxLmlzTmV3Q2hhdCA9IGZhbHNlOw0KICAgIH0sDQoNCiAgICBtZXNzYWdlKGRhdGEpIHsNCiAgICAgIG1lc3NhZ2UoZGF0YSkudGhlbihyZXMgPT4gew0KICAgICAgICBpZiAocmVzLmNvZGUgPT0gMjAxKSB7DQogICAgICAgICAgdW5pLnNob3dUb2FzdCh7DQogICAgICAgICAgICB0aXRsZTogcmVzLm1lc3NhZ2VzLA0KICAgICAgICAgICAgaWNvbjogJ25vbmUnLA0KICAgICAgICAgICAgZHVyYXRpb246IDE1MDAsDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICB9LA0KICAgIHRvZ2dsZUNhcGFiaWxpdHkoYWksIGNhcGFiaWxpdHlWYWx1ZSkgew0KICAgICAgaWYgKCFhaS5lbmFibGVkKSByZXR1cm47DQoNCiAgICAgIGNvbnN0IGluZGV4ID0gYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMuaW5kZXhPZihjYXBhYmlsaXR5VmFsdWUpOw0KICAgICAgY29uc29sZS5sb2coJ+WIh+aNouWJjTonLCBhaS5zZWxlY3RlZENhcGFiaWxpdGllcyk7DQogICAgICBpZiAoaW5kZXggPT09IC0xKSB7DQogICAgICAgIC8vIOWmguaenOS4jeWtmOWcqO+8jOWImea3u+WKoA0KICAgICAgICB0aGlzLiRzZXQoYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXMsIGFpLnNlbGVjdGVkQ2FwYWJpbGl0aWVzLmxlbmd0aCwgY2FwYWJpbGl0eVZhbHVlKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOW3suWtmOWcqO+8jOWImeenu+mZpA0KICAgICAgICBjb25zdCBuZXdDYXBhYmlsaXRpZXMgPSBbLi4uYWkuc2VsZWN0ZWRDYXBhYmlsaXRpZXNdOw0KICAgICAgICBuZXdDYXBhYmlsaXRpZXMuc3BsaWNlKGluZGV4LCAxKTsNCiAgICAgICAgdGhpcy4kc2V0KGFpLCAnc2VsZWN0ZWRDYXBhYmlsaXRpZXMnLCBuZXdDYXBhYmlsaXRpZXMpOw0KICAgICAgfQ0KICAgICAgY29uc29sZS5sb2coJ+WIh+aNouWQjjonLCBhaS5zZWxlY3RlZENhcGFiaWxpdGllcyk7DQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpOyAvLyDlvLrliLbmm7TmlrDop4blm74NCiAgICB9LA0KICAgIGdldFN0YXR1c1RleHQoc3RhdHVzKSB7DQogICAgICBzd2l0Y2ggKHN0YXR1cykgew0KICAgICAgICBjYXNlICdpZGxlJzogcmV0dXJuICfnrYnlvoXkuK0nOw0KICAgICAgICBjYXNlICdydW5uaW5nJzogcmV0dXJuICfmraPlnKjmiafooYwnOw0KICAgICAgICBjYXNlICdjb21wbGV0ZWQnOiByZXR1cm4gJ+W3suWujOaIkCc7DQogICAgICAgIGNhc2UgJ2ZhaWxlZCc6IHJldHVybiAn5omn6KGM5aSx6LSlJzsNCiAgICAgICAgZGVmYXVsdDogcmV0dXJuICfmnKrnn6XnirbmgIEnOw0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0U3RhdHVzSWNvbihzdGF0dXMpIHsNCiAgICAgIHN3aXRjaCAoc3RhdHVzKSB7DQogICAgICAgIGNhc2UgJ2lkbGUnOiByZXR1cm4gJ2VsLWljb24tdGltZSc7DQogICAgICAgIGNhc2UgJ3J1bm5pbmcnOiByZXR1cm4gJ2VsLWljb24tbG9hZGluZyc7DQogICAgICAgIGNhc2UgJ2NvbXBsZXRlZCc6IHJldHVybiAnZWwtaWNvbi1jaGVjayBzdWNjZXNzLWljb24nOw0KICAgICAgICBjYXNlICdmYWlsZWQnOiByZXR1cm4gJ2VsLWljb24tY2xvc2UgZXJyb3ItaWNvbic7DQogICAgICAgIGRlZmF1bHQ6IHJldHVybiAnZWwtaWNvbi1xdWVzdGlvbic7DQogICAgICB9DQogICAgfSwNCiAgICByZW5kZXJNYXJrZG93bih0ZXh0KSB7DQogICAgICByZXR1cm4gbWFya2VkKHRleHQpOw0KICAgIH0sDQogICAgLy8gSFRNTOi9rOe6r+aWh+acrA0KICAgIGh0bWxUb1RleHQoaHRtbCkgew0KICAgICAgY29uc3QgdGVtcERpdiA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2RpdicpOw0KICAgICAgdGVtcERpdi5pbm5lckhUTUwgPSBodG1sOw0KICAgICAgcmV0dXJuIHRlbXBEaXYudGV4dENvbnRlbnQgfHwgdGVtcERpdi5pbm5lclRleHQgfHwgJyc7DQogICAgfSwNCg0KICAgIC8vIEhUTUzovaxNYXJrZG93bg0KICAgIGh0bWxUb01hcmtkb3duKGh0bWwpIHsNCiAgICAgIHJldHVybiB0aGlzLnR1cm5kb3duU2VydmljZS50dXJuZG93bihodG1sKTsNCiAgICB9LA0KDQogICAgY29weVJlc3VsdChjb250ZW50KSB7DQogICAgICAvLyDlsIZIVE1M6L2s5o2i5Li657qv5paH5pysDQogICAgICBjb25zdCBwbGFpblRleHQgPSB0aGlzLmh0bWxUb1RleHQoY29udGVudCk7DQogICAgICBjb25zdCB0ZXh0YXJlYSA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3RleHRhcmVhJyk7DQogICAgICB0ZXh0YXJlYS52YWx1ZSA9IHBsYWluVGV4dDsNCiAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQodGV4dGFyZWEpOw0KICAgICAgdGV4dGFyZWEuc2VsZWN0KCk7DQogICAgICBkb2N1bWVudC5leGVjQ29tbWFuZCgnY29weScpOw0KICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZCh0ZXh0YXJlYSk7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWkjeWItue6r+aWh+acrOWIsOWJqui0tOadvycpOw0KICAgIH0sDQoNCiAgICBleHBvcnRSZXN1bHQocmVzdWx0KSB7DQogICAgICAvLyDlsIZIVE1M6L2s5o2i5Li6TWFya2Rvd24NCiAgICAgIGNvbnN0IG1hcmtkb3duID0gcmVzdWx0LmNvbnRlbnQ7DQogICAgICBjb25zdCBibG9iID0gbmV3IEJsb2IoW21hcmtkb3duXSwgeyB0eXBlOiAndGV4dC9tYXJrZG93bicgfSk7DQogICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYScpOw0KICAgICAgbGluay5ocmVmID0gVVJMLmNyZWF0ZU9iamVjdFVSTChibG9iKTsNCiAgICAgIGxpbmsuZG93bmxvYWQgPSBgJHtyZXN1bHQuYWlOYW1lfV/nu5PmnpxfJHtuZXcgRGF0ZSgpLnRvSVNPU3RyaW5nKCkuc2xpY2UoMCwgMTApfS5tZGA7DQogICAgICBsaW5rLmNsaWNrKCk7DQogICAgICBVUkwucmV2b2tlT2JqZWN0VVJMKGxpbmsuaHJlZik7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+W3suWvvOWHuk1hcmtkb3du5paH5Lu2Jyk7DQogICAgfSwNCg0KICAgIG9wZW5TaGFyZVVybChzaGFyZVVybCkgew0KICAgICAgaWYgKHNoYXJlVXJsKSB7DQogICAgICAgIHdpbmRvdy5vcGVuKHNoYXJlVXJsLCAnX2JsYW5rJyk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoJ+aaguaXoOWOn+mTvuaOpScpOw0KICAgICAgfQ0KICAgIH0sDQogICAgc2hvd0xhcmdlSW1hZ2UoaW1hZ2VVcmwpIHsNCiAgICAgIHRoaXMuY3VycmVudExhcmdlSW1hZ2UgPSBpbWFnZVVybDsNCiAgICAgIHRoaXMuc2hvd0ltYWdlRGlhbG9nID0gdHJ1ZTsNCiAgICAgIC8vIOaJvuWIsOW9k+WJjeWbvueJh+eahOe0ouW8le+8jOiuvue9rui9ruaSreWbvueahOWIneWni+S9jee9rg0KICAgICAgY29uc3QgY3VycmVudEluZGV4ID0gdGhpcy5zY3JlZW5zaG90cy5pbmRleE9mKGltYWdlVXJsKTsNCiAgICAgIGlmIChjdXJyZW50SW5kZXggIT09IC0xKSB7DQogICAgICAgIHRoaXMuJG5leHRUaWNrKCgpID0+IHsNCiAgICAgICAgICBjb25zdCBjYXJvdXNlbCA9IHRoaXMuJGVsLnF1ZXJ5U2VsZWN0b3IoJy5pbWFnZS1kaWFsb2cgLmVsLWNhcm91c2VsJyk7DQogICAgICAgICAgaWYgKGNhcm91c2VsICYmIGNhcm91c2VsLl9fdnVlX18pIHsNCiAgICAgICAgICAgIGNhcm91c2VsLl9fdnVlX18uc2V0QWN0aXZlSXRlbShjdXJyZW50SW5kZXgpOw0KICAgICAgICAgIH0NCiAgICAgICAgfSk7DQogICAgICB9DQogICAgfSwNCiAgICBjbG9zZUxhcmdlSW1hZ2UoKSB7DQogICAgICB0aGlzLnNob3dJbWFnZURpYWxvZyA9IGZhbHNlOw0KICAgICAgdGhpcy5jdXJyZW50TGFyZ2VJbWFnZSA9ICcnOw0KICAgIH0sDQogICAgLy8gV2ViU29ja2V0IOebuOWFs+aWueazlQ0KICAgIGluaXRXZWJTb2NrZXQoaWQpIHsNCiAgICAgIGNvbnN0IHdzVXJsID0gcHJvY2Vzcy5lbnYuVlVFX0FQUF9XU19BUEkgKyBgbXlwYy0ke2lkfWA7DQogICAgICBjb25zb2xlLmxvZygnV2ViU29ja2V0IFVSTDonLCBwcm9jZXNzLmVudi5WVUVfQVBQX1dTX0FQSSk7DQogICAgICB3ZWJzb2NrZXRDbGllbnQuY29ubmVjdCh3c1VybCwgKGV2ZW50KSA9PiB7DQogICAgICAgIHN3aXRjaCAoZXZlbnQudHlwZSkgew0KICAgICAgICAgIGNhc2UgJ29wZW4nOg0KICAgICAgICAgICAgLy8gdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCcnKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgJ21lc3NhZ2UnOg0KICAgICAgICAgICAgdGhpcy5oYW5kbGVXZWJTb2NrZXRNZXNzYWdlKGV2ZW50LmRhdGEpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAnY2xvc2UnOg0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCdXZWJTb2NrZXTov57mjqXlt7LlhbPpl60nKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgJ2Vycm9yJzoNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ1dlYlNvY2tldOi/nuaOpemUmeivrycpOw0KICAgICAgICAgICAgYnJlYWs7DQogICAgICAgICAgY2FzZSAncmVjb25uZWN0X2ZhaWxlZCc6DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCdXZWJTb2NrZXTph43ov57lpLHotKXvvIzor7fliLfmlrDpobXpnaLph43or5UnKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KDQogICAgaGFuZGxlV2ViU29ja2V0TWVzc2FnZShkYXRhKSB7DQoNCiAgICAgIGNvbnN0IGRhdGFzdHIgPSBkYXRhOw0KICAgICAgY29uc3QgZGF0YU9iaiA9IEpTT04ucGFyc2UoZGF0YXN0cik7DQoNCiAgICAgIC8vIOWkhOeQhmNoYXRJZOa2iOaBrw0KICAgICAgaWYgKGRhdGFPYmoudHlwZSA9PT0gJ1JFVFVSTl9ZQlQxX0NIQVRJRCcgJiYgZGF0YU9iai5jaGF0SWQpIHsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS50b25lQ2hhdElkID0gZGF0YU9iai5jaGF0SWQ7DQogICAgICB9IGVsc2UgaWYgKGRhdGFPYmoudHlwZSA9PT0gJ1JFVFVSTl9ZQkRTX0NIQVRJRCcgJiYgZGF0YU9iai5jaGF0SWQpIHsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS55YkRzQ2hhdElkID0gZGF0YU9iai5jaGF0SWQ7DQogICAgICB9IGVsc2UgaWYgKGRhdGFPYmoudHlwZSA9PT0gJ1JFVFVSTl9EQl9DSEFUSUQnICYmIGRhdGFPYmouY2hhdElkKSB7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEuZGJDaGF0SWQgPSBkYXRhT2JqLmNoYXRJZDsNCiAgICAgIH0NCg0KICAgICAgLy8g5aSE55CG6L+b5bqm5pel5b+X5raI5oGvDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAnUkVUVVJOX1BDX1RBU0tfTE9HJyAmJiBkYXRhT2JqLmFpTmFtZSkgew0KICAgICAgICBjb25zdCB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKGFpID0+IGFpLm5hbWUgPT09IGRhdGFPYmouYWlOYW1lKTsNCiAgICAgICAgaWYgKHRhcmdldEFJKSB7DQogICAgICAgICAgLy8g5bCG5paw6L+b5bqm5re75Yqg5Yiw5pWw57uE5byA5aS0DQogICAgICAgICAgdGFyZ2V0QUkucHJvZ3Jlc3NMb2dzLnVuc2hpZnQoew0KICAgICAgICAgICAgY29udGVudDogZGF0YU9iai5jb250ZW50LA0KICAgICAgICAgICAgdGltZXN0YW1wOiBuZXcgRGF0ZSgpLA0KICAgICAgICAgICAgaXNDb21wbGV0ZWQ6IGZhbHNlDQogICAgICAgICAgfSk7DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDlpITnkIbmiKrlm77mtojmga8NCiAgICAgIGlmIChkYXRhT2JqLnR5cGUgPT09ICdSRVRVUk5fUENfVEFTS19JTUcnICYmIGRhdGFPYmoudXJsKSB7DQogICAgICAgIC8vIOWwhuaWsOeahOaIquWbvua3u+WKoOWIsOaVsOe7hOW8gOWktA0KICAgICAgICB0aGlzLnNjcmVlbnNob3RzLnVuc2hpZnQoZGF0YU9iai51cmwpOw0KICAgICAgICByZXR1cm47DQogICAgICB9DQoNCiAgICAgICAgICAgICAgLy8g5aSE55CG5pm66IO96K+E5YiG57uT5p6cDQogICAgICBpZiAoZGF0YU9iai50eXBlID09PSAnUkVUVVJOX1dLUEZfUkVTJykgew0KICAgICAgICBjb25zdCB3a3BmQUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZChhaSA9PiBhaS5uYW1lID09PSAn5pm66IO96K+E5YiGJyk7DQogICAgICAgIGlmICh3a3BmQUkpIHsNCiAgICAgICAgICB0aGlzLiRzZXQod2twZkFJLCAnc3RhdHVzJywgJ2NvbXBsZXRlZCcpOw0KICAgICAgICAgIGlmICh3a3BmQUkucHJvZ3Jlc3NMb2dzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIHRoaXMuJHNldCh3a3BmQUkucHJvZ3Jlc3NMb2dzWzBdLCAnaXNDb21wbGV0ZWQnLCB0cnVlKTsNCiAgICAgICAgICB9DQogICAgICAgICAgLy8g5re75Yqg6K+E5YiG57uT5p6c5YiwcmVzdWx0c+acgOWJjemdog0KICAgICAgICAgIHRoaXMucmVzdWx0cy51bnNoaWZ0KHsNCiAgICAgICAgICAgIGFpTmFtZTogJ+aZuuiDveivhOWIhicsDQogICAgICAgICAgICBjb250ZW50OiBkYXRhT2JqLmRyYWZ0Q29udGVudCwNCiAgICAgICAgICAgIHNoYXJlVXJsOiBkYXRhT2JqLnNoYXJlVXJsIHx8ICcnLA0KICAgICAgICAgICAgc2hhcmVJbWdVcmw6IGRhdGFPYmouc2hhcmVJbWdVcmwgfHwgJycsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmFjdGl2ZVJlc3VsdFRhYiA9ICdyZXN1bHQtMCc7DQoNCiAgICAgICAgICAvLyDmmbrog73or4TliIblrozmiJDml7bvvIzlho3mrKHkv53lrZjljoblj7LorrDlvZUNCiAgICAgICAgICB0aGlzLnNhdmVIaXN0b3J5KCk7DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuOw0KICAgICAgfQ0KDQogICAgICAvLyDmoLnmja7mtojmga/nsbvlnovmm7TmlrDlr7nlupRBSeeahOeKtuaAgeWSjOe7k+aenA0KICAgICAgbGV0IHRhcmdldEFJID0gbnVsbDsNCiAgICAgIHN3aXRjaCAoZGF0YU9iai50eXBlKSB7DQogICAgICAgIGNhc2UgJ1JFVFVSTl9ZQlQxX1JFUyc6DQogICAgICAgICAgY29uc29sZS5sb2coJ+aUtuWIsOa2iOaBrzonLCBkYXRhKTsNCiAgICAgICAgICB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKGFpID0+IGFpLm5hbWUgPT09ICfohb7orq/lhYPlrp1UMScpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdSRVRVUk5fWUJEU19SRVMnOg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfmlLbliLDmtojmga86JywgZGF0YSk7DQogICAgICAgICAgdGFyZ2V0QUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZChhaSA9PiBhaS5uYW1lID09PSAn6IW+6K6v5YWD5a6dRFMnKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAnUkVUVVJOX0RCX1JFUyc6DQogICAgICAgICAgY29uc29sZS5sb2coJ+aUtuWIsOa2iOaBrzonLCBkYXRhKTsNCiAgICAgICAgICB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKGFpID0+IGFpLm5hbWUgPT09ICfosYbljIUnKTsNCiAgICAgICAgICBicmVhazsNCiAgICAgICAgY2FzZSAnUkVUVVJOX1RVUkJPU19SRVMnOg0KICAgICAgICAgIGNvbnNvbGUubG9nKCfmlLbliLDmtojmga86JywgZGF0YSk7DQogICAgICAgICAgdGFyZ2V0QUkgPSB0aGlzLmVuYWJsZWRBSXMuZmluZChhaSA9PiBhaS5uYW1lID09PSAnVHVyYm9TQOWFg+WZqCcpOw0KICAgICAgICAgIGJyZWFrOw0KICAgICAgICBjYXNlICdSRVRVUk5fVFVSQk9TX0xBUkdFX1JFUyc6DQogICAgICAgICAgY29uc29sZS5sb2coJ+aUtuWIsOa2iOaBrzonLCBkYXRhKTsNCiAgICAgICAgICB0YXJnZXRBSSA9IHRoaXMuZW5hYmxlZEFJcy5maW5kKGFpID0+IGFpLm5hbWUgPT09ICdUdXJib1Pplb/mlofniYhA5YWD5ZmoJyk7DQogICAgICAgICAgYnJlYWs7DQogICAgICAgIC8vIGNhc2UgJ1JFVFVSTl9NSU5JX01BWF9SRVMnOg0KICAgICAgICAvLyAgIHRhcmdldEFJID0gdGhpcy5lbmFibGVkQUlzLmZpbmQoYWkgPT4gYWkubmFtZSA9PT0gJ01pbmlNYXhA5YWD5ZmoJyk7DQogICAgICAgIC8vICAgYnJlYWs7DQogICAgICB9DQoNCiAgICAgIGlmICh0YXJnZXRBSSkgew0KICAgICAgICAvLyDmm7TmlrBBSeeKtuaAgeS4uuW3suWujOaIkA0KICAgICAgICB0aGlzLiRzZXQodGFyZ2V0QUksICdzdGF0dXMnLCAnY29tcGxldGVkJyk7DQoNCiAgICAgICAgLy8g5bCG5pyA5ZCO5LiA5p2h6L+b5bqm5raI5oGv5qCH6K6w5Li65bey5a6M5oiQDQogICAgICAgIGlmICh0YXJnZXRBSS5wcm9ncmVzc0xvZ3MubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuJHNldCh0YXJnZXRBSS5wcm9ncmVzc0xvZ3NbMF0sICdpc0NvbXBsZXRlZCcsIHRydWUpOw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5re75Yqg57uT5p6c5Yiw5pWw57uE5byA5aS0DQogICAgICAgIGNvbnN0IHJlc3VsdEluZGV4ID0gdGhpcy5yZXN1bHRzLmZpbmRJbmRleChyID0+IHIuYWlOYW1lID09PSB0YXJnZXRBSS5uYW1lKTsNCiAgICAgICAgaWYgKHJlc3VsdEluZGV4ID09PSAtMSkgew0KICAgICAgICAgIHRoaXMucmVzdWx0cy51bnNoaWZ0KHsNCiAgICAgICAgICAgIGFpTmFtZTogdGFyZ2V0QUkubmFtZSwNCiAgICAgICAgICAgIGNvbnRlbnQ6IGRhdGFPYmouZHJhZnRDb250ZW50LA0KICAgICAgICAgICAgc2hhcmVVcmw6IGRhdGFPYmouc2hhcmVVcmwgfHwgJycsDQogICAgICAgICAgICBzaGFyZUltZ1VybDogZGF0YU9iai5zaGFyZUltZ1VybCB8fCAnJywNCiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKQ0KICAgICAgICAgIH0pOw0KICAgICAgICAgIHRoaXMuYWN0aXZlUmVzdWx0VGFiID0gJ3Jlc3VsdC0wJzsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnJlc3VsdHMuc3BsaWNlKHJlc3VsdEluZGV4LCAxKTsNCiAgICAgICAgICB0aGlzLnJlc3VsdHMudW5zaGlmdCh7DQogICAgICAgICAgICBhaU5hbWU6IHRhcmdldEFJLm5hbWUsDQogICAgICAgICAgICBjb250ZW50OiBkYXRhT2JqLmRyYWZ0Q29udGVudCwNCiAgICAgICAgICAgIHNoYXJlVXJsOiBkYXRhT2JqLnNoYXJlVXJsIHx8ICcnLA0KICAgICAgICAgICAgc2hhcmVJbWdVcmw6IGRhdGFPYmouc2hhcmVJbWdVcmwgfHwgJycsDQogICAgICAgICAgICB0aW1lc3RhbXA6IG5ldyBEYXRlKCkNCiAgICAgICAgICB9KTsNCiAgICAgICAgICB0aGlzLmFjdGl2ZVJlc3VsdFRhYiA9ICdyZXN1bHQtMCc7DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5zYXZlSGlzdG9yeSgpOw0KICAgICAgfQ0KDQogICAgICAvLyDmo4Dmn6XmmK/lkKbmiYDmnInku7vliqHpg73lt7LlrozmiJANCiAgICAgIC8vIGNvbnN0IGFsbENvbXBsZXRlZCA9IHRoaXMuZW5hYmxlZEFJcy5ldmVyeShhaSA9Pg0KICAgICAgLy8gICBhaS5zdGF0dXMgPT09ICdjb21wbGV0ZWQnIHx8IGFpLnN0YXR1cyA9PT0gJ2ZhaWxlZCcNCiAgICAgIC8vICk7DQoNCiAgICAgIC8vIGlmIChhbGxDb21wbGV0ZWQpIHsNCiAgICAgIC8vDQogICAgICAvLyB9DQogICAgfSwNCg0KICAgIGNsb3NlV2ViU29ja2V0KCkgew0KICAgICAgd2Vic29ja2V0Q2xpZW50LmNsb3NlKCk7DQogICAgfSwNCg0KICAgIHNlbmRNZXNzYWdlKGRhdGEpIHsNCiAgICAgIGlmICh3ZWJzb2NrZXRDbGllbnQuc2VuZChkYXRhKSkgew0KICAgICAgICAvLyDmu5rliqjliLDlupXpg6gNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMuc2Nyb2xsVG9Cb3R0b20oKTsNCiAgICAgICAgfSk7DQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCdXZWJTb2NrZXTmnKrov57mjqUnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHRvZ2dsZUFJRXhwYW5zaW9uKGFpKSB7DQogICAgICB0aGlzLiRzZXQoYWksICdpc0V4cGFuZGVkJywgIWFpLmlzRXhwYW5kZWQpOw0KICAgIH0sDQoNCiAgICBmb3JtYXRUaW1lKHRpbWVzdGFtcCkgew0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVzdGFtcCk7DQogICAgICByZXR1cm4gZGF0ZS50b0xvY2FsZVRpbWVTdHJpbmcoJ3poLUNOJywgew0KICAgICAgICBob3VyOiAnMi1kaWdpdCcsDQogICAgICAgIG1pbnV0ZTogJzItZGlnaXQnLA0KICAgICAgICBzZWNvbmQ6ICcyLWRpZ2l0JywNCiAgICAgICAgaG91cjEyOiBmYWxzZQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBzaG93U2NvcmVEaWFsb2coKSB7DQogICAgICB0aGlzLnNjb3JlRGlhbG9nVmlzaWJsZSA9IHRydWU7DQogICAgICB0aGlzLnNlbGVjdGVkUmVzdWx0cyA9IFtdOw0KICAgIH0sDQoNCiAgICBoYW5kbGVTY29yZSgpIHsNCiAgICAgIGlmICghdGhpcy5jYW5TY29yZSkgcmV0dXJuOw0KDQogICAgICAvLyDojrflj5bpgInkuK3nmoTnu5PmnpzlhoXlrrnlubbmjInnhafmjIflrprmoLzlvI/mi7zmjqUNCiAgICAgIGNvbnN0IHNlbGVjdGVkQ29udGVudHMgPSB0aGlzLnJlc3VsdHMNCiAgICAgICAgLmZpbHRlcihyZXN1bHQgPT4gdGhpcy5zZWxlY3RlZFJlc3VsdHMuaW5jbHVkZXMocmVzdWx0LmFpTmFtZSkpDQogICAgICAgIC5tYXAocmVzdWx0ID0+IHsNCiAgICAgICAgICAvLyDlsIZIVE1M5YaF5a656L2s5o2i5Li657qv5paH5pysDQogICAgICAgICAgY29uc3QgcGxhaW5Db250ZW50ID0gdGhpcy5odG1sVG9UZXh0KHJlc3VsdC5jb250ZW50KTsNCiAgICAgICAgICByZXR1cm4gYCR7cmVzdWx0LmFpTmFtZX3liJ3nqL/vvJpcbiR7cGxhaW5Db250ZW50fVxuYDsNCiAgICAgICAgfSkNCiAgICAgICAgLmpvaW4oJ1xuJyk7DQoNCiAgICAgIC8vIOaehOW7uuWujOaVtOeahOivhOWIhuaPkOekuuWGheWuuQ0KICAgICAgY29uc3QgZnVsbFByb21wdCA9IGAke3RoaXMuc2NvcmVQcm9tcHR9XG4ke3NlbGVjdGVkQ29udGVudHN9YDsNCg0KICAgICAgLy8g5p6E5bu66K+E5YiG6K+35rGCDQogICAgICBjb25zdCBzY29yZVJlcXVlc3QgPSB7DQogICAgICAgIGpzb25ycGM6ICcyLjAnLA0KICAgICAgICBpZDogdXVpZHY0KCksDQogICAgICAgIG1ldGhvZDogJ0FJ6K+E5YiGJywNCiAgICAgICAgcGFyYW1zOiB7DQogICAgICAgICAgdGFza0lkOiB1dWlkdjQoKSwNCiAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQsDQogICAgICAgICAgdXNlclByb21wdDogZnVsbFByb21wdCwNCiAgICAgICAgICByb2xlczogJ3pqLWRiLXNkc2snIC8vIOm7mOiupOS9v+eUqOixhuWMhei/m+ihjOivhOWIhg0KICAgICAgICB9DQogICAgICB9Ow0KDQogICAgICAvLyDlj5HpgIHor4TliIbor7fmsYINCiAgICAgIGNvbnNvbGUubG9nKCLlj4LmlbAiLCBzY29yZVJlcXVlc3QpDQogICAgICB0aGlzLm1lc3NhZ2Uoc2NvcmVSZXF1ZXN0KTsNCiAgICAgIHRoaXMuc2NvcmVEaWFsb2dWaXNpYmxlID0gZmFsc2U7DQoNCiAgICAgIC8vIOWIm+W7uuaZuuiDveivhOWIhkFJ6IqC54K5DQogICAgICBjb25zdCB3a3BmQUkgPSB7DQogICAgICAgIG5hbWU6ICfmmbrog73or4TliIYnLA0KICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZycpLA0KICAgICAgICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgIHN0YXR1czogJ3J1bm5pbmcnLA0KICAgICAgICBwcm9ncmVzc0xvZ3M6IFsNCiAgICAgICAgICB7DQogICAgICAgICAgICBjb250ZW50OiAn5pm66IO96K+E5YiG5Lu75Yqh5bey5o+Q5Lqk77yM5q2j5Zyo6K+E5YiGLi4uJywNCiAgICAgICAgICAgIHRpbWVzdGFtcDogbmV3IERhdGUoKSwNCiAgICAgICAgICAgIGlzQ29tcGxldGVkOiBmYWxzZSwNCiAgICAgICAgICAgIHR5cGU6ICfmmbrog73or4TliIYnDQogICAgICAgICAgfQ0KICAgICAgICBdLA0KICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICB9Ow0KDQogICAgICAvLyDmo4Dmn6XmmK/lkKblt7LlrZjlnKjmmbrog73or4TliIYNCiAgICAgIGNvbnN0IGV4aXN0SW5kZXggPSB0aGlzLmVuYWJsZWRBSXMuZmluZEluZGV4KGFpID0+IGFpLm5hbWUgPT09ICfmmbrog73or4TliIYnKTsNCiAgICAgIGlmIChleGlzdEluZGV4ID09PSAtMSkgew0KICAgICAgICAvLyDlpoLmnpzkuI3lrZjlnKjvvIzmt7vliqDliLDmlbDnu4TlvIDlpLQNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQod2twZkFJKTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIC8vIOWmguaenOW3suWtmOWcqO+8jOabtOaWsOeKtuaAgeWSjOaXpeW/lw0KICAgICAgICB0aGlzLmVuYWJsZWRBSXNbZXhpc3RJbmRleF0gPSB3a3BmQUk7DQogICAgICAgIC8vIOWwhuaZuuiDveivhOWIhuenu+WIsOaVsOe7hOW8gOWktA0KICAgICAgICBjb25zdCB3a3BmID0gdGhpcy5lbmFibGVkQUlzLnNwbGljZShleGlzdEluZGV4LCAxKVswXTsNCiAgICAgICAgdGhpcy5lbmFibGVkQUlzLnVuc2hpZnQod2twZik7DQogICAgICB9DQoNCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCk7DQogICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+ivhOWIhuivt+axguW3suWPkemAge+8jOivt+etieW+hee7k+aenCcpOw0KICAgIH0sDQogICAgLy8g5pi+56S65Y6G5Y+y6K6w5b2V5oq95bGJDQogICAgc2hvd0hpc3RvcnlEcmF3ZXIoKSB7DQogICAgICB0aGlzLmhpc3RvcnlEcmF3ZXJWaXNpYmxlID0gdHJ1ZTsNCiAgICAgIHRoaXMubG9hZENoYXRIaXN0b3J5KDEpOw0KICAgIH0sDQoNCiAgICAvLyDlhbPpl63ljoblj7LorrDlvZXmir3lsYkNCiAgICBoYW5kbGVIaXN0b3J5RHJhd2VyQ2xvc2UoKSB7DQogICAgICB0aGlzLmhpc3RvcnlEcmF3ZXJWaXNpYmxlID0gZmFsc2U7DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veWOhuWPsuiusOW9lQ0KICAgIGFzeW5jIGxvYWRDaGF0SGlzdG9yeShpc0FsbCkgew0KICAgICAgdHJ5IHsNCiAgICAgICAgY29uc3QgcmVzID0gYXdhaXQgZ2V0Q2hhdEhpc3RvcnkodGhpcy51c2VySWQsIGlzQWxsKTsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLmNoYXRIaXN0b3J5ID0gcmVzLmRhdGEgfHwgW107DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veWOhuWPsuiusOW9leWksei0pTonLCBlcnJvcik7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ+WKoOi9veWOhuWPsuiusOW9leWksei0pScpOw0KICAgICAgfQ0KICAgIH0sDQoNCiAgICAvLyDmoLzlvI/ljJbljoblj7LorrDlvZXml7bpl7QNCiAgICBmb3JtYXRIaXN0b3J5VGltZSh0aW1lc3RhbXApIHsNCiAgICAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh0aW1lc3RhbXApOw0KICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgaG91cjogJzItZGlnaXQnLA0KICAgICAgICBtaW51dGU6ICcyLWRpZ2l0JywNCiAgICAgICAgaG91cjEyOiBmYWxzZQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIC8vIOiOt+WPluWOhuWPsuiusOW9leaXpeacn+WIhue7hA0KICAgIGdldEhpc3RvcnlEYXRlKHRpbWVzdGFtcCkgew0KICAgICAgY29uc3QgZGF0ZSA9IG5ldyBEYXRlKHRpbWVzdGFtcCk7DQogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgICBjb25zdCB5ZXN0ZXJkYXkgPSBuZXcgRGF0ZSh0b2RheSk7DQogICAgICB5ZXN0ZXJkYXkuc2V0RGF0ZSh5ZXN0ZXJkYXkuZ2V0RGF0ZSgpIC0gMSk7DQogICAgICBjb25zdCB0d29EYXlzQWdvID0gbmV3IERhdGUodG9kYXkpOw0KICAgICAgdHdvRGF5c0Fnby5zZXREYXRlKHR3b0RheXNBZ28uZ2V0RGF0ZSgpIC0gMik7DQogICAgICBjb25zdCB0aHJlZURheXNBZ28gPSBuZXcgRGF0ZSh0b2RheSk7DQogICAgICB0aHJlZURheXNBZ28uc2V0RGF0ZSh0aHJlZURheXNBZ28uZ2V0RGF0ZSgpIC0gMyk7DQoNCiAgICAgIGlmIChkYXRlLnRvRGF0ZVN0cmluZygpID09PSB0b2RheS50b0RhdGVTdHJpbmcoKSkgew0KICAgICAgICByZXR1cm4gJ+S7iuWkqSc7DQogICAgICB9IGVsc2UgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHllc3RlcmRheS50b0RhdGVTdHJpbmcoKSkgew0KICAgICAgICByZXR1cm4gJ+aYqOWkqSc7DQogICAgICB9IGVsc2UgaWYgKGRhdGUudG9EYXRlU3RyaW5nKCkgPT09IHR3b0RheXNBZ28udG9EYXRlU3RyaW5nKCkpIHsNCiAgICAgICAgcmV0dXJuICfkuKTlpKnliY0nOw0KICAgICAgfSBlbHNlIGlmIChkYXRlLnRvRGF0ZVN0cmluZygpID09PSB0aHJlZURheXNBZ28udG9EYXRlU3RyaW5nKCkpIHsNCiAgICAgICAgcmV0dXJuICfkuInlpKnliY0nOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVEYXRlU3RyaW5nKCd6aC1DTicsIHsNCiAgICAgICAgICB5ZWFyOiAnbnVtZXJpYycsDQogICAgICAgICAgbW9udGg6ICdsb25nJywNCiAgICAgICAgICBkYXk6ICdudW1lcmljJw0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5Yqg6L295Y6G5Y+y6K6w5b2V6aG5DQogICAgbG9hZEhpc3RvcnlJdGVtKGl0ZW0pIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IGhpc3RvcnlEYXRhID0gSlNPTi5wYXJzZShpdGVtLmRhdGEpOw0KICAgICAgICAvLyDmgaLlpI1BSemAieaLqemFjee9rg0KICAgICAgICB0aGlzLmFpTGlzdCA9IGhpc3RvcnlEYXRhLmFpTGlzdCB8fCB0aGlzLmFpTGlzdDsNCiAgICAgICAgLy8g5oGi5aSN5o+Q56S66K+N6L6T5YWlDQogICAgICAgIHRoaXMucHJvbXB0SW5wdXQgPSBoaXN0b3J5RGF0YS5wcm9tcHRJbnB1dCB8fCAnJzsNCiAgICAgICAgLy8g5oGi5aSN5Lu75Yqh5rWB56iLDQogICAgICAgIHRoaXMuZW5hYmxlZEFJcyA9IGhpc3RvcnlEYXRhLmVuYWJsZWRBSXMgfHwgW107DQogICAgICAgIC8vIOaBouWkjeS4u+acuuWPr+inhuWMlg0KICAgICAgICB0aGlzLnNjcmVlbnNob3RzID0gaGlzdG9yeURhdGEuc2NyZWVuc2hvdHMgfHwgW107DQogICAgICAgIC8vIOaBouWkjeaJp+ihjOe7k+aenA0KICAgICAgICB0aGlzLnJlc3VsdHMgPSBoaXN0b3J5RGF0YS5yZXN1bHRzIHx8IFtdOw0KICAgICAgICAvLyDmgaLlpI1jaGF0SWQNCiAgICAgICAgdGhpcy5jaGF0SWQgPSBpdGVtLmNoYXRJZCB8fCB0aGlzLmNoYXRJZDsNCiAgICAgICAgdGhpcy51c2VySW5mb1JlcS50b25lQ2hhdElkID0gaXRlbS50b25lQ2hhdElkIHx8ICcnOw0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLnliRHNDaGF0SWQgPSBpdGVtLnliRHNDaGF0SWQgfHwgJyc7DQogICAgICAgIHRoaXMudXNlckluZm9SZXEuZGJDaGF0SWQgPSBpdGVtLmRiQ2hhdElkIHx8ICcnOw0KICAgICAgICB0aGlzLnVzZXJJbmZvUmVxLmlzTmV3Q2hhdCA9IGZhbHNlOw0KDQogICAgICAgIC8vIOWxleW8gOebuOWFs+WMuuWfnw0KICAgICAgICB0aGlzLmFjdGl2ZUNvbGxhcHNlcyA9IFsnYWktc2VsZWN0aW9uJywgJ3Byb21wdC1pbnB1dCddOw0KICAgICAgICB0aGlzLnRhc2tTdGFydGVkID0gdHJ1ZTsNCg0KICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+WOhuWPsuiusOW9leWKoOi9veaIkOWKnycpOw0KICAgICAgICB0aGlzLmhpc3RvcnlEcmF3ZXJWaXNpYmxlID0gZmFsc2U7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfliqDovb3ljoblj7LorrDlvZXlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfliqDovb3ljoblj7LorrDlvZXlpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5L+d5a2Y5Y6G5Y+y6K6w5b2VDQogICAgYXN5bmMgc2F2ZUhpc3RvcnkoKSB7DQogICAgICAvLyBpZiAoIXRoaXMudGFza1N0YXJ0ZWQgfHwgdGhpcy5lbmFibGVkQUlzLnNvbWUoYWkgPT4gYWkuc3RhdHVzID09PSAncnVubmluZycpKSB7DQogICAgICAvLyAgIHJldHVybjsNCiAgICAgIC8vIH0NCg0KICAgICAgY29uc3QgaGlzdG9yeURhdGEgPSB7DQogICAgICAgIGFpTGlzdDogdGhpcy5haUxpc3QsDQogICAgICAgIHByb21wdElucHV0OiB0aGlzLnByb21wdElucHV0LA0KICAgICAgICBlbmFibGVkQUlzOiB0aGlzLmVuYWJsZWRBSXMsDQogICAgICAgIHNjcmVlbnNob3RzOiB0aGlzLnNjcmVlbnNob3RzLA0KICAgICAgICByZXN1bHRzOiB0aGlzLnJlc3VsdHMsDQogICAgICAgIGNoYXRJZDogdGhpcy5jaGF0SWQsDQogICAgICAgIHRvbmVDaGF0SWQ6IHRoaXMudXNlckluZm9SZXEudG9uZUNoYXRJZCwNCiAgICAgICAgeWJEc0NoYXRJZDogdGhpcy51c2VySW5mb1JlcS55YkRzQ2hhdElkLA0KICAgICAgICBkYkNoYXRJZDogdGhpcy51c2VySW5mb1JlcS5kYkNoYXRJZA0KICAgICAgfTsNCg0KICAgICAgdHJ5IHsNCiAgICAgICAgYXdhaXQgc2F2ZVVzZXJDaGF0RGF0YSh7DQogICAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgICB1c2VyUHJvbXB0OiB0aGlzLnByb21wdElucHV0LA0KICAgICAgICAgIGRhdGE6IEpTT04uc3RyaW5naWZ5KGhpc3RvcnlEYXRhKSwNCiAgICAgICAgICBjaGF0SWQ6IHRoaXMuY2hhdElkLA0KICAgICAgICAgIHRvbmVDaGF0SWQ6IHRoaXMudXNlckluZm9SZXEudG9uZUNoYXRJZCwNCiAgICAgICAgICB5YkRzQ2hhdElkOiB0aGlzLnVzZXJJbmZvUmVxLnliRHNDaGF0SWQsDQogICAgICAgICAgZGJDaGF0SWQ6IHRoaXMudXNlckluZm9SZXEuZGJDaGF0SWQNCiAgICAgICAgfSk7DQogICAgICB9IGNhdGNoIChlcnJvcikgew0KICAgICAgICBjb25zb2xlLmVycm9yKCfkv53lrZjljoblj7LorrDlvZXlpLHotKU6JywgZXJyb3IpOw0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCfkv53lrZjljoblj7LorrDlvZXlpLHotKUnKTsNCiAgICAgIH0NCiAgICB9LA0KDQogICAgLy8g5L+u5pS55oqY5Y+g5YiH5o2i5pa55rOVDQogICAgdG9nZ2xlSGlzdG9yeUV4cGFuc2lvbihpdGVtKSB7DQogICAgICB0aGlzLiRzZXQodGhpcy5leHBhbmRlZEhpc3RvcnlJdGVtcywgaXRlbS5jaGF0SWQsICF0aGlzLmV4cGFuZGVkSGlzdG9yeUl0ZW1zW2l0ZW0uY2hhdElkXSk7DQogICAgfSwNCg0KICAgIC8vIOWIm+W7uuaWsOWvueivnQ0KICAgIGNyZWF0ZU5ld0NoYXQoKSB7DQogICAgICAvLyDph43nva7miYDmnInmlbDmja4NCiAgICAgIHRoaXMuY2hhdElkID0gdXVpZHY0KCk7DQogICAgICB0aGlzLmlzTmV3Q2hhdCA9IHRydWU7DQogICAgICB0aGlzLnByb21wdElucHV0ID0gJyc7DQogICAgICB0aGlzLnRhc2tTdGFydGVkID0gZmFsc2U7DQogICAgICB0aGlzLnNjcmVlbnNob3RzID0gW107DQogICAgICB0aGlzLnJlc3VsdHMgPSBbXTsNCiAgICAgIHRoaXMuZW5hYmxlZEFJcyA9IFtdOw0KICAgICAgdGhpcy51c2VySW5mb1JlcSA9IHsNCiAgICAgICAgdXNlclByb21wdDogJycsDQogICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQsDQogICAgICAgIHRhc2tJZDogJycsDQogICAgICAgIHJvbGVzOiAnJywNCiAgICAgICAgdG9uZUNoYXRJZDogJycsDQogICAgICAgIHliRHNDaGF0SWQ6ICcnLA0KICAgICAgICBkYkNoYXRJZDogJycsDQogICAgICAgIGlzTmV3Q2hhdDogdHJ1ZQ0KICAgICAgfTsNCiAgICAgIC8vIOmHjee9rkFJ5YiX6KGo5Li65Yid5aeL54q25oCBDQogICAgICB0aGlzLmFpTGlzdCA9IFsNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICdUdXJib1NA5YWD5ZmoJywNCiAgICAgICAgICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZycpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogW10sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAnVHVyYm9T6ZW/5paH54mIQOWFg+WZqCcsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAgIHNlbGVjdGVkQ2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICAvLyB7DQogICAgICAgIC8vICAgbmFtZTogJ01pbmlNYXhA5YWD5ZmoJywNCiAgICAgICAgLy8gICBhdmF0YXI6IHJlcXVpcmUoJy4uLy4uLy4uL2Fzc2V0cy9haS95dWFuYmFvLnBuZycpLA0KICAgICAgICAvLyAgIGNhcGFiaWxpdGllczogW10sDQogICAgICAgIC8vICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFtdLA0KICAgICAgICAvLyAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgIC8vICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgIC8vICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgLy8gICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIC8vIH0sDQogICAgICAgIC8vIHsNCiAgICAgICAgLy8gICBuYW1lOiAnS0lNSUDlhYPlmagnLA0KICAgICAgICAvLyAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgIC8vICAgY2FwYWJpbGl0aWVzOiBbXSwNCiAgICAgICAgLy8gICBzZWxlY3RlZENhcGFiaWxpdGllczogW10sDQogICAgICAgIC8vICAgZW5hYmxlZDogdHJ1ZSwNCiAgICAgICAgLy8gICBzdGF0dXM6ICdpZGxlJywNCiAgICAgICAgLy8gICBwcm9ncmVzc0xvZ3M6IFtdLA0KICAgICAgICAvLyAgIGlzRXhwYW5kZWQ6IHRydWUNCiAgICAgICAgLy8gfSwNCiAgICAgICAgew0KICAgICAgICAgIG5hbWU6ICfohb7orq/lhYPlrp1UMScsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkveXVhbmJhby5wbmcnKSwNCiAgICAgICAgICBjYXBhYmlsaXRpZXM6IFsNCiAgICAgICAgICAgIHsgbGFiZWw6ICfmt7HluqbmgJ3ogIMnLCB2YWx1ZTogJ2RlZXBfdGhpbmtpbmcnIH0sDQogICAgICAgICAgICB7IGxhYmVsOiAn6IGU572R5pCc57SiJywgdmFsdWU6ICd3ZWJfc2VhcmNoJyB9DQogICAgICAgICAgXSwNCiAgICAgICAgICBzZWxlY3RlZENhcGFiaWxpdGllczogWydkZWVwX3RoaW5raW5nJywnd2ViX3NlYXJjaCddLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBuYW1lOiAn6IW+6K6v5YWD5a6dRFMnLA0KICAgICAgICAgIGF2YXRhcjogcmVxdWlyZSgnLi4vLi4vLi4vYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgICAgY2FwYWJpbGl0aWVzOiBbDQogICAgICAgICAgICB7IGxhYmVsOiAn5rex5bqm5oCd6ICDJywgdmFsdWU6ICdkZWVwX3RoaW5raW5nJyB9LA0KICAgICAgICAgICAgeyBsYWJlbDogJ+iBlOe9keaQnOe0oicsIHZhbHVlOiAnd2ViX3NlYXJjaCcgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFsnZGVlcF90aGlua2luZycsJ3dlYl9zZWFyY2gnXSwNCiAgICAgICAgICBlbmFibGVkOiB0cnVlLA0KICAgICAgICAgIHN0YXR1czogJ2lkbGUnLA0KICAgICAgICAgIHByb2dyZXNzTG9nczogW10sDQogICAgICAgICAgaXNFeHBhbmRlZDogdHJ1ZQ0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgICAgbmFtZTogJ+ixhuWMhScsDQogICAgICAgICAgYXZhdGFyOiByZXF1aXJlKCcuLi8uLi8uLi9hc3NldHMvYWkv6LGG5YyFLnBuZycpLA0KICAgICAgICAgIGNhcGFiaWxpdGllczogWw0KICAgICAgICAgICAgeyBsYWJlbDogJ+a3seW6puaAneiAgycsIHZhbHVlOiAnZGVlcF90aGlua2luZycgfQ0KICAgICAgICAgIF0sDQogICAgICAgICAgc2VsZWN0ZWRDYXBhYmlsaXRpZXM6IFsnZGVlcF90aGlua2luZyddLA0KICAgICAgICAgIGVuYWJsZWQ6IHRydWUsDQogICAgICAgICAgc3RhdHVzOiAnaWRsZScsDQogICAgICAgICAgcHJvZ3Jlc3NMb2dzOiBbXSwNCiAgICAgICAgICBpc0V4cGFuZGVkOiB0cnVlDQogICAgICAgIH0NCiAgICAgIF07DQogICAgICAvLyDlsZXlvIDnm7jlhbPljLrln58NCiAgICAgIHRoaXMuYWN0aXZlQ29sbGFwc2VzID0gWydhaS1zZWxlY3Rpb24nLCAncHJvbXB0LWlucHV0J107DQoNCiAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5bey5Yib5bu65paw5a+56K+dJyk7DQogICAgfSwNCg0KICAgIC8vIOWKoOi9veS4iuasoeS8muivnQ0KICAgIGFzeW5jIGxvYWRMYXN0Q2hhdCgpIHsNCiAgICAgIHRyeSB7DQogICAgICAgIGNvbnN0IHJlcyA9IGF3YWl0IGdldENoYXRIaXN0b3J5KHRoaXMudXNlcklkLDApOw0KICAgICAgICBpZiAocmVzLmNvZGUgPT09IDIwMCAmJiByZXMuZGF0YSAmJiByZXMuZGF0YS5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g6I635Y+W5pyA5paw55qE5Lya6K+d6K6w5b2VDQogICAgICAgICAgY29uc3QgbGFzdENoYXQgPSByZXMuZGF0YVswXTsNCiAgICAgICAgICB0aGlzLmxvYWRIaXN0b3J5SXRlbShsYXN0Q2hhdCk7DQogICAgICAgIH0NCiAgICAgIH0gY2F0Y2ggKGVycm9yKSB7DQogICAgICAgIGNvbnNvbGUuZXJyb3IoJ+WKoOi9veS4iuasoeS8muivneWksei0pTonLCBlcnJvcik7DQogICAgICB9DQogICAgfSwNCg0KICAgIC8vIOWIpOaWreaYr+WQpuS4uuWbvueJh+aWh+S7tg0KICAgIGlzSW1hZ2VGaWxlKHVybCkgew0KICAgICAgaWYgKCF1cmwpIHJldHVybiBmYWxzZTsNCiAgICAgIGNvbnN0IGltYWdlRXh0ZW5zaW9ucyA9IFsnLmpwZycsICcuanBlZycsICcucG5nJywgJy5naWYnLCAnLmJtcCcsICcud2VicCcsICcuc3ZnJ107DQogICAgICBjb25zdCB1cmxMb3dlciA9IHVybC50b0xvd2VyQ2FzZSgpOw0KICAgICAgcmV0dXJuIGltYWdlRXh0ZW5zaW9ucy5zb21lKGV4dCA9PiB1cmxMb3dlci5pbmNsdWRlcyhleHQpKTsNCiAgICB9LA0KDQogICAgLy8g5Yik5pat5piv5ZCm5Li6UERG5paH5Lu2DQogICAgaXNQZGZGaWxlKHVybCkgew0KICAgICAgaWYgKCF1cmwpIHJldHVybiBmYWxzZTsNCiAgICAgIHJldHVybiB1cmwudG9Mb3dlckNhc2UoKS5pbmNsdWRlcygnLnBkZicpOw0KICAgIH0sDQoNCiAgICAvLyDmoLnmja5BSeWQjeensOiOt+WPluWbvueJh+agt+W8jw0KICAgIGdldEltYWdlU3R5bGUoYWlOYW1lKSB7DQogICAgICBjb25zdCB3aWR0aE1hcCA9IHsNCiAgICAgICAgJ1R1cmJvU0DlhYPlmagnOiAnNzAwcHgnLA0KICAgICAgICAn6IW+6K6v5YWD5a6dRFMnOiAnNzAwcHgnLA0KICAgICAgICAnVHVyYm9T6ZW/5paH54mIQOWFg+WZqCc6ICc3MDBweCcsDQogICAgICAgICfohb7orq/lhYPlrp1UMSc6ICc3MDBweCcsDQogICAgICAgICfosYbljIUnOiAnNTYwcHgnDQogICAgICB9Ow0KDQogICAgICBjb25zdCB3aWR0aCA9IHdpZHRoTWFwW2FpTmFtZV0gfHwgJzU2MHB4JzsgLy8g6buY6K6k5a695bqmDQoNCiAgICAgIHJldHVybiB7DQogICAgICAgIHdpZHRoOiB3aWR0aCwNCiAgICAgICAgaGVpZ2h0OiAnYXV0bycNCiAgICAgIH07DQogICAgfSwNCg0KICAgIC8vIOaKlemAkuWIsOWFrOS8l+WPtw0KICAgIGhhbmRsZVB1c2hUb1dlY2hhdChyZXN1bHQpIHsNCiAgICAgIGlmICh0aGlzLnB1c2hpbmdUb1dlY2hhdCkgcmV0dXJuOyAvLyDpmLLmraLph43lpI3ngrnlh7sNCg0KICAgICAgdGhpcy5wdXNoaW5nVG9XZWNoYXQgPSB0cnVlOyAvLyDlvIDlp4tsb2FkaW5nDQogICAgICB0aGlzLnB1c2hPZmZpY2VOdW0gKz0gMTsgLy8g6YCS5aKe57yW5Y+3DQoNCiAgICAgIGNvbnN0IHBhcmFtcyA9IHsNCiAgICAgICAgY29udGVudFRleHQ6IHJlc3VsdC5jb250ZW50LA0KICAgICAgICBzaGFyZVVybDogcmVzdWx0LnNoYXJlVXJsLA0KICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICBudW06IHRoaXMucHVzaE9mZmljZU51bSwNCiAgICAgICAgYWlOYW1lOiByZXN1bHQuYWlOYW1lDQogICAgICB9Ow0KDQogICAgICBwdXNoQXV0b09mZmljZShwYXJhbXMpLnRoZW4ocmVzID0+IHsNCiAgICAgICAgaWYgKHJlcy5jb2RlID09PSAyMDApIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoJ+aKlemAkuWIsOWFrOS8l+WPt+aIkOWKn++8gScpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IocmVzLm1zZyB8fCAn5oqV6YCS5aSx6LSl77yM6K+36YeN6K+VJyk7DQogICAgICAgIH0NCiAgICAgIH0pLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgY29uc29sZS5lcnJvcign5oqV6YCS5Yiw5YWs5LyX5Y+35aSx6LSlOicsIGVycm9yKTsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcign5oqV6YCS5aSx6LSl77yM6K+36YeN6K+VJyk7DQogICAgICB9KS5maW5hbGx5KCgpID0+IHsNCiAgICAgICAgdGhpcy5wdXNoaW5nVG9XZWNoYXQgPSBmYWxzZTsgLy8g57uT5p2fbG9hZGluZw0KICAgICAgfSk7DQogICAgfSwNCg0KDQogIH0NCn07DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoQA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA", "file": "index.vue", "sourceRoot": "src/views/wechat/chrome", "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\">\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img :src=\"require('../../../assets/ai/celan.png')\" alt=\"历史记录\" class=\"history-icon\">\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer title=\"历史会话记录\" :visible.sync=\"historyDrawerVisible\" direction=\"rtl\" size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\">\r\n      <div class=\"history-content\">\r\n        <div v-for=\"(group, date) in groupedHistory\" :key=\"date\" class=\"history-group\">\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div v-for=\"(item, index) in group\" :key=\"index\" class=\"history-item\">\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i :class=\"['el-icon-arrow-right', {'is-expanded': item.isExpanded}]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">{{ formatHistoryTime(item.createTime) }}</div>\r\n              </div>\r\n              <div v-if=\"item.children && item.children.length > 0 && item.isExpanded\" class=\"history-children\">\r\n                <div v-for=\"(child, childIndex) in item.children\" :key=\"childIndex\" class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\">\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">{{ formatHistoryTime(child.createTime) }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card v-for=\"(ai, index) in aiList\" :key=\"index\" class=\"ai-card\" shadow=\"hover\">\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\">\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch v-model=\"ai.enabled\" active-color=\"#13ce66\" inactive-color=\"#ff4949\">\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <div class=\"button-capability-group\">\r\n                    <el-button v-for=\"capability in ai.capabilities\" :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\" :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\" class=\"capability-button\">\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input type=\"textarea\" :rows=\"5\" placeholder=\"请输入提示词，支持Markdown格式\" v-model=\"promptInput\" resize=\"none\"\r\n              class=\"prompt-input\">\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button type=\"primary\" @click=\"sendPrompt\" :disabled=\"!canSend\" class=\"send-button\">\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div v-for=\"(ai, index) in enabledAIs\" :key=\"index\" class=\"task-item\">\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i :class=\"['el-icon-arrow-right', {'is-expanded': ai.isExpanded}]\"></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{ getStatusText(ai.status) }}</span>\r\n                      <i :class=\"getStatusIcon(ai.status)\" class=\"status-icon\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div class=\"progress-timeline\" v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\">\r\n                    <div class=\"timeline-scroll\">\r\n                      <div v-for=\"(log, logIndex) in ai.progressLogs\" :key=\"logIndex\" class=\"progress-item\" :class=\"{\r\n                             'completed': log.isCompleted || logIndex > 0,\r\n                             'current': !log.isCompleted && logIndex === 0\r\n                           }\">\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div class=\"progress-line\" v-if=\"logIndex < ai.progressLogs.length - 1\"></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch v-model=\"autoPlay\" active-text=\"自动轮播\" inactive-text=\"手动切换\">\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"700px\">\r\n                  <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n                    <img :src=\"screenshot\" alt=\"执行截图\" class=\"screenshot-image\" @click=\"showLargeImage(screenshot)\">\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" :name=\"'result-' + index\">\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-link\" @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\">\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-s-promotion\" @click=\"handlePushToWechat(result)\"\r\n                    class=\"push-wechat-btn\" :loading=\"pushingToWechat\" :disabled=\"pushingToWechat\">\r\n                    投递到公众号\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img v-if=\"isImageFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" alt=\"分享图片\" class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\">\r\n                <!-- 渲染PDF -->\r\n                <iframe v-else-if=\"isPdfFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" class=\"share-pdf\"\r\n                  frameborder=\"0\">\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-document\" @click=\"openShareUrl(result.shareImgUrl)\">\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div v-else class=\"markdown-content\" v-html=\"renderMarkdown(result.content)\"></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"small\" type=\"primary\" @click=\"copyResult(result.content)\">复制（纯文本）</el-button>\r\n                <el-button size=\"small\" type=\"success\" @click=\"exportResult(result)\">导出（MD文件）</el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog :visible.sync=\"showImageDialog\" width=\"90%\" :show-close=\"true\" :modal=\"true\" center class=\"image-dialog\"\r\n      :append-to-body=\"true\" @close=\"closeLargeImage\">\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\" class=\"single-image-container\">\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\">\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel v-else :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"80vh\">\r\n          <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\">\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog title=\"智能评分\" :visible.sync=\"scoreDialogVisible\" width=\"60%\" height=\"65%\" :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\">\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input type=\"textarea\" :rows=\"10\" placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\" v-model=\"scorePrompt\"\r\n            resize=\"none\" class=\"score-prompt-input\">\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" class=\"result-checkbox\">\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n\r\n\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {marked} from 'marked';\r\nimport {message, saveUserChatData, getChatHistory, pushAutoOffice} from \"@/api/wechat/aigc\";\r\nimport {\r\n\t\tv4 as uuidv4\r\n\t} from 'uuid';\r\nimport websocketClient from '@/utils/websocket';\r\nimport store from '@/store';\r\nimport TurndownService from 'turndown';\r\n\r\nexport default {\r\n  name: 'AIManagementPlatform',\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: '',\r\n        userId: '',\r\n        corpId: '',\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: '',\r\n        params: {}\r\n      },\r\n      aiList: [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: '搜狗搜索@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '文心一言',\r\n          avatar: require('../../../assets/ai/wenxin.png'),\r\n          capabilities: [\r\n            { label: '文心4.5 Turbo', value: 'wx-4.5' },\r\n            { label: '文心X1 Turbo', value: 'wx-x1' }\r\n          ],\r\n          selectedCapabilities: ['wx-4.5'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: '',\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: 'result-0',\r\n      activeCollapses: ['ai-selection', 'prompt-input'], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: '',\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: 'atx',\r\n        codeBlockStyle: 'fenced',\r\n        emDelimiter: '*'\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇公众号章，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return this.promptInput.trim().length > 0 && this.aiList.some(ai => ai.enabled);\r\n    },\r\n    canScore() {\r\n      return this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach(item => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach(chatGroup => {\r\n        // 按时间排序\r\n        chatGroup.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map(child => ({\r\n            ...child,\r\n            isParent: false\r\n          }))\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    }\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots =[];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = '';\r\n\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter(ai => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach(ai => {\r\n        this.$set(ai, 'status', 'running');\r\n      });\r\n\r\n      this.enabledAIs.forEach(ai => {\r\n        if(ai.name === '腾讯元宝T1'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === '腾讯元宝DS'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === 'TurboS@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-trubos-agent,';\r\n        }\r\n        if(ai.name === 'TurboS长文版@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-turbos-large-agent,';\r\n        }\r\n        if(ai.name === 'MiniMax@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-mini-max-agent,';\r\n        }\r\n        // if(ai.name === '搜狗搜索@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-sogou-agent,';\r\n        // }\r\n        // if(ai.name === 'KIMI@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-lwss-agent,';\r\n        // }\r\n        if(ai.name === '豆包'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db,';\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db-sdsk,';\r\n          }\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq)\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\"\r\n      this.jsonRpcReqest.params = this.userInfoReq\r\n      this.message(this.jsonRpcReqest)\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then(res => {\r\n        if (res.code == 201) {\r\n          uni.showToast({\r\n            title: res.messages,\r\n            icon: 'none',\r\n            duration: 1500,\r\n          });\r\n        }\r\n      })\r\n\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log('切换前:', ai.selectedCapabilities);\r\n      if (index === -1) {\r\n        // 如果不存在，则添加\r\n        this.$set(ai.selectedCapabilities, ai.selectedCapabilities.length, capabilityValue);\r\n      } else {\r\n        // 如果已存在，则移除\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        newCapabilities.splice(index, 1);\r\n        this.$set(ai, 'selectedCapabilities', newCapabilities);\r\n      }\r\n      console.log('切换后:', ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 'idle': return '等待中';\r\n        case 'running': return '正在执行';\r\n        case 'completed': return '已完成';\r\n        case 'failed': return '执行失败';\r\n        default: return '未知状态';\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case 'idle': return 'el-icon-time';\r\n        case 'running': return 'el-icon-loading';\r\n        case 'completed': return 'el-icon-check success-icon';\r\n        case 'failed': return 'el-icon-close error-icon';\r\n        default: return 'el-icon-question';\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement('div');\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || '';\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement('textarea');\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textarea);\r\n      this.$message.success('已复制纯文本到剪贴板');\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: 'text/markdown' });\r\n      const link = document.createElement('a');\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date().toISOString().slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success('已导出Markdown文件');\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, '_blank');\r\n      } else {\r\n        this.$message.warning('暂无原链接');\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector('.image-dialog .el-carousel');\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = '';\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log('WebSocket URL:', process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case 'open':\r\n            // this.$message.success('');\r\n            break;\r\n          case 'message':\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case 'close':\r\n            this.$message.warning('WebSocket连接已关闭');\r\n            break;\r\n          case 'error':\r\n            this.$message.error('WebSocket连接错误');\r\n            break;\r\n          case 'reconnect_failed':\r\n            this.$message.error('WebSocket重连失败，请刷新页面重试');\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === 'RETURN_YBT1_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_YBDS_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_DB_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_LOG' && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n        if (targetAI) {\r\n          // 将新进度添加到数组开头\r\n          targetAI.progressLogs.unshift({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            isCompleted: false\r\n          });\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理截图消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_IMG' && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n              // 处理智能评分结果\r\n      if (dataObj.type === 'RETURN_WKPF_RES') {\r\n        const wkpfAI = this.enabledAIs.find(ai => ai.name === '智能评分');\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, 'status', 'completed');\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: '智能评分',\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case 'RETURN_YBT1_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝T1');\r\n          break;\r\n        case 'RETURN_YBDS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝DS');\r\n          break;\r\n        case 'RETURN_DB_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '豆包');\r\n          break;\r\n        case 'RETURN_TURBOS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS@元器');\r\n          break;\r\n        case 'RETURN_TURBOS_LARGE_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS长文版@元器');\r\n          break;\r\n        // case 'RETURN_MINI_MAX_RES':\r\n        //   targetAI = this.enabledAIs.find(ai => ai.name === 'MiniMax@元器');\r\n        //   break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, 'status', 'completed');\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], 'isCompleted', true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(r => r.aiName === targetAI.name);\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n      // 检查是否所有任务都已完成\r\n      // const allCompleted = this.enabledAIs.every(ai =>\r\n      //   ai.status === 'completed' || ai.status === 'failed'\r\n      // );\r\n\r\n      // if (allCompleted) {\r\n      //\r\n      // }\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error('WebSocket未连接');\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, 'isExpanded', !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter(result => this.selectedResults.includes(result.aiName))\r\n        .map(result => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join('\\n');\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: 'AI评分',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: 'zj-db-sdsk' // 默认使用豆包进行评分\r\n        }\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest)\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: '智能评分',\r\n        avatar: require('../../../assets/ai/yuanbao.png'),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: 'running',\r\n        progressLogs: [\r\n          {\r\n            content: '智能评分任务已提交，正在评分...',\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: '智能评分'\r\n          }\r\n        ],\r\n        isExpanded: true\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能评分');\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success('评分请求已发送，请等待结果');\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return '今天';\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return '昨天';\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return '两天前';\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return '三天前';\r\n      } else {\r\n        return date.toLocaleDateString('zh-CN', {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric'\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || '';\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || '';\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || '';\r\n        this.userInfoReq.dbChatId = item.dbChatId || '';\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success('历史记录加载成功');\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId\r\n        });\r\n      } catch (error) {\r\n        console.error('保存历史记录失败:', error);\r\n        this.$message.error('保存历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(this.expandedHistoryItems, item.chatId, !this.expandedHistoryItems[item.chatId]);\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = '';\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: '',\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n\r\n      this.$message.success('已创建新对话');\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId,0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载上次会话失败:', error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some(ext => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes('.pdf');\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        'TurboS@元器': '700px',\r\n        '腾讯元宝DS': '700px',\r\n        'TurboS长文版@元器': '700px',\r\n        '腾讯元宝T1': '700px',\r\n        '豆包': '560px'\r\n      };\r\n\r\n      const width = widthMap[aiName] || '560px'; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: 'auto'\r\n      };\r\n    },\r\n\r\n    // 投递到公众号\r\n    handlePushToWechat(result) {\r\n      if (this.pushingToWechat) return; // 防止重复点击\r\n\r\n      this.pushingToWechat = true; // 开始loading\r\n      this.pushOfficeNum += 1; // 递增编号\r\n\r\n      const params = {\r\n        contentText: result.content,\r\n        shareUrl: result.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: result.aiName\r\n      };\r\n\r\n      pushAutoOffice(params).then(res => {\r\n        if (res.code === 200) {\r\n          this.$message.success('投递到公众号成功！');\r\n        } else {\r\n          this.$message.error(res.msg || '投递失败，请重试');\r\n        }\r\n      }).catch(error => {\r\n        console.error('投递到公众号失败:', error);\r\n        this.$message.error('投递失败，请重试');\r\n      }).finally(() => {\r\n        this.pushingToWechat = false; // 结束loading\r\n      });\r\n    },\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding:20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card, .screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn, .push-wechat-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409EFF;\r\n  border-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409EFF;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n</style>\r\n"]}]}