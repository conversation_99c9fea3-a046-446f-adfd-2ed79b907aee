{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\wechat\\chrome\\index.vue", "mtime": 1751901861895}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\babel.config.js", "mtime": 1751782516642}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751784291203}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_marked", "require", "_aigc", "_uuid", "_websocket", "_interopRequireDefault", "_store", "_turndown", "name", "data", "userId", "store", "state", "user", "id", "corpId", "corp_id", "chatId", "uuidv4", "expandedHistoryItems", "userInfoReq", "userPrompt", "taskId", "roles", "toneChatId", "ybDsChatId", "dbChatId", "isNewChat", "jsonRpcReqest", "jsonrpc", "method", "params", "aiList", "avatar", "capabilities", "selectedCapabilities", "enabled", "status", "progressLogs", "isExpanded", "label", "value", "promptInput", "taskStarted", "autoPlay", "screenshots", "results", "activeResultTab", "activeCollapses", "showImageDialog", "currentLargeImage", "enabledAIs", "turndownService", "TurndownService", "headingStyle", "codeBlockStyle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "scoreDialogVisible", "selectedResults", "scorePrompt", "historyDrawerVisible", "chatHistory", "pushOfficeNum", "pushingToWechat", "computed", "canSend", "trim", "length", "some", "ai", "canScore", "groupedHistory", "_this", "groups", "chatGroups", "for<PERSON>ach", "item", "push", "Object", "values", "chatGroup", "sort", "a", "b", "Date", "createTime", "parentItem", "date", "getHistoryDate", "_objectSpread2", "default", "isParent", "children", "slice", "map", "child", "created", "console", "log", "initWebSocket", "loadChatHistory", "loadLastChat", "methods", "sendPrompt", "_this2", "filter", "$set", "includes", "message", "then", "res", "code", "uni", "showToast", "title", "messages", "icon", "duration", "toggleCapability", "capabilityValue", "index", "indexOf", "newCapabilities", "_toConsumableArray2", "splice", "$forceUpdate", "getStatusText", "getStatusIcon", "renderMarkdown", "text", "marked", "htmlToText", "html", "tempDiv", "document", "createElement", "innerHTML", "textContent", "innerText", "htmlToMarkdown", "turndown", "copyResult", "content", "plainText", "textarea", "body", "append<PERSON><PERSON><PERSON>", "select", "execCommand", "<PERSON><PERSON><PERSON><PERSON>", "$message", "success", "exportResult", "result", "markdown", "blob", "Blob", "type", "link", "href", "URL", "createObjectURL", "download", "concat", "aiName", "toISOString", "click", "revokeObjectURL", "openShareUrl", "shareUrl", "window", "open", "warning", "showLargeImage", "imageUrl", "_this3", "currentIndex", "$nextTick", "carousel", "$el", "querySelector", "__vue__", "setActiveItem", "closeLargeImage", "_this4", "wsUrl", "process", "env", "VUE_APP_WS_API", "websocketClient", "connect", "event", "handleWebSocketMessage", "error", "datastr", "dataObj", "JSON", "parse", "targetAI", "find", "unshift", "timestamp", "isCompleted", "url", "wkpfAI", "draftContent", "shareImgUrl", "saveHistory", "resultIndex", "findIndex", "r", "closeWebSocket", "close", "sendMessage", "_this5", "send", "scrollToBottom", "toggleAIExpansion", "formatTime", "toLocaleTimeString", "hour", "minute", "second", "hour12", "showScoreDialog", "handleScore", "_this6", "selectedContents", "plainContent", "join", "fullPrompt", "scoreRequest", "existIndex", "wkpf", "showHistoryDrawer", "handleHistoryDrawerClose", "isAll", "_this7", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "_t", "w", "_context", "n", "p", "getChatHistory", "v", "formatHistoryTime", "today", "yesterday", "setDate", "getDate", "twoDaysAgo", "threeDaysAgo", "toDateString", "toLocaleDateString", "year", "month", "day", "loadHistoryItem", "historyData", "_this8", "_callee2", "_t2", "_context2", "saveUserChatData", "stringify", "toggleHistoryExpansion", "createNewChat", "_this9", "_callee3", "lastChat", "_t3", "_context3", "isImageFile", "imageExtensions", "url<PERSON><PERSON><PERSON>", "toLowerCase", "ext", "isPdfFile", "getImageStyle", "widthMap", "width", "height", "handlePushToWechat", "_this0", "contentText", "num", "pushAutoOffice", "msg", "catch", "finally"], "sources": ["src/views/wechat/chrome/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"ai-management-platform\">\r\n    <!-- 顶部导航区 -->\r\n    <div class=\"top-nav\">\r\n      <div class=\"logo-area\">\r\n        <img src=\"../../../assets/ai/logo.png\" alt=\"Logo\" class=\"logo\">\r\n        <h1 class=\"platform-title\">主机</h1>\r\n      </div>\r\n      <div class=\"nav-buttons\">\r\n        <el-button type=\"primary\" size=\"small\" @click=\"createNewChat\">\r\n          <i class=\"el-icon-plus\"></i>\r\n          创建新对话\r\n        </el-button>\r\n        <div class=\"history-button\">\r\n          <el-button type=\"text\" @click=\"showHistoryDrawer\">\r\n            <img :src=\"require('../../../assets/ai/celan.png')\" alt=\"历史记录\" class=\"history-icon\">\r\n          </el-button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 历史记录抽屉 -->\r\n    <el-drawer title=\"历史会话记录\" :visible.sync=\"historyDrawerVisible\" direction=\"rtl\" size=\"30%\"\r\n      :before-close=\"handleHistoryDrawerClose\">\r\n      <div class=\"history-content\">\r\n        <div v-for=\"(group, date) in groupedHistory\" :key=\"date\" class=\"history-group\">\r\n          <div class=\"history-date\">{{ date }}</div>\r\n          <div class=\"history-list\">\r\n            <div v-for=\"(item, index) in group\" :key=\"index\" class=\"history-item\">\r\n              <div class=\"history-parent\" @click=\"loadHistoryItem(item)\">\r\n                <div class=\"history-header\">\r\n                  <i :class=\"['el-icon-arrow-right', {'is-expanded': item.isExpanded}]\"\r\n                    @click.stop=\"toggleHistoryExpansion(item)\"></i>\r\n                  <div class=\"history-prompt\">{{ item.userPrompt }}</div>\r\n                </div>\r\n                <div class=\"history-time\">{{ formatHistoryTime(item.createTime) }}</div>\r\n              </div>\r\n              <div v-if=\"item.children && item.children.length > 0 && item.isExpanded\" class=\"history-children\">\r\n                <div v-for=\"(child, childIndex) in item.children\" :key=\"childIndex\" class=\"history-child-item\"\r\n                  @click=\"loadHistoryItem(child)\">\r\n                  <div class=\"history-prompt\">{{ child.userPrompt }}</div>\r\n                  <div class=\"history-time\">{{ formatHistoryTime(child.createTime) }}</div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-drawer>\r\n\r\n    <div class=\"main-content\">\r\n      <el-collapse v-model=\"activeCollapses\">\r\n        <el-collapse-item title=\"AI选择配置\" name=\"ai-selection\">\r\n          <div class=\"ai-selection-section\">\r\n            <div class=\"ai-cards\">\r\n              <el-card v-for=\"(ai, index) in aiList\" :key=\"index\" class=\"ai-card\" shadow=\"hover\">\r\n                <div class=\"ai-card-header\">\r\n                  <div class=\"ai-left\">\r\n                    <div class=\"ai-avatar\">\r\n                      <img :src=\"ai.avatar\" alt=\"AI头像\">\r\n                    </div>\r\n                    <div class=\"ai-name\">{{ ai.name }}</div>\r\n                  </div>\r\n                  <div class=\"ai-status\">\r\n                    <el-switch v-model=\"ai.enabled\" active-color=\"#13ce66\" inactive-color=\"#ff4949\">\r\n                    </el-switch>\r\n                  </div>\r\n                </div>\r\n                <div class=\"ai-capabilities\" v-if=\"ai.capabilities && ai.capabilities.length > 0\">\r\n                  <div class=\"button-capability-group\">\r\n                    <el-button v-for=\"capability in ai.capabilities\" :key=\"capability.value\" size=\"mini\"\r\n                      :type=\"ai.selectedCapabilities.includes(capability.value) ? 'primary' : 'info'\"\r\n                      :disabled=\"!ai.enabled\" :plain=\"!ai.selectedCapabilities.includes(capability.value)\"\r\n                      @click=\"toggleCapability(ai, capability.value)\" class=\"capability-button\">\r\n                      {{ capability.label }}\r\n                    </el-button>\r\n                  </div>\r\n                </div>\r\n              </el-card>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n\r\n        <!-- 提示词输入区 -->\r\n        <el-collapse-item title=\"提示词输入\" name=\"prompt-input\">\r\n          <div class=\"prompt-input-section\">\r\n            <el-input type=\"textarea\" :rows=\"5\" placeholder=\"请输入提示词，支持Markdown格式\" v-model=\"promptInput\" resize=\"none\"\r\n              class=\"prompt-input\">\r\n            </el-input>\r\n            <div class=\"prompt-footer\">\r\n              <div class=\"word-count\">字数统计: {{ promptInput.length }}</div>\r\n              <el-button type=\"primary\" @click=\"sendPrompt\" :disabled=\"!canSend\" class=\"send-button\">\r\n                发送\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </el-collapse-item>\r\n      </el-collapse>\r\n\r\n      <!-- 执行状态展示区 -->\r\n      <div class=\"execution-status-section\" v-if=\"taskStarted\">\r\n        <el-row :gutter=\"20\">\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"task-flow-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>任务流程</span>\r\n              </div>\r\n              <div class=\"task-flow\">\r\n                <div v-for=\"(ai, index) in enabledAIs\" :key=\"index\" class=\"task-item\">\r\n                  <div class=\"task-header\" @click=\"toggleAIExpansion(ai)\">\r\n                    <div class=\"header-left\">\r\n                      <i :class=\"['el-icon-arrow-right', {'is-expanded': ai.isExpanded}]\"></i>\r\n                      <span class=\"ai-name\">{{ ai.name }}</span>\r\n                    </div>\r\n                    <div class=\"header-right\">\r\n                      <span class=\"status-text\">{{ getStatusText(ai.status) }}</span>\r\n                      <i :class=\"getStatusIcon(ai.status)\" class=\"status-icon\"></i>\r\n                    </div>\r\n                  </div>\r\n                  <!-- 添加进度轨迹 -->\r\n                  <div class=\"progress-timeline\" v-if=\"ai.progressLogs.length > 0 && ai.isExpanded\">\r\n                    <div class=\"timeline-scroll\">\r\n                      <div v-for=\"(log, logIndex) in ai.progressLogs\" :key=\"logIndex\" class=\"progress-item\" :class=\"{\r\n                             'completed': log.isCompleted || logIndex > 0,\r\n                             'current': !log.isCompleted && logIndex === 0\r\n                           }\">\r\n                        <div class=\"progress-dot\"></div>\r\n                        <div class=\"progress-line\" v-if=\"logIndex < ai.progressLogs.length - 1\"></div>\r\n                        <div class=\"progress-content\">\r\n                          <div class=\"progress-time\">{{ formatTime(log.timestamp) }}</div>\r\n                          <div class=\"progress-text\">{{ log.content }}</div>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n          <el-col :span=\"12\">\r\n            <el-card class=\"screenshots-card\">\r\n              <div slot=\"header\" class=\"card-header\">\r\n                <span>主机可视化</span>\r\n                <div class=\"controls\">\r\n                  <el-switch v-model=\"autoPlay\" active-text=\"自动轮播\" inactive-text=\"手动切换\">\r\n                  </el-switch>\r\n                </div>\r\n              </div>\r\n              <div class=\"screenshots\">\r\n                <el-carousel :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"700px\">\r\n                  <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n                    <img :src=\"screenshot\" alt=\"执行截图\" class=\"screenshot-image\" @click=\"showLargeImage(screenshot)\">\r\n                  </el-carousel-item>\r\n                </el-carousel>\r\n              </div>\r\n            </el-card>\r\n          </el-col>\r\n        </el-row>\r\n      </div>\r\n\r\n      <!-- 结果展示区 -->\r\n      <div class=\"results-section\" v-if=\"results.length > 0\">\r\n        <div class=\"section-header\">\r\n          <h2 class=\"section-title\">执行结果</h2>\r\n          <el-button type=\"primary\" @click=\"showScoreDialog\" size=\"small\">\r\n            智能评分\r\n          </el-button>\r\n        </div>\r\n        <el-tabs v-model=\"activeResultTab\" type=\"card\">\r\n          <el-tab-pane v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" :name=\"'result-' + index\">\r\n            <div class=\"result-content\">\r\n              <div class=\"result-header\" v-if=\"result.shareUrl\">\r\n                <div class=\"result-title\">{{ result.aiName }}的执行结果</div>\r\n                <div class=\"result-buttons\">\r\n                  <el-button size=\"mini\" type=\"primary\" icon=\"el-icon-link\" @click=\"openShareUrl(result.shareUrl)\"\r\n                    class=\"share-link-btn\">\r\n                    查看原链接\r\n                  </el-button>\r\n                  <el-button size=\"mini\" type=\"success\" icon=\"el-icon-s-promotion\" @click=\"handlePushToWechat(result)\"\r\n                    class=\"push-wechat-btn\" :loading=\"pushingToWechat\" :disabled=\"pushingToWechat\">\r\n                    投递到公众号\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <!-- 如果有shareImgUrl则渲染图片或PDF，否则渲染markdown -->\r\n              <div v-if=\"result.shareImgUrl\" class=\"share-content\">\r\n                <!-- 渲染图片 -->\r\n                <img v-if=\"isImageFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" alt=\"分享图片\" class=\"share-image\"\r\n                  :style=\"getImageStyle(result.aiName)\">\r\n                <!-- 渲染PDF -->\r\n                <iframe v-else-if=\"isPdfFile(result.shareImgUrl)\" :src=\"result.shareImgUrl\" class=\"share-pdf\"\r\n                  frameborder=\"0\">\r\n                </iframe>\r\n                <!-- 其他文件类型显示链接 -->\r\n                <div v-else class=\"share-file\">\r\n                  <el-button type=\"primary\" icon=\"el-icon-document\" @click=\"openShareUrl(result.shareImgUrl)\">\r\n                    查看文件\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n              <div v-else class=\"markdown-content\" v-html=\"renderMarkdown(result.content)\"></div>\r\n              <div class=\"action-buttons\">\r\n                <el-button size=\"small\" type=\"primary\" @click=\"copyResult(result.content)\">复制（纯文本）</el-button>\r\n                <el-button size=\"small\" type=\"success\" @click=\"exportResult(result)\">导出（MD文件）</el-button>\r\n              </div>\r\n            </div>\r\n          </el-tab-pane>\r\n        </el-tabs>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 大图查看对话框 -->\r\n    <el-dialog :visible.sync=\"showImageDialog\" width=\"90%\" :show-close=\"true\" :modal=\"true\" center class=\"image-dialog\"\r\n      :append-to-body=\"true\" @close=\"closeLargeImage\">\r\n      <div class=\"large-image-container\">\r\n        <!-- 如果是单张分享图片，直接显示 -->\r\n        <div v-if=\"currentLargeImage && !screenshots.includes(currentLargeImage)\" class=\"single-image-container\">\r\n          <img :src=\"currentLargeImage\" alt=\"大图\" class=\"large-image\">\r\n        </div>\r\n        <!-- 如果是截图轮播 -->\r\n        <el-carousel v-else :interval=\"3000\" :autoplay=\"false\" indicator-position=\"outside\" height=\"80vh\">\r\n          <el-carousel-item v-for=\"(screenshot, index) in screenshots\" :key=\"index\">\r\n            <img :src=\"screenshot\" alt=\"大图\" class=\"large-image\">\r\n          </el-carousel-item>\r\n        </el-carousel>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <!-- 评分弹窗 -->\r\n    <el-dialog title=\"智能评分\" :visible.sync=\"scoreDialogVisible\" width=\"60%\" height=\"65%\" :close-on-click-modal=\"false\"\r\n      class=\"score-dialog\">\r\n      <div class=\"score-dialog-content\">\r\n        <div class=\"score-prompt-section\">\r\n          <h3>评分提示词：</h3>\r\n          <el-input type=\"textarea\" :rows=\"10\" placeholder=\"请输入评分提示词，例如：请从内容质量、逻辑性、创新性等方面进行评分\" v-model=\"scorePrompt\"\r\n            resize=\"none\" class=\"score-prompt-input\">\r\n          </el-input>\r\n        </div>\r\n        <div class=\"selected-results\">\r\n          <h3>选择要评分的内容：</h3>\r\n          <el-checkbox-group v-model=\"selectedResults\">\r\n            <el-checkbox v-for=\"(result, index) in results\" :key=\"index\" :label=\"result.aiName\" class=\"result-checkbox\">\r\n              {{ result.aiName }}\r\n            </el-checkbox>\r\n          </el-checkbox-group>\r\n        </div>\r\n\r\n\r\n      </div>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button @click=\"scoreDialogVisible = false\">取 消</el-button>\r\n        <el-button type=\"primary\" @click=\"handleScore\" :disabled=\"!canScore\">\r\n          开始评分\r\n        </el-button>\r\n      </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {marked} from 'marked';\r\nimport {message, saveUserChatData, getChatHistory, pushAutoOffice} from \"@/api/wechat/aigc\";\r\nimport {\r\n\t\tv4 as uuidv4\r\n\t} from 'uuid';\r\nimport websocketClient from '@/utils/websocket';\r\nimport store from '@/store';\r\nimport TurndownService from 'turndown';\r\n\r\nexport default {\r\n  name: 'AIManagementPlatform',\r\n  data() {\r\n    return {\r\n      userId: store.state.user.id,\r\n      corpId: store.state.user.corp_id,\r\n      chatId: uuidv4(),\r\n      expandedHistoryItems: {},\r\n      userInfoReq: {\r\n        userPrompt: '',\r\n        userId: '',\r\n        corpId: '',\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      },\r\n      jsonRpcReqest: {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: '',\r\n        params: {}\r\n      },\r\n      aiList: [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: '搜狗搜索@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '百度AI',\r\n          avatar: require('../../../assets/ai/baidu.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ],\r\n      promptInput: '',\r\n      taskStarted: false,\r\n      autoPlay: false,\r\n      screenshots: [],\r\n      results: [],\r\n      activeResultTab: 'result-0',\r\n      activeCollapses: ['ai-selection', 'prompt-input'], // 默认展开这两个区域\r\n      showImageDialog: false,\r\n      currentLargeImage: '',\r\n      enabledAIs: [],\r\n      turndownService: new TurndownService({\r\n        headingStyle: 'atx',\r\n        codeBlockStyle: 'fenced',\r\n        emDelimiter: '*'\r\n      }),\r\n      scoreDialogVisible: false,\r\n      selectedResults: [],\r\n      scorePrompt: `请你深度阅读以下几篇公众号章，从多个维度进行逐项打分，输出评分结果。并在以下各篇文章的基础上博采众长，综合整理一篇更全面的文章。`,\r\n      historyDrawerVisible: false,\r\n      chatHistory: [],\r\n      pushOfficeNum: 0, // 投递到公众号的递增编号\r\n      pushingToWechat: false, // 投递到公众号的loading状态\r\n    };\r\n  },\r\n  computed: {\r\n    canSend() {\r\n      return this.promptInput.trim().length > 0 && this.aiList.some(ai => ai.enabled);\r\n    },\r\n    canScore() {\r\n      return this.selectedResults.length > 0 && this.scorePrompt.trim().length > 0;\r\n    },\r\n    groupedHistory() {\r\n      const groups = {};\r\n      const chatGroups = {};\r\n\r\n      // 首先按chatId分组\r\n      this.chatHistory.forEach(item => {\r\n        if (!chatGroups[item.chatId]) {\r\n          chatGroups[item.chatId] = [];\r\n        }\r\n        chatGroups[item.chatId].push(item);\r\n      });\r\n\r\n      // 然后按日期分组，并处理父子关系\r\n      Object.values(chatGroups).forEach(chatGroup => {\r\n        // 按时间排序\r\n        chatGroup.sort((a, b) => new Date(a.createTime) - new Date(b.createTime));\r\n\r\n        // 获取最早的记录作为父级\r\n        const parentItem = chatGroup[0];\r\n        const date = this.getHistoryDate(parentItem.createTime);\r\n\r\n        if (!groups[date]) {\r\n          groups[date] = [];\r\n        }\r\n\r\n        // 添加父级记录\r\n        groups[date].push({\r\n          ...parentItem,\r\n          isParent: true,\r\n          isExpanded: this.expandedHistoryItems[parentItem.chatId] || false,\r\n          children: chatGroup.slice(1).map(child => ({\r\n            ...child,\r\n            isParent: false\r\n          }))\r\n        });\r\n      });\r\n\r\n      return groups;\r\n    }\r\n  },\r\n  created() {\r\n    console.log(this.userId);\r\n    console.log(this.corpId);\r\n    this.initWebSocket(this.userId);\r\n    this.loadChatHistory(0); // 加载历史记录\r\n    this.loadLastChat(); // 加载上次会话\r\n  },\r\n  methods: {\r\n    sendPrompt() {\r\n      if (!this.canSend) return;\r\n\r\n      this.screenshots =[];\r\n      // 折叠所有区域\r\n      this.activeCollapses = [];\r\n\r\n      this.taskStarted = true;\r\n      this.results = []; // 清空之前的结果\r\n\r\n      this.userInfoReq.roles = '';\r\n\r\n\r\n      this.userInfoReq.taskId = uuidv4();\r\n      this.userInfoReq.userId = this.userId;\r\n      this.userInfoReq.corpId = this.corpId;\r\n      this.userInfoReq.userPrompt = this.promptInput;\r\n\r\n      // 获取启用的AI列表及其状态\r\n      this.enabledAIs = this.aiList.filter(ai => ai.enabled);\r\n\r\n      // 将所有启用的AI状态设置为运行中\r\n      this.enabledAIs.forEach(ai => {\r\n        this.$set(ai, 'status', 'running');\r\n      });\r\n\r\n      this.enabledAIs.forEach(ai => {\r\n        if(ai.name === '腾讯元宝T1'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-hunyuan-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === '腾讯元宝DS'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-pt,';\r\n          if(ai.selectedCapabilities.includes(\"deep_thinking\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-sdsk,';\r\n          }\r\n          if(ai.selectedCapabilities.includes(\"web_search\")){\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'yb-deepseek-lwss,';\r\n          }\r\n        }\r\n        if(ai.name === 'TurboS@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-trubos-agent,';\r\n        }\r\n        if(ai.name === 'TurboS长文版@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-turbos-large-agent,';\r\n        }\r\n        if(ai.name === 'MiniMax@元器'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'cube-mini-max-agent,';\r\n        }\r\n        // if(ai.name === '搜狗搜索@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-sogou-agent,';\r\n        // }\r\n        // if(ai.name === 'KIMI@元器'){\r\n        //   this.userInfoReq.roles = this.userInfoReq.roles + 'cube-lwss-agent,';\r\n        // }\r\n        if(ai.name === '豆包'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db,';\r\n          if (ai.selectedCapabilities.includes(\"deep_thinking\")) {\r\n            this.userInfoReq.roles = this.userInfoReq.roles + 'zj-db-sdsk,';\r\n          }\r\n        }\r\n        if(ai.name === '百度AI'){\r\n          this.userInfoReq.roles = this.userInfoReq.roles + 'wx-,';\r\n        }\r\n      });\r\n\r\n      console.log(\"参数：\", this.userInfoReq)\r\n\r\n      //调用后端接口\r\n      this.jsonRpcReqest.method = \"使用F8S\"\r\n      this.jsonRpcReqest.params = this.userInfoReq\r\n      this.message(this.jsonRpcReqest)\r\n      this.userInfoReq.isNewChat = false;\r\n    },\r\n\r\n    message(data) {\r\n      message(data).then(res => {\r\n        if (res.code == 201) {\r\n          uni.showToast({\r\n            title: res.messages,\r\n            icon: 'none',\r\n            duration: 1500,\r\n          });\r\n        }\r\n      })\r\n\r\n    },\r\n    toggleCapability(ai, capabilityValue) {\r\n      if (!ai.enabled) return;\r\n\r\n      const index = ai.selectedCapabilities.indexOf(capabilityValue);\r\n      console.log('切换前:', ai.selectedCapabilities);\r\n      if (index === -1) {\r\n        // 如果不存在，则添加\r\n        this.$set(ai.selectedCapabilities, ai.selectedCapabilities.length, capabilityValue);\r\n      } else {\r\n        // 如果已存在，则移除\r\n        const newCapabilities = [...ai.selectedCapabilities];\r\n        newCapabilities.splice(index, 1);\r\n        this.$set(ai, 'selectedCapabilities', newCapabilities);\r\n      }\r\n      console.log('切换后:', ai.selectedCapabilities);\r\n      this.$forceUpdate(); // 强制更新视图\r\n    },\r\n    getStatusText(status) {\r\n      switch (status) {\r\n        case 'idle': return '等待中';\r\n        case 'running': return '正在执行';\r\n        case 'completed': return '已完成';\r\n        case 'failed': return '执行失败';\r\n        default: return '未知状态';\r\n      }\r\n    },\r\n    getStatusIcon(status) {\r\n      switch (status) {\r\n        case 'idle': return 'el-icon-time';\r\n        case 'running': return 'el-icon-loading';\r\n        case 'completed': return 'el-icon-check success-icon';\r\n        case 'failed': return 'el-icon-close error-icon';\r\n        default: return 'el-icon-question';\r\n      }\r\n    },\r\n    renderMarkdown(text) {\r\n      return marked(text);\r\n    },\r\n    // HTML转纯文本\r\n    htmlToText(html) {\r\n      const tempDiv = document.createElement('div');\r\n      tempDiv.innerHTML = html;\r\n      return tempDiv.textContent || tempDiv.innerText || '';\r\n    },\r\n\r\n    // HTML转Markdown\r\n    htmlToMarkdown(html) {\r\n      return this.turndownService.turndown(html);\r\n    },\r\n\r\n    copyResult(content) {\r\n      // 将HTML转换为纯文本\r\n      const plainText = this.htmlToText(content);\r\n      const textarea = document.createElement('textarea');\r\n      textarea.value = plainText;\r\n      document.body.appendChild(textarea);\r\n      textarea.select();\r\n      document.execCommand('copy');\r\n      document.body.removeChild(textarea);\r\n      this.$message.success('已复制纯文本到剪贴板');\r\n    },\r\n\r\n    exportResult(result) {\r\n      // 将HTML转换为Markdown\r\n      const markdown = result.content;\r\n      const blob = new Blob([markdown], { type: 'text/markdown' });\r\n      const link = document.createElement('a');\r\n      link.href = URL.createObjectURL(blob);\r\n      link.download = `${result.aiName}_结果_${new Date().toISOString().slice(0, 10)}.md`;\r\n      link.click();\r\n      URL.revokeObjectURL(link.href);\r\n      this.$message.success('已导出Markdown文件');\r\n    },\r\n\r\n    openShareUrl(shareUrl) {\r\n      if (shareUrl) {\r\n        window.open(shareUrl, '_blank');\r\n      } else {\r\n        this.$message.warning('暂无原链接');\r\n      }\r\n    },\r\n    showLargeImage(imageUrl) {\r\n      this.currentLargeImage = imageUrl;\r\n      this.showImageDialog = true;\r\n      // 找到当前图片的索引，设置轮播图的初始位置\r\n      const currentIndex = this.screenshots.indexOf(imageUrl);\r\n      if (currentIndex !== -1) {\r\n        this.$nextTick(() => {\r\n          const carousel = this.$el.querySelector('.image-dialog .el-carousel');\r\n          if (carousel && carousel.__vue__) {\r\n            carousel.__vue__.setActiveItem(currentIndex);\r\n          }\r\n        });\r\n      }\r\n    },\r\n    closeLargeImage() {\r\n      this.showImageDialog = false;\r\n      this.currentLargeImage = '';\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log('WebSocket URL:', process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case 'open':\r\n            // this.$message.success('');\r\n            break;\r\n          case 'message':\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case 'close':\r\n            this.$message.warning('WebSocket连接已关闭');\r\n            break;\r\n          case 'error':\r\n            this.$message.error('WebSocket连接错误');\r\n            break;\r\n          case 'reconnect_failed':\r\n            this.$message.error('WebSocket重连失败，请刷新页面重试');\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      // 处理chatId消息\r\n      if (dataObj.type === 'RETURN_YBT1_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.toneChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_YBDS_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.ybDsChatId = dataObj.chatId;\r\n      } else if (dataObj.type === 'RETURN_DB_CHATID' && dataObj.chatId) {\r\n        this.userInfoReq.dbChatId = dataObj.chatId;\r\n      }\r\n\r\n      // 处理进度日志消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_LOG' && dataObj.aiName) {\r\n        const targetAI = this.enabledAIs.find(ai => ai.name === dataObj.aiName);\r\n        if (targetAI) {\r\n          // 将新进度添加到数组开头\r\n          targetAI.progressLogs.unshift({\r\n            content: dataObj.content,\r\n            timestamp: new Date(),\r\n            isCompleted: false\r\n          });\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 处理截图消息\r\n      if (dataObj.type === 'RETURN_PC_TASK_IMG' && dataObj.url) {\r\n        // 将新的截图添加到数组开头\r\n        this.screenshots.unshift(dataObj.url);\r\n        return;\r\n      }\r\n\r\n              // 处理智能评分结果\r\n      if (dataObj.type === 'RETURN_WKPF_RES') {\r\n        const wkpfAI = this.enabledAIs.find(ai => ai.name === '智能评分');\r\n        if (wkpfAI) {\r\n          this.$set(wkpfAI, 'status', 'completed');\r\n          if (wkpfAI.progressLogs.length > 0) {\r\n            this.$set(wkpfAI.progressLogs[0], 'isCompleted', true);\r\n          }\r\n          // 添加评分结果到results最前面\r\n          this.results.unshift({\r\n            aiName: '智能评分',\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n\r\n          // 智能评分完成时，再次保存历史记录\r\n          this.saveHistory();\r\n        }\r\n        return;\r\n      }\r\n\r\n      // 根据消息类型更新对应AI的状态和结果\r\n      let targetAI = null;\r\n      switch (dataObj.type) {\r\n        case 'RETURN_YBT1_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝T1');\r\n          break;\r\n        case 'RETURN_YBDS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '腾讯元宝DS');\r\n          break;\r\n        case 'RETURN_DB_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '豆包');\r\n          break;\r\n        case 'RETURN_WX_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === '百度AI');\r\n          break;\r\n        case 'RETURN_TURBOS_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS@元器');\r\n          break;\r\n        case 'RETURN_TURBOS_LARGE_RES':\r\n          console.log('收到消息:', data);\r\n          targetAI = this.enabledAIs.find(ai => ai.name === 'TurboS长文版@元器');\r\n          break;\r\n        // case 'RETURN_MINI_MAX_RES':\r\n        //   targetAI = this.enabledAIs.find(ai => ai.name === 'MiniMax@元器');\r\n        //   break;\r\n      }\r\n\r\n      if (targetAI) {\r\n        // 更新AI状态为已完成\r\n        this.$set(targetAI, 'status', 'completed');\r\n\r\n        // 将最后一条进度消息标记为已完成\r\n        if (targetAI.progressLogs.length > 0) {\r\n          this.$set(targetAI.progressLogs[0], 'isCompleted', true);\r\n        }\r\n\r\n        // 添加结果到数组开头\r\n        const resultIndex = this.results.findIndex(r => r.aiName === targetAI.name);\r\n        if (resultIndex === -1) {\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        } else {\r\n          this.results.splice(resultIndex, 1);\r\n          this.results.unshift({\r\n            aiName: targetAI.name,\r\n            content: dataObj.draftContent,\r\n            shareUrl: dataObj.shareUrl || '',\r\n            shareImgUrl: dataObj.shareImgUrl || '',\r\n            timestamp: new Date()\r\n          });\r\n          this.activeResultTab = 'result-0';\r\n        }\r\n        this.saveHistory();\r\n      }\r\n\r\n      // 检查是否所有任务都已完成\r\n      // const allCompleted = this.enabledAIs.every(ai =>\r\n      //   ai.status === 'completed' || ai.status === 'failed'\r\n      // );\r\n\r\n      // if (allCompleted) {\r\n      //\r\n      // }\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error('WebSocket未连接');\r\n      }\r\n    },\r\n    toggleAIExpansion(ai) {\r\n      this.$set(ai, 'isExpanded', !ai.isExpanded);\r\n    },\r\n\r\n    formatTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        second: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n    showScoreDialog() {\r\n      this.scoreDialogVisible = true;\r\n      this.selectedResults = [];\r\n    },\r\n\r\n    handleScore() {\r\n      if (!this.canScore) return;\r\n\r\n      // 获取选中的结果内容并按照指定格式拼接\r\n      const selectedContents = this.results\r\n        .filter(result => this.selectedResults.includes(result.aiName))\r\n        .map(result => {\r\n          // 将HTML内容转换为纯文本\r\n          const plainContent = this.htmlToText(result.content);\r\n          return `${result.aiName}初稿：\\n${plainContent}\\n`;\r\n        })\r\n        .join('\\n');\r\n\r\n      // 构建完整的评分提示内容\r\n      const fullPrompt = `${this.scorePrompt}\\n${selectedContents}`;\r\n\r\n      // 构建评分请求\r\n      const scoreRequest = {\r\n        jsonrpc: '2.0',\r\n        id: uuidv4(),\r\n        method: 'AI评分',\r\n        params: {\r\n          taskId: uuidv4(),\r\n          userId: this.userId,\r\n          corpId: this.corpId,\r\n          userPrompt: fullPrompt,\r\n          roles: 'zj-db-sdsk' // 默认使用豆包进行评分\r\n        }\r\n      };\r\n\r\n      // 发送评分请求\r\n      console.log(\"参数\", scoreRequest)\r\n      this.message(scoreRequest);\r\n      this.scoreDialogVisible = false;\r\n\r\n      // 创建智能评分AI节点\r\n      const wkpfAI = {\r\n        name: '智能评分',\r\n        avatar: require('../../../assets/ai/yuanbao.png'),\r\n        capabilities: [],\r\n        selectedCapabilities: [],\r\n        enabled: true,\r\n        status: 'running',\r\n        progressLogs: [\r\n          {\r\n            content: '智能评分任务已提交，正在评分...',\r\n            timestamp: new Date(),\r\n            isCompleted: false,\r\n            type: '智能评分'\r\n          }\r\n        ],\r\n        isExpanded: true\r\n      };\r\n\r\n      // 检查是否已存在智能评分\r\n      const existIndex = this.enabledAIs.findIndex(ai => ai.name === '智能评分');\r\n      if (existIndex === -1) {\r\n        // 如果不存在，添加到数组开头\r\n        this.enabledAIs.unshift(wkpfAI);\r\n      } else {\r\n        // 如果已存在，更新状态和日志\r\n        this.enabledAIs[existIndex] = wkpfAI;\r\n        // 将智能评分移到数组开头\r\n        const wkpf = this.enabledAIs.splice(existIndex, 1)[0];\r\n        this.enabledAIs.unshift(wkpf);\r\n      }\r\n\r\n      this.$forceUpdate();\r\n      this.$message.success('评分请求已发送，请等待结果');\r\n    },\r\n    // 显示历史记录抽屉\r\n    showHistoryDrawer() {\r\n      this.historyDrawerVisible = true;\r\n      this.loadChatHistory(1);\r\n    },\r\n\r\n    // 关闭历史记录抽屉\r\n    handleHistoryDrawerClose() {\r\n      this.historyDrawerVisible = false;\r\n    },\r\n\r\n    // 加载历史记录\r\n    async loadChatHistory(isAll) {\r\n      try {\r\n        const res = await getChatHistory(this.userId, isAll);\r\n        if (res.code === 200) {\r\n          this.chatHistory = res.data || [];\r\n        }\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 格式化历史记录时间\r\n    formatHistoryTime(timestamp) {\r\n      const date = new Date(timestamp);\r\n      return date.toLocaleTimeString('zh-CN', {\r\n        hour: '2-digit',\r\n        minute: '2-digit',\r\n        hour12: false\r\n      });\r\n    },\r\n\r\n    // 获取历史记录日期分组\r\n    getHistoryDate(timestamp) {\r\n      const date = new Date(timestamp);\r\n      const today = new Date();\r\n      const yesterday = new Date(today);\r\n      yesterday.setDate(yesterday.getDate() - 1);\r\n      const twoDaysAgo = new Date(today);\r\n      twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);\r\n      const threeDaysAgo = new Date(today);\r\n      threeDaysAgo.setDate(threeDaysAgo.getDate() - 3);\r\n\r\n      if (date.toDateString() === today.toDateString()) {\r\n        return '今天';\r\n      } else if (date.toDateString() === yesterday.toDateString()) {\r\n        return '昨天';\r\n      } else if (date.toDateString() === twoDaysAgo.toDateString()) {\r\n        return '两天前';\r\n      } else if (date.toDateString() === threeDaysAgo.toDateString()) {\r\n        return '三天前';\r\n      } else {\r\n        return date.toLocaleDateString('zh-CN', {\r\n          year: 'numeric',\r\n          month: 'long',\r\n          day: 'numeric'\r\n        });\r\n      }\r\n    },\r\n\r\n    // 加载历史记录项\r\n    loadHistoryItem(item) {\r\n      try {\r\n        const historyData = JSON.parse(item.data);\r\n        // 恢复AI选择配置\r\n        this.aiList = historyData.aiList || this.aiList;\r\n        // 恢复提示词输入\r\n        this.promptInput = historyData.promptInput || '';\r\n        // 恢复任务流程\r\n        this.enabledAIs = historyData.enabledAIs || [];\r\n        // 恢复主机可视化\r\n        this.screenshots = historyData.screenshots || [];\r\n        // 恢复执行结果\r\n        this.results = historyData.results || [];\r\n        // 恢复chatId\r\n        this.chatId = item.chatId || this.chatId;\r\n        this.userInfoReq.toneChatId = item.toneChatId || '';\r\n        this.userInfoReq.ybDsChatId = item.ybDsChatId || '';\r\n        this.userInfoReq.dbChatId = item.dbChatId || '';\r\n        this.userInfoReq.isNewChat = false;\r\n\r\n        // 展开相关区域\r\n        this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n        this.taskStarted = true;\r\n\r\n        this.$message.success('历史记录加载成功');\r\n        this.historyDrawerVisible = false;\r\n      } catch (error) {\r\n        console.error('加载历史记录失败:', error);\r\n        this.$message.error('加载历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 保存历史记录\r\n    async saveHistory() {\r\n      // if (!this.taskStarted || this.enabledAIs.some(ai => ai.status === 'running')) {\r\n      //   return;\r\n      // }\r\n\r\n      const historyData = {\r\n        aiList: this.aiList,\r\n        promptInput: this.promptInput,\r\n        enabledAIs: this.enabledAIs,\r\n        screenshots: this.screenshots,\r\n        results: this.results,\r\n        chatId: this.chatId,\r\n        toneChatId: this.userInfoReq.toneChatId,\r\n        ybDsChatId: this.userInfoReq.ybDsChatId,\r\n        dbChatId: this.userInfoReq.dbChatId\r\n      };\r\n\r\n      try {\r\n        await saveUserChatData({\r\n          userId: this.userId,\r\n          userPrompt: this.promptInput,\r\n          data: JSON.stringify(historyData),\r\n          chatId: this.chatId,\r\n          toneChatId: this.userInfoReq.toneChatId,\r\n          ybDsChatId: this.userInfoReq.ybDsChatId,\r\n          dbChatId: this.userInfoReq.dbChatId\r\n        });\r\n      } catch (error) {\r\n        console.error('保存历史记录失败:', error);\r\n        this.$message.error('保存历史记录失败');\r\n      }\r\n    },\r\n\r\n    // 修改折叠切换方法\r\n    toggleHistoryExpansion(item) {\r\n      this.$set(this.expandedHistoryItems, item.chatId, !this.expandedHistoryItems[item.chatId]);\r\n    },\r\n\r\n    // 创建新对话\r\n    createNewChat() {\r\n      // 重置所有数据\r\n      this.chatId = uuidv4();\r\n      this.isNewChat = true;\r\n      this.promptInput = '';\r\n      this.taskStarted = false;\r\n      this.screenshots = [];\r\n      this.results = [];\r\n      this.enabledAIs = [];\r\n      this.userInfoReq = {\r\n        userPrompt: '',\r\n        userId: this.userId,\r\n        corpId: this.corpId,\r\n        taskId: '',\r\n        roles: '',\r\n        toneChatId: '',\r\n        ybDsChatId: '',\r\n        dbChatId: '',\r\n        isNewChat: true\r\n      };\r\n      // 重置AI列表为初始状态\r\n      this.aiList = [\r\n        {\r\n          name: 'TurboS@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: 'TurboS长文版@元器',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [],\r\n          selectedCapabilities: [],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        // {\r\n        //   name: 'MiniMax@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        // {\r\n        //   name: 'KIMI@元器',\r\n        //   avatar: require('../../../assets/ai/yuanbao.png'),\r\n        //   capabilities: [],\r\n        //   selectedCapabilities: [],\r\n        //   enabled: true,\r\n        //   status: 'idle',\r\n        //   progressLogs: [],\r\n        //   isExpanded: true\r\n        // },\r\n        {\r\n          name: '腾讯元宝T1',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '腾讯元宝DS',\r\n          avatar: require('../../../assets/ai/yuanbao.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' },\r\n            { label: '联网搜索', value: 'web_search' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking','web_search'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '豆包',\r\n          avatar: require('../../../assets/ai/豆包.png'),\r\n          capabilities: [\r\n            { label: '深度思考', value: 'deep_thinking' }\r\n          ],\r\n          selectedCapabilities: ['deep_thinking'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        },\r\n        {\r\n          name: '百度AI',\r\n          avatar: require('../../../assets/ai/baidu.png'),\r\n          capabilities: [\r\n            { label: '文心4.5 Turbo', value: 'wx-4.5' },\r\n            { label: '文心X1 Turbo', value: 'wx-x1' }\r\n          ],\r\n          selectedCapabilities: ['wx-4.5'],\r\n          enabled: true,\r\n          status: 'idle',\r\n          progressLogs: [],\r\n          isExpanded: true\r\n        }\r\n      ];\r\n      // 展开相关区域\r\n      this.activeCollapses = ['ai-selection', 'prompt-input'];\r\n\r\n      this.$message.success('已创建新对话');\r\n    },\r\n\r\n    // 加载上次会话\r\n    async loadLastChat() {\r\n      try {\r\n        const res = await getChatHistory(this.userId,0);\r\n        if (res.code === 200 && res.data && res.data.length > 0) {\r\n          // 获取最新的会话记录\r\n          const lastChat = res.data[0];\r\n          this.loadHistoryItem(lastChat);\r\n        }\r\n      } catch (error) {\r\n        console.error('加载上次会话失败:', error);\r\n      }\r\n    },\r\n\r\n    // 判断是否为图片文件\r\n    isImageFile(url) {\r\n      if (!url) return false;\r\n      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg'];\r\n      const urlLower = url.toLowerCase();\r\n      return imageExtensions.some(ext => urlLower.includes(ext));\r\n    },\r\n\r\n    // 判断是否为PDF文件\r\n    isPdfFile(url) {\r\n      if (!url) return false;\r\n      return url.toLowerCase().includes('.pdf');\r\n    },\r\n\r\n    // 根据AI名称获取图片样式\r\n    getImageStyle(aiName) {\r\n      const widthMap = {\r\n        'TurboS@元器': '700px',\r\n        '腾讯元宝DS': '700px',\r\n        'TurboS长文版@元器': '700px',\r\n        '腾讯元宝T1': '700px',\r\n        '豆包': '560px',\r\n        '百度AI': '700px'\r\n      };\r\n\r\n      const width = widthMap[aiName] || '560px'; // 默认宽度\r\n\r\n      return {\r\n        width: width,\r\n        height: 'auto'\r\n      };\r\n    },\r\n\r\n    // 投递到公众号\r\n    handlePushToWechat(result) {\r\n      if (this.pushingToWechat) return; // 防止重复点击\r\n\r\n      this.pushingToWechat = true; // 开始loading\r\n      this.pushOfficeNum += 1; // 递增编号\r\n\r\n      const params = {\r\n        contentText: result.content,\r\n        shareUrl: result.shareUrl,\r\n        userId: this.userId,\r\n        num: this.pushOfficeNum,\r\n        aiName: result.aiName\r\n      };\r\n\r\n      pushAutoOffice(params).then(res => {\r\n        if (res.code === 200) {\r\n          this.$message.success('投递到公众号成功！');\r\n        } else {\r\n          this.$message.error(res.msg || '投递失败，请重试');\r\n        }\r\n      }).catch(error => {\r\n        console.error('投递到公众号失败:', error);\r\n        this.$message.error('投递失败，请重试');\r\n      }).finally(() => {\r\n        this.pushingToWechat = false; // 结束loading\r\n      });\r\n    },\r\n\r\n\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped>\r\n.ai-management-platform {\r\n  min-height: 100vh;\r\n  background-color: #f5f7fa;\r\n  padding-bottom: 30px;\r\n}\r\n\r\n.top-nav {\r\n  background-color: #fff;\r\n  padding: 15px 20px;\r\n  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  margin-bottom: 20px;\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.logo-area {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.logo {\r\n  height: 36px;\r\n  margin-right: 10px;\r\n}\r\n\r\n.platform-title {\r\n  margin: 0;\r\n  font-size: 20px;\r\n  color: #303133;\r\n}\r\n\r\n.main-content {\r\n  padding: 0 30px;\r\n  width: 90%;\r\n  margin: 0 auto;\r\n}\r\n::v-deep .el-collapse-item__header {\r\n  font-size: 16px;\r\n  color: #333;\r\n  padding-left: 20px;\r\n}\r\n.section-title {\r\n  font-size: 18px;\r\n  color: #606266;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-cards {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 20px;\r\n  margin-bottom: 0px;\r\n  margin-left: 20px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.ai-card {\r\n  width: calc(25% - 20px);\r\n  box-sizing: border-box;\r\n}\r\n\r\n.ai-card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.ai-left {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-avatar {\r\n  margin-right: 10px;\r\n}\r\n\r\n.ai-avatar img {\r\n  width: 30px;\r\n  height: 30px;\r\n  border-radius: 50%;\r\n  object-fit: cover;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: bold;\r\n  font-size: 12px;\r\n}\r\n\r\n.ai-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.ai-capabilities {\r\n  margin: 15px 0;\r\n  width: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  flex-wrap: wrap;\r\n}\r\n\r\n.button-capability-group {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  justify-content: center;\r\n  gap: 8px;\r\n}\r\n\r\n.button-capability-group .el-button {\r\n  margin: 0;\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.button-capability-group .el-button.is-plain:hover,\r\n.button-capability-group .el-button.is-plain:focus {\r\n  background: #ecf5ff;\r\n  border-color: #b3d8ff;\r\n  color: #409EFF;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.prompt-input {\r\n  margin-bottom: 10px;\r\n  margin-left: 20px;\r\n  width: 99%;\r\n}\r\n\r\n.prompt-footer {\r\n  display: flex;\r\n  margin-bottom: -30px;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.word-count {\r\n  font-size: 12px;\r\n  padding-left: 20px;\r\n}\r\n\r\n.send-button {\r\n  padding: 10px 20px;\r\n}\r\n\r\n.execution-status-section {\r\n  margin-bottom: 30px;\r\n  padding:20px 0px 0px 0px;\r\n}\r\n\r\n.task-flow-card, .screenshots-card {\r\n  height: 800px;\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.task-flow {\r\n  padding: 15px;\r\n  height: 800px;\r\n  overflow-y: auto;\r\n  background-color: #f5f7fa;\r\n  border-radius: 4px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 3px;\r\n}\r\n\r\n.task-flow::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.task-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\r\n  overflow: hidden;\r\n}\r\n\r\n.task-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 15px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.task-header:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.header-left {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-left .el-icon-arrow-right {\r\n  transition: transform 0.3s;\r\n  font-size: 14px;\r\n  color: #909399;\r\n}\r\n\r\n.header-left .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.progress-timeline {\r\n  position: relative;\r\n  margin: 0;\r\n  padding: 15px 0;\r\n}\r\n\r\n.timeline-scroll {\r\n  max-height: 200px;\r\n  overflow-y: auto;\r\n  padding: 0 15px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar {\r\n  width: 4px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-thumb {\r\n  background-color: #c0c4cc;\r\n  border-radius: 2px;\r\n}\r\n\r\n.timeline-scroll::-webkit-scrollbar-track {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.progress-item {\r\n  position: relative;\r\n  padding: 8px 0 8px 20px;\r\n  display: flex;\r\n  align-items: flex-start;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.progress-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.progress-dot {\r\n  position: absolute;\r\n  left: 0;\r\n  top: 12px;\r\n  width: 10px;\r\n  height: 10px;\r\n  border-radius: 50%;\r\n  background-color: #e0e0e0;\r\n  flex-shrink: 0;\r\n}\r\n\r\n.progress-line {\r\n  position: absolute;\r\n  left: 4px;\r\n  top: 22px;\r\n  bottom: -8px;\r\n  width: 2px;\r\n  background-color: #e0e0e0;\r\n}\r\n\r\n.progress-content {\r\n  flex: 1;\r\n  min-width: 0;\r\n}\r\n\r\n.progress-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n  margin-bottom: 4px;\r\n}\r\n\r\n.progress-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n  line-height: 1.4;\r\n  word-break: break-all;\r\n}\r\n\r\n.progress-item.completed .progress-dot {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.completed .progress-line {\r\n  background-color: #67c23a;\r\n}\r\n\r\n.progress-item.current .progress-dot {\r\n  background-color: #409eff;\r\n  animation: pulse 1.5s infinite;\r\n}\r\n\r\n.progress-item.current .progress-line {\r\n  background-color: #409eff;\r\n}\r\n\r\n.ai-name {\r\n  font-weight: 600;\r\n  font-size: 14px;\r\n  color: #303133;\r\n}\r\n\r\n.header-right {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 13px;\r\n  color: #606266;\r\n}\r\n\r\n.status-icon {\r\n  font-size: 16px;\r\n}\r\n\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n@keyframes pulse {\r\n  0% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0.4);\r\n  }\r\n  70% {\r\n    box-shadow: 0 0 0 6px rgba(64, 158, 255, 0);\r\n  }\r\n  100% {\r\n    box-shadow: 0 0 0 0 rgba(64, 158, 255, 0);\r\n  }\r\n}\r\n\r\n.screenshot-image {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n  cursor: pointer;\r\n  transition: transform 0.3s;\r\n}\r\n\r\n.screenshot-image:hover {\r\n  transform: scale(1.05);\r\n}\r\n\r\n.results-section {\r\n  margin-top: 20px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.result-content {\r\n  padding: 20px 30px;\r\n}\r\n\r\n.result-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n  padding-bottom: 10px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.result-title {\r\n  font-size: 16px;\r\n  font-weight: 600;\r\n  color: #303133;\r\n}\r\n\r\n.result-buttons {\r\n  display: flex;\r\n  gap: 10px;\r\n  align-items: center;\r\n}\r\n\r\n.share-link-btn, .push-wechat-btn {\r\n  border-radius: 16px;\r\n  padding: 6px 12px;\r\n}\r\n\r\n.markdown-content {\r\n  margin-bottom: 20px;\r\n  max-height: 400px;\r\n  overflow-y: auto;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n}\r\n\r\n.action-buttons {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  padding: 0 10px;\r\n}\r\n\r\n@media (max-width: 1200px) {\r\n  .ai-card {\r\n    width: calc(33.33% - 14px);\r\n  }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n  .ai-card {\r\n    width: calc(50% - 10px);\r\n  }\r\n}\r\n\r\n@media (max-width: 768px) {\r\n  .ai-card {\r\n    width: 100%;\r\n  }\r\n}\r\n\r\n.el-collapse {\r\n  border-top: none;\r\n  border-bottom: none;\r\n}\r\n\r\n\r\n\r\n.el-collapse-item__content {\r\n  padding: 15px 0;\r\n}\r\n\r\n.ai-selection-section {\r\n  margin-bottom: 0;\r\n}\r\n\r\n.prompt-input-section {\r\n  margin-bottom: 30px;\r\n  padding: 0 20px 0 0px;\r\n}\r\n\r\n.image-dialog .el-dialog__body {\r\n  padding: 0;\r\n}\r\n\r\n.large-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.large-image {\r\n  max-width: 100%;\r\n  max-height: 80vh;\r\n  object-fit: contain;\r\n}\r\n\r\n.image-dialog .el-carousel {\r\n  width: 100%;\r\n  height: 100%;\r\n}\r\n\r\n.image-dialog .el-carousel__container {\r\n  height: 80vh;\r\n}\r\n\r\n.image-dialog .el-carousel__item {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  background-color: #000;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.score-dialog-content {\r\n  padding: 20px;\r\n}\r\n\r\n.selected-results {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.result-checkbox {\r\n  margin-right: 20px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.score-prompt-section {\r\n  margin-top: 20px;\r\n}\r\n\r\n.score-prompt-input {\r\n  margin-top: 10px;\r\n}\r\n\r\n.score-prompt-input .el-textarea__inner {\r\n  min-height: 500px !important;\r\n}\r\n\r\n.dialog-footer {\r\n  text-align: right;\r\n}\r\n\r\n.score-dialog .el-dialog {\r\n  height: 95vh;\r\n  margin-top: 2.5vh !important;\r\n}\r\n\r\n.score-dialog .el-dialog__body {\r\n  height: calc(95vh - 120px);\r\n  overflow-y: auto;\r\n  padding: 20px;\r\n}\r\n\r\n.nav-buttons {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 20px;\r\n}\r\n\r\n.history-button {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.history-icon {\r\n  width: 24px;\r\n  height: 24px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.history-content {\r\n  padding: 20px;\r\n}\r\n\r\n.history-group {\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.history-date {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  margin-bottom: 10px;\r\n  padding: 5px 0;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n}\r\n\r\n.history-item {\r\n  margin-bottom: 15px;\r\n  border-radius: 4px;\r\n  background-color: #f5f7fa;\r\n  overflow: hidden;\r\n}\r\n\r\n.history-parent {\r\n  padding: 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n\r\n.history-parent:hover {\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.history-children {\r\n  padding-left: 20px;\r\n  background-color: #fff;\r\n  transition: all 0.3s ease;\r\n}\r\n\r\n.history-child-item {\r\n  padding: 8px 10px;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s;\r\n  border-bottom: 1px solid #f0f0f0;\r\n}\r\n\r\n.history-child-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-child-item:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  gap: 8px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right {\r\n  font-size: 14px;\r\n  color: #909399;\r\n  transition: transform 0.3s;\r\n  cursor: pointer;\r\n  margin-top: 3px;\r\n}\r\n\r\n.history-header .el-icon-arrow-right.is-expanded {\r\n  transform: rotate(90deg);\r\n}\r\n\r\n.history-prompt {\r\n  font-size: 14px;\r\n  color: #303133;\r\n  margin-bottom: 5px;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  display: -webkit-box;\r\n  -webkit-line-clamp: 2;\r\n  -webkit-box-orient: vertical;\r\n  flex: 1;\r\n}\r\n\r\n.history-time {\r\n  font-size: 12px;\r\n  color: #909399;\r\n}\r\n\r\n.capability-button {\r\n  transition: all 0.3s;\r\n}\r\n\r\n.capability-button.el-button--primary {\r\n  background-color: #409EFF;\r\n  border-color: #409EFF;\r\n  color: #fff;\r\n}\r\n\r\n.capability-button.el-button--info {\r\n  background-color: #fff;\r\n  border-color: #dcdfe6;\r\n  color: #606266;\r\n}\r\n\r\n.capability-button.el-button--info:hover {\r\n  color: #409EFF;\r\n  border-color: #c6e2ff;\r\n  background-color: #ecf5ff;\r\n}\r\n\r\n.capability-button.el-button--primary:hover {\r\n  background-color: #66b1ff;\r\n  border-color: #66b1ff;\r\n  color: #fff;\r\n}\r\n\r\n/* 分享内容样式 */\r\n.share-content {\r\n  margin-bottom: 20px;\r\n  padding: 15px 20px;\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  background-color: #fff;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: flex-start;\r\n  min-height: 600px;\r\n  max-height: 800px;\r\n  overflow: auto;\r\n}\r\n\r\n.share-image {\r\n  object-fit: contain;\r\n  display: block;\r\n}\r\n\r\n.share-pdf {\r\n  width: 100%;\r\n  height: 600px;\r\n  border: none;\r\n  border-radius: 4px;\r\n}\r\n\r\n.share-file {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  flex-direction: column;\r\n  color: #909399;\r\n}\r\n\r\n.single-image-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  width: 100%;\r\n  height: 80vh;\r\n}\r\n\r\n.single-image-container .large-image {\r\n  max-width: 100%;\r\n  max-height: 100%;\r\n  object-fit: contain;\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoQA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,KAAA,GAAAF,OAAA;AAGA,IAAAG,UAAA,GAAAC,sBAAA,CAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAD,sBAAA,CAAAJ,OAAA;AACA,IAAAM,SAAA,GAAAF,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAEA;EACAO,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,MAAA,EAAAC,cAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,EAAA;MACAC,MAAA,EAAAJ,cAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAG,OAAA;MACAC,MAAA,MAAAC,QAAA;MACAC,oBAAA;MACAC,WAAA;QACAC,UAAA;QACAX,MAAA;QACAK,MAAA;QACAO,MAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACAC,aAAA;QACAC,OAAA;QACAf,EAAA,MAAAI,QAAA;QACAY,MAAA;QACAC,MAAA;MACA;MACAC,MAAA,GACA;QACAxB,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,EACA;MACAG,WAAA;MACAC,WAAA;MACAC,QAAA;MACAC,WAAA;MACAC,OAAA;MACAC,eAAA;MACAC,eAAA;MAAA;MACAC,eAAA;MACAC,iBAAA;MACAC,UAAA;MACAC,eAAA,MAAAC,iBAAA;QACAC,YAAA;QACAC,cAAA;QACAC,WAAA;MACA;MACAC,kBAAA;MACAC,eAAA;MACAC,WAAA;MACAC,oBAAA;MACAC,WAAA;MACAC,aAAA;MAAA;MACAC,eAAA;IACA;EACA;EACAC,QAAA;IACAC,OAAA,WAAAA,QAAA;MACA,YAAAvB,WAAA,CAAAwB,IAAA,GAAAC,MAAA,aAAAnC,MAAA,CAAAoC,IAAA,WAAAC,EAAA;QAAA,OAAAA,EAAA,CAAAjC,OAAA;MAAA;IACA;IACAkC,QAAA,WAAAA,SAAA;MACA,YAAAZ,eAAA,CAAAS,MAAA,aAAAR,WAAA,CAAAO,IAAA,GAAAC,MAAA;IACA;IACAI,cAAA,WAAAA,eAAA;MAAA,IAAAC,KAAA;MACA,IAAAC,MAAA;MACA,IAAAC,UAAA;;MAEA;MACA,KAAAb,WAAA,CAAAc,OAAA,WAAAC,IAAA;QACA,KAAAF,UAAA,CAAAE,IAAA,CAAA3D,MAAA;UACAyD,UAAA,CAAAE,IAAA,CAAA3D,MAAA;QACA;QACAyD,UAAA,CAAAE,IAAA,CAAA3D,MAAA,EAAA4D,IAAA,CAAAD,IAAA;MACA;;MAEA;MACAE,MAAA,CAAAC,MAAA,CAAAL,UAAA,EAAAC,OAAA,WAAAK,SAAA;QACA;QACAA,SAAA,CAAAC,IAAA,WAAAC,CAAA,EAAAC,CAAA;UAAA,WAAAC,IAAA,CAAAF,CAAA,CAAAG,UAAA,QAAAD,IAAA,CAAAD,CAAA,CAAAE,UAAA;QAAA;;QAEA;QACA,IAAAC,UAAA,GAAAN,SAAA;QACA,IAAAO,IAAA,GAAAf,KAAA,CAAAgB,cAAA,CAAAF,UAAA,CAAAD,UAAA;QAEA,KAAAZ,MAAA,CAAAc,IAAA;UACAd,MAAA,CAAAc,IAAA;QACA;;QAEA;QACAd,MAAA,CAAAc,IAAA,EAAAV,IAAA,KAAAY,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAJ,UAAA;UACAK,QAAA;UACApD,UAAA,EAAAiC,KAAA,CAAArD,oBAAA,CAAAmE,UAAA,CAAArE,MAAA;UACA2E,QAAA,EAAAZ,SAAA,CAAAa,KAAA,IAAAC,GAAA,WAAAC,KAAA;YAAA,WAAAN,cAAA,CAAAC,OAAA,MAAAD,cAAA,CAAAC,OAAA,MACAK,KAAA;cACAJ,QAAA;YAAA;UAAA,CACA;QAAA,EACA;MACA;MAEA,OAAAlB,MAAA;IACA;EACA;EACAuB,OAAA,WAAAA,QAAA;IACAC,OAAA,CAAAC,GAAA,MAAAxF,MAAA;IACAuF,OAAA,CAAAC,GAAA,MAAAnF,MAAA;IACA,KAAAoF,aAAA,MAAAzF,MAAA;IACA,KAAA0F,eAAA;IACA,KAAAC,YAAA;EACA;EACAC,OAAA;IACAC,UAAA,WAAAA,WAAA;MAAA,IAAAC,MAAA;MACA,UAAAvC,OAAA;MAEA,KAAApB,WAAA;MACA;MACA,KAAAG,eAAA;MAEA,KAAAL,WAAA;MACA,KAAAG,OAAA;;MAEA,KAAA1B,WAAA,CAAAG,KAAA;MAGA,KAAAH,WAAA,CAAAE,MAAA,OAAAJ,QAAA;MACA,KAAAE,WAAA,CAAAV,MAAA,QAAAA,MAAA;MACA,KAAAU,WAAA,CAAAL,MAAA,QAAAA,MAAA;MACA,KAAAK,WAAA,CAAAC,UAAA,QAAAqB,WAAA;;MAEA;MACA,KAAAS,UAAA,QAAAnB,MAAA,CAAAyE,MAAA,WAAApC,EAAA;QAAA,OAAAA,EAAA,CAAAjC,OAAA;MAAA;;MAEA;MACA,KAAAe,UAAA,CAAAwB,OAAA,WAAAN,EAAA;QACAmC,MAAA,CAAAE,IAAA,CAAArC,EAAA;MACA;MAEA,KAAAlB,UAAA,CAAAwB,OAAA,WAAAN,EAAA;QACA,IAAAA,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA,IAAA8C,EAAA,CAAAlC,oBAAA,CAAAwE,QAAA;YACAH,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;UACA;QACA;QACA,IAAA8C,EAAA,CAAA7D,IAAA;UACAgG,MAAA,CAAApF,WAAA,CAAAG,KAAA,GAAAiF,MAAA,CAAApF,WAAA,CAAAG,KAAA;QACA;MACA;MAEA0E,OAAA,CAAAC,GAAA,aAAA9E,WAAA;;MAEA;MACA,KAAAQ,aAAA,CAAAE,MAAA;MACA,KAAAF,aAAA,CAAAG,MAAA,QAAAX,WAAA;MACA,KAAAwF,OAAA,MAAAhF,aAAA;MACA,KAAAR,WAAA,CAAAO,SAAA;IACA;IAEAiF,OAAA,WAAAA,QAAAnG,IAAA;MACA,IAAAmG,aAAA,EAAAnG,IAAA,EAAAoG,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAC,GAAA,CAAAC,SAAA;YACAC,KAAA,EAAAJ,GAAA,CAAAK,QAAA;YACAC,IAAA;YACAC,QAAA;UACA;QACA;MACA;IAEA;IACAC,gBAAA,WAAAA,iBAAAjD,EAAA,EAAAkD,eAAA;MACA,KAAAlD,EAAA,CAAAjC,OAAA;MAEA,IAAAoF,KAAA,GAAAnD,EAAA,CAAAlC,oBAAA,CAAAsF,OAAA,CAAAF,eAAA;MACAtB,OAAA,CAAAC,GAAA,SAAA7B,EAAA,CAAAlC,oBAAA;MACA,IAAAqF,KAAA;QACA;QACA,KAAAd,IAAA,CAAArC,EAAA,CAAAlC,oBAAA,EAAAkC,EAAA,CAAAlC,oBAAA,CAAAgC,MAAA,EAAAoD,eAAA;MACA;QACA;QACA,IAAAG,eAAA,OAAAC,mBAAA,CAAAjC,OAAA,EAAArB,EAAA,CAAAlC,oBAAA;QACAuF,eAAA,CAAAE,MAAA,CAAAJ,KAAA;QACA,KAAAd,IAAA,CAAArC,EAAA,0BAAAqD,eAAA;MACA;MACAzB,OAAA,CAAAC,GAAA,SAAA7B,EAAA,CAAAlC,oBAAA;MACA,KAAA0F,YAAA;IACA;IACAC,aAAA,WAAAA,cAAAzF,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IACA0F,aAAA,WAAAA,cAAA1F,MAAA;MACA,QAAAA,MAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MACA;IACA;IACA2F,cAAA,WAAAA,eAAAC,IAAA;MACA,WAAAC,cAAA,EAAAD,IAAA;IACA;IACA;IACAE,UAAA,WAAAA,WAAAC,IAAA;MACA,IAAAC,OAAA,GAAAC,QAAA,CAAAC,aAAA;MACAF,OAAA,CAAAG,SAAA,GAAAJ,IAAA;MACA,OAAAC,OAAA,CAAAI,WAAA,IAAAJ,OAAA,CAAAK,SAAA;IACA;IAEA;IACAC,cAAA,WAAAA,eAAAP,IAAA;MACA,YAAAhF,eAAA,CAAAwF,QAAA,CAAAR,IAAA;IACA;IAEAS,UAAA,WAAAA,WAAAC,OAAA;MACA;MACA,IAAAC,SAAA,QAAAZ,UAAA,CAAAW,OAAA;MACA,IAAAE,QAAA,GAAAV,QAAA,CAAAC,aAAA;MACAS,QAAA,CAAAvG,KAAA,GAAAsG,SAAA;MACAT,QAAA,CAAAW,IAAA,CAAAC,WAAA,CAAAF,QAAA;MACAA,QAAA,CAAAG,MAAA;MACAb,QAAA,CAAAc,WAAA;MACAd,QAAA,CAAAW,IAAA,CAAAI,WAAA,CAAAL,QAAA;MACA,KAAAM,QAAA,CAAAC,OAAA;IACA;IAEAC,YAAA,WAAAA,aAAAC,MAAA;MACA;MACA,IAAAC,QAAA,GAAAD,MAAA,CAAAX,OAAA;MACA,IAAAa,IAAA,OAAAC,IAAA,EAAAF,QAAA;QAAAG,IAAA;MAAA;MACA,IAAAC,IAAA,GAAAxB,QAAA,CAAAC,aAAA;MACAuB,IAAA,CAAAC,IAAA,GAAAC,GAAA,CAAAC,eAAA,CAAAN,IAAA;MACAG,IAAA,CAAAI,QAAA,MAAAC,MAAA,CAAAV,MAAA,CAAAW,MAAA,oBAAAD,MAAA,KAAA/E,IAAA,GAAAiF,WAAA,GAAAxE,KAAA;MACAiE,IAAA,CAAAQ,KAAA;MACAN,GAAA,CAAAO,eAAA,CAAAT,IAAA,CAAAC,IAAA;MACA,KAAAT,QAAA,CAAAC,OAAA;IACA;IAEAiB,YAAA,WAAAA,aAAAC,QAAA;MACA,IAAAA,QAAA;QACAC,MAAA,CAAAC,IAAA,CAAAF,QAAA;MACA;QACA,KAAAnB,QAAA,CAAAsB,OAAA;MACA;IACA;IACAC,cAAA,WAAAA,eAAAC,QAAA;MAAA,IAAAC,MAAA;MACA,KAAA7H,iBAAA,GAAA4H,QAAA;MACA,KAAA7H,eAAA;MACA;MACA,IAAA+H,YAAA,QAAAnI,WAAA,CAAA4E,OAAA,CAAAqD,QAAA;MACA,IAAAE,YAAA;QACA,KAAAC,SAAA;UACA,IAAAC,QAAA,GAAAH,MAAA,CAAAI,GAAA,CAAAC,aAAA;UACA,IAAAF,QAAA,IAAAA,QAAA,CAAAG,OAAA;YACAH,QAAA,CAAAG,OAAA,CAAAC,aAAA,CAAAN,YAAA;UACA;QACA;MACA;IACA;IACAO,eAAA,WAAAA,gBAAA;MACA,KAAAtI,eAAA;MACA,KAAAC,iBAAA;IACA;IACA;IACAiD,aAAA,WAAAA,cAAArF,EAAA;MAAA,IAAA0K,MAAA;MACA,IAAAC,KAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,cAAA,WAAAzB,MAAA,CAAArJ,EAAA;MACAmF,OAAA,CAAAC,GAAA,mBAAAwF,OAAA,CAAAC,GAAA,CAAAC,cAAA;MACAC,kBAAA,CAAAC,OAAA,CAAAL,KAAA,YAAAM,KAAA;QACA,QAAAA,KAAA,CAAAlC,IAAA;UACA;YACA;YACA;UACA;YACA2B,MAAA,CAAAQ,sBAAA,CAAAD,KAAA,CAAAtL,IAAA;YACA;UACA;YACA+K,MAAA,CAAAlC,QAAA,CAAAsB,OAAA;YACA;UACA;YACAY,MAAA,CAAAlC,QAAA,CAAA2C,KAAA;YACA;UACA;YACAT,MAAA,CAAAlC,QAAA,CAAA2C,KAAA;YACA;QACA;MACA;IACA;IAEAD,sBAAA,WAAAA,uBAAAvL,IAAA;MAEA,IAAAyL,OAAA,GAAAzL,IAAA;MACA,IAAA0L,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,OAAA;;MAEA;MACA,IAAAC,OAAA,CAAAtC,IAAA,6BAAAsC,OAAA,CAAAlL,MAAA;QACA,KAAAG,WAAA,CAAAI,UAAA,GAAA2K,OAAA,CAAAlL,MAAA;MACA,WAAAkL,OAAA,CAAAtC,IAAA,6BAAAsC,OAAA,CAAAlL,MAAA;QACA,KAAAG,WAAA,CAAAK,UAAA,GAAA0K,OAAA,CAAAlL,MAAA;MACA,WAAAkL,OAAA,CAAAtC,IAAA,2BAAAsC,OAAA,CAAAlL,MAAA;QACA,KAAAG,WAAA,CAAAM,QAAA,GAAAyK,OAAA,CAAAlL,MAAA;MACA;;MAEA;MACA,IAAAkL,OAAA,CAAAtC,IAAA,6BAAAsC,OAAA,CAAA/B,MAAA;QACA,IAAAkC,SAAA,QAAAnJ,UAAA,CAAAoJ,IAAA,WAAAlI,EAAA;UAAA,OAAAA,EAAA,CAAA7D,IAAA,KAAA2L,OAAA,CAAA/B,MAAA;QAAA;QACA,IAAAkC,SAAA;UACA;UACAA,SAAA,CAAAhK,YAAA,CAAAkK,OAAA;YACA1D,OAAA,EAAAqD,OAAA,CAAArD,OAAA;YACA2D,SAAA,MAAArH,IAAA;YACAsH,WAAA;UACA;QACA;QACA;MACA;;MAEA;MACA,IAAAP,OAAA,CAAAtC,IAAA,6BAAAsC,OAAA,CAAAQ,GAAA;QACA;QACA,KAAA9J,WAAA,CAAA2J,OAAA,CAAAL,OAAA,CAAAQ,GAAA;QACA;MACA;;MAEA;MACA,IAAAR,OAAA,CAAAtC,IAAA;QACA,IAAA+C,MAAA,QAAAzJ,UAAA,CAAAoJ,IAAA,WAAAlI,EAAA;UAAA,OAAAA,EAAA,CAAA7D,IAAA;QAAA;QACA,IAAAoM,MAAA;UACA,KAAAlG,IAAA,CAAAkG,MAAA;UACA,IAAAA,MAAA,CAAAtK,YAAA,CAAA6B,MAAA;YACA,KAAAuC,IAAA,CAAAkG,MAAA,CAAAtK,YAAA;UACA;UACA;UACA,KAAAQ,OAAA,CAAA0J,OAAA;YACApC,MAAA;YACAtB,OAAA,EAAAqD,OAAA,CAAAU,YAAA;YACApC,QAAA,EAAA0B,OAAA,CAAA1B,QAAA;YACAqC,WAAA,EAAAX,OAAA,CAAAW,WAAA;YACAL,SAAA,MAAArH,IAAA;UACA;UACA,KAAArC,eAAA;;UAEA;UACA,KAAAgK,WAAA;QACA;QACA;MACA;;MAEA;MACA,IAAAT,QAAA;MACA,QAAAH,OAAA,CAAAtC,IAAA;QACA;UACA5D,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA6L,QAAA,QAAAnJ,UAAA,CAAAoJ,IAAA,WAAAlI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA6L,QAAA,QAAAnJ,UAAA,CAAAoJ,IAAA,WAAAlI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA6L,QAAA,QAAAnJ,UAAA,CAAAoJ,IAAA,WAAAlI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA6L,QAAA,QAAAnJ,UAAA,CAAAoJ,IAAA,WAAAlI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA6L,QAAA,QAAAnJ,UAAA,CAAAoJ,IAAA,WAAAlI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;UACAyF,OAAA,CAAAC,GAAA,UAAAzF,IAAA;UACA6L,QAAA,QAAAnJ,UAAA,CAAAoJ,IAAA,WAAAlI,EAAA;YAAA,OAAAA,EAAA,CAAA7D,IAAA;UAAA;UACA;QACA;QACA;QACA;MACA;MAEA,IAAA8L,QAAA;QACA;QACA,KAAA5F,IAAA,CAAA4F,QAAA;;QAEA;QACA,IAAAA,QAAA,CAAAhK,YAAA,CAAA6B,MAAA;UACA,KAAAuC,IAAA,CAAA4F,QAAA,CAAAhK,YAAA;QACA;;QAEA;QACA,IAAA0K,WAAA,QAAAlK,OAAA,CAAAmK,SAAA,WAAAC,CAAA;UAAA,OAAAA,CAAA,CAAA9C,MAAA,KAAAkC,QAAA,CAAA9L,IAAA;QAAA;QACA,IAAAwM,WAAA;UACA,KAAAlK,OAAA,CAAA0J,OAAA;YACApC,MAAA,EAAAkC,QAAA,CAAA9L,IAAA;YACAsI,OAAA,EAAAqD,OAAA,CAAAU,YAAA;YACApC,QAAA,EAAA0B,OAAA,CAAA1B,QAAA;YACAqC,WAAA,EAAAX,OAAA,CAAAW,WAAA;YACAL,SAAA,MAAArH,IAAA;UACA;UACA,KAAArC,eAAA;QACA;UACA,KAAAD,OAAA,CAAA8E,MAAA,CAAAoF,WAAA;UACA,KAAAlK,OAAA,CAAA0J,OAAA;YACApC,MAAA,EAAAkC,QAAA,CAAA9L,IAAA;YACAsI,OAAA,EAAAqD,OAAA,CAAAU,YAAA;YACApC,QAAA,EAAA0B,OAAA,CAAA1B,QAAA;YACAqC,WAAA,EAAAX,OAAA,CAAAW,WAAA;YACAL,SAAA,MAAArH,IAAA;UACA;UACA,KAAArC,eAAA;QACA;QACA,KAAAgK,WAAA;MACA;;MAEA;MACA;MACA;MACA;;MAEA;MACA;MACA;IACA;IAEAI,cAAA,WAAAA,eAAA;MACAtB,kBAAA,CAAAuB,KAAA;IACA;IAEAC,WAAA,WAAAA,YAAA5M,IAAA;MAAA,IAAA6M,MAAA;MACA,IAAAzB,kBAAA,CAAA0B,IAAA,CAAA9M,IAAA;QACA;QACA,KAAAwK,SAAA;UACAqC,MAAA,CAAAE,cAAA;QACA;MACA;QACA,KAAAlE,QAAA,CAAA2C,KAAA;MACA;IACA;IACAwB,iBAAA,WAAAA,kBAAApJ,EAAA;MACA,KAAAqC,IAAA,CAAArC,EAAA,iBAAAA,EAAA,CAAA9B,UAAA;IACA;IAEAmL,UAAA,WAAAA,WAAAjB,SAAA;MACA,IAAAlH,IAAA,OAAAH,IAAA,CAAAqH,SAAA;MACA,OAAAlH,IAAA,CAAAoI,kBAAA;QACAC,IAAA;QACAC,MAAA;QACAC,MAAA;QACAC,MAAA;MACA;IACA;IACAC,eAAA,WAAAA,gBAAA;MACA,KAAAvK,kBAAA;MACA,KAAAC,eAAA;IACA;IAEAuK,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,UAAA5J,QAAA;;MAEA;MACA,IAAA6J,gBAAA,QAAArL,OAAA,CACA2D,MAAA,WAAAgD,MAAA;QAAA,OAAAyE,MAAA,CAAAxK,eAAA,CAAAiD,QAAA,CAAA8C,MAAA,CAAAW,MAAA;MAAA,GACAtE,GAAA,WAAA2D,MAAA;QACA;QACA,IAAA2E,YAAA,GAAAF,MAAA,CAAA/F,UAAA,CAAAsB,MAAA,CAAAX,OAAA;QACA,UAAAqB,MAAA,CAAAV,MAAA,CAAAW,MAAA,0BAAAD,MAAA,CAAAiE,YAAA;MACA,GACAC,IAAA;;MAEA;MACA,IAAAC,UAAA,MAAAnE,MAAA,MAAAxG,WAAA,QAAAwG,MAAA,CAAAgE,gBAAA;;MAEA;MACA,IAAAI,YAAA;QACA1M,OAAA;QACAf,EAAA,MAAAI,QAAA;QACAY,MAAA;QACAC,MAAA;UACAT,MAAA,MAAAJ,QAAA;UACAR,MAAA,OAAAA,MAAA;UACAK,MAAA,OAAAA,MAAA;UACAM,UAAA,EAAAiN,UAAA;UACA/M,KAAA;QACA;MACA;;MAEA;MACA0E,OAAA,CAAAC,GAAA,OAAAqI,YAAA;MACA,KAAA3H,OAAA,CAAA2H,YAAA;MACA,KAAA9K,kBAAA;;MAEA;MACA,IAAAmJ,MAAA;QACApM,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA,GACA;UACAwG,OAAA;UACA2D,SAAA,MAAArH,IAAA;UACAsH,WAAA;UACA7C,IAAA;QACA,EACA;QACAtH,UAAA;MACA;;MAEA;MACA,IAAAiM,UAAA,QAAArL,UAAA,CAAA8J,SAAA,WAAA5I,EAAA;QAAA,OAAAA,EAAA,CAAA7D,IAAA;MAAA;MACA,IAAAgO,UAAA;QACA;QACA,KAAArL,UAAA,CAAAqJ,OAAA,CAAAI,MAAA;MACA;QACA;QACA,KAAAzJ,UAAA,CAAAqL,UAAA,IAAA5B,MAAA;QACA;QACA,IAAA6B,IAAA,QAAAtL,UAAA,CAAAyE,MAAA,CAAA4G,UAAA;QACA,KAAArL,UAAA,CAAAqJ,OAAA,CAAAiC,IAAA;MACA;MAEA,KAAA5G,YAAA;MACA,KAAAyB,QAAA,CAAAC,OAAA;IACA;IACA;IACAmF,iBAAA,WAAAA,kBAAA;MACA,KAAA9K,oBAAA;MACA,KAAAwC,eAAA;IACA;IAEA;IACAuI,wBAAA,WAAAA,yBAAA;MACA,KAAA/K,oBAAA;IACA;IAEA;IACAwC,eAAA,WAAAA,gBAAAwI,KAAA;MAAA,IAAAC,MAAA;MAAA,WAAAC,kBAAA,CAAApJ,OAAA,mBAAAqJ,aAAA,CAAArJ,OAAA,IAAAsJ,CAAA,UAAAC,QAAA;QAAA,IAAAnI,GAAA,EAAAoI,EAAA;QAAA,WAAAH,aAAA,CAAArJ,OAAA,IAAAyJ,CAAA,WAAAC,QAAA;UAAA,kBAAAA,QAAA,CAAAC,CAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAF,QAAA,CAAAC,CAAA;cAAA,OAEA,IAAAE,oBAAA,EAAAV,MAAA,CAAAnO,MAAA,EAAAkO,KAAA;YAAA;cAAA9H,GAAA,GAAAsI,QAAA,CAAAI,CAAA;cACA,IAAA1I,GAAA,CAAAC,IAAA;gBACA8H,MAAA,CAAAhL,WAAA,GAAAiD,GAAA,CAAArG,IAAA;cACA;cAAA2O,QAAA,CAAAC,CAAA;cAAA;YAAA;cAAAD,QAAA,CAAAE,CAAA;cAAAJ,EAAA,GAAAE,QAAA,CAAAI,CAAA;cAEAvJ,OAAA,CAAAgG,KAAA,cAAAiD,EAAA;cACAL,MAAA,CAAAvF,QAAA,CAAA2C,KAAA;YAAA;cAAA,OAAAmD,QAAA,CAAAlK,CAAA;UAAA;QAAA,GAAA+J,OAAA;MAAA;IAEA;IAEA;IACAQ,iBAAA,WAAAA,kBAAAhD,SAAA;MACA,IAAAlH,IAAA,OAAAH,IAAA,CAAAqH,SAAA;MACA,OAAAlH,IAAA,CAAAoI,kBAAA;QACAC,IAAA;QACAC,MAAA;QACAE,MAAA;MACA;IACA;IAEA;IACAvI,cAAA,WAAAA,eAAAiH,SAAA;MACA,IAAAlH,IAAA,OAAAH,IAAA,CAAAqH,SAAA;MACA,IAAAiD,KAAA,OAAAtK,IAAA;MACA,IAAAuK,SAAA,OAAAvK,IAAA,CAAAsK,KAAA;MACAC,SAAA,CAAAC,OAAA,CAAAD,SAAA,CAAAE,OAAA;MACA,IAAAC,UAAA,OAAA1K,IAAA,CAAAsK,KAAA;MACAI,UAAA,CAAAF,OAAA,CAAAE,UAAA,CAAAD,OAAA;MACA,IAAAE,YAAA,OAAA3K,IAAA,CAAAsK,KAAA;MACAK,YAAA,CAAAH,OAAA,CAAAG,YAAA,CAAAF,OAAA;MAEA,IAAAtK,IAAA,CAAAyK,YAAA,OAAAN,KAAA,CAAAM,YAAA;QACA;MACA,WAAAzK,IAAA,CAAAyK,YAAA,OAAAL,SAAA,CAAAK,YAAA;QACA;MACA,WAAAzK,IAAA,CAAAyK,YAAA,OAAAF,UAAA,CAAAE,YAAA;QACA;MACA,WAAAzK,IAAA,CAAAyK,YAAA,OAAAD,YAAA,CAAAC,YAAA;QACA;MACA;QACA,OAAAzK,IAAA,CAAA0K,kBAAA;UACAC,IAAA;UACAC,KAAA;UACAC,GAAA;QACA;MACA;IACA;IAEA;IACAC,eAAA,WAAAA,gBAAAzL,IAAA;MACA;QACA,IAAA0L,WAAA,GAAAlE,IAAA,CAAAC,KAAA,CAAAzH,IAAA,CAAAnE,IAAA;QACA;QACA,KAAAuB,MAAA,GAAAsO,WAAA,CAAAtO,MAAA,SAAAA,MAAA;QACA;QACA,KAAAU,WAAA,GAAA4N,WAAA,CAAA5N,WAAA;QACA;QACA,KAAAS,UAAA,GAAAmN,WAAA,CAAAnN,UAAA;QACA;QACA,KAAAN,WAAA,GAAAyN,WAAA,CAAAzN,WAAA;QACA;QACA,KAAAC,OAAA,GAAAwN,WAAA,CAAAxN,OAAA;QACA;QACA,KAAA7B,MAAA,GAAA2D,IAAA,CAAA3D,MAAA,SAAAA,MAAA;QACA,KAAAG,WAAA,CAAAI,UAAA,GAAAoD,IAAA,CAAApD,UAAA;QACA,KAAAJ,WAAA,CAAAK,UAAA,GAAAmD,IAAA,CAAAnD,UAAA;QACA,KAAAL,WAAA,CAAAM,QAAA,GAAAkD,IAAA,CAAAlD,QAAA;QACA,KAAAN,WAAA,CAAAO,SAAA;;QAEA;QACA,KAAAqB,eAAA;QACA,KAAAL,WAAA;QAEA,KAAA2G,QAAA,CAAAC,OAAA;QACA,KAAA3F,oBAAA;MACA,SAAAqI,KAAA;QACAhG,OAAA,CAAAgG,KAAA,cAAAA,KAAA;QACA,KAAA3C,QAAA,CAAA2C,KAAA;MACA;IACA;IAEA;IACAc,WAAA,WAAAA,YAAA;MAAA,IAAAwD,MAAA;MAAA,WAAAzB,kBAAA,CAAApJ,OAAA,mBAAAqJ,aAAA,CAAArJ,OAAA,IAAAsJ,CAAA,UAAAwB,SAAA;QAAA,IAAAF,WAAA,EAAAG,GAAA;QAAA,WAAA1B,aAAA,CAAArJ,OAAA,IAAAyJ,CAAA,WAAAuB,SAAA;UAAA,kBAAAA,SAAA,CAAArB,CAAA;YAAA;cACA;cACA;cACA;cAEAiB,WAAA;gBACAtO,MAAA,EAAAuO,MAAA,CAAAvO,MAAA;gBACAU,WAAA,EAAA6N,MAAA,CAAA7N,WAAA;gBACAS,UAAA,EAAAoN,MAAA,CAAApN,UAAA;gBACAN,WAAA,EAAA0N,MAAA,CAAA1N,WAAA;gBACAC,OAAA,EAAAyN,MAAA,CAAAzN,OAAA;gBACA7B,MAAA,EAAAsP,MAAA,CAAAtP,MAAA;gBACAO,UAAA,EAAA+O,MAAA,CAAAnP,WAAA,CAAAI,UAAA;gBACAC,UAAA,EAAA8O,MAAA,CAAAnP,WAAA,CAAAK,UAAA;gBACAC,QAAA,EAAA6O,MAAA,CAAAnP,WAAA,CAAAM;cACA;cAAAgP,SAAA,CAAApB,CAAA;cAAAoB,SAAA,CAAArB,CAAA;cAAA,OAGA,IAAAsB,sBAAA;gBACAjQ,MAAA,EAAA6P,MAAA,CAAA7P,MAAA;gBACAW,UAAA,EAAAkP,MAAA,CAAA7N,WAAA;gBACAjC,IAAA,EAAA2L,IAAA,CAAAwE,SAAA,CAAAN,WAAA;gBACArP,MAAA,EAAAsP,MAAA,CAAAtP,MAAA;gBACAO,UAAA,EAAA+O,MAAA,CAAAnP,WAAA,CAAAI,UAAA;gBACAC,UAAA,EAAA8O,MAAA,CAAAnP,WAAA,CAAAK,UAAA;gBACAC,QAAA,EAAA6O,MAAA,CAAAnP,WAAA,CAAAM;cACA;YAAA;cAAAgP,SAAA,CAAArB,CAAA;cAAA;YAAA;cAAAqB,SAAA,CAAApB,CAAA;cAAAmB,GAAA,GAAAC,SAAA,CAAAlB,CAAA;cAEAvJ,OAAA,CAAAgG,KAAA,cAAAwE,GAAA;cACAF,MAAA,CAAAjH,QAAA,CAAA2C,KAAA;YAAA;cAAA,OAAAyE,SAAA,CAAAxL,CAAA;UAAA;QAAA,GAAAsL,QAAA;MAAA;IAEA;IAEA;IACAK,sBAAA,WAAAA,uBAAAjM,IAAA;MACA,KAAA8B,IAAA,MAAAvF,oBAAA,EAAAyD,IAAA,CAAA3D,MAAA,QAAAE,oBAAA,CAAAyD,IAAA,CAAA3D,MAAA;IACA;IAEA;IACA6P,aAAA,WAAAA,cAAA;MACA;MACA,KAAA7P,MAAA,OAAAC,QAAA;MACA,KAAAS,SAAA;MACA,KAAAe,WAAA;MACA,KAAAC,WAAA;MACA,KAAAE,WAAA;MACA,KAAAC,OAAA;MACA,KAAAK,UAAA;MACA,KAAA/B,WAAA;QACAC,UAAA;QACAX,MAAA,OAAAA,MAAA;QACAK,MAAA,OAAAA,MAAA;QACAO,MAAA;QACAC,KAAA;QACAC,UAAA;QACAC,UAAA;QACAC,QAAA;QACAC,SAAA;MACA;MACA;MACA,KAAAK,MAAA,IACA;QACAxB,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA;QACAC,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,GACA;QACA/B,IAAA;QACAyB,MAAA,EAAAhC,OAAA;QACAiC,YAAA,GACA;UAAAM,KAAA;UAAAC,KAAA;QAAA,GACA;UAAAD,KAAA;UAAAC,KAAA;QAAA,EACA;QACAN,oBAAA;QACAC,OAAA;QACAC,MAAA;QACAC,YAAA;QACAC,UAAA;MACA,EACA;MACA;MACA,KAAAS,eAAA;MAEA,KAAAsG,QAAA,CAAAC,OAAA;IACA;IAEA;IACAlD,YAAA,WAAAA,aAAA;MAAA,IAAA0K,MAAA;MAAA,WAAAjC,kBAAA,CAAApJ,OAAA,mBAAAqJ,aAAA,CAAArJ,OAAA,IAAAsJ,CAAA,UAAAgC,SAAA;QAAA,IAAAlK,GAAA,EAAAmK,QAAA,EAAAC,GAAA;QAAA,WAAAnC,aAAA,CAAArJ,OAAA,IAAAyJ,CAAA,WAAAgC,SAAA;UAAA,kBAAAA,SAAA,CAAA9B,CAAA;YAAA;cAAA8B,SAAA,CAAA7B,CAAA;cAAA6B,SAAA,CAAA9B,CAAA;cAAA,OAEA,IAAAE,oBAAA,EAAAwB,MAAA,CAAArQ,MAAA;YAAA;cAAAoG,GAAA,GAAAqK,SAAA,CAAA3B,CAAA;cACA,IAAA1I,GAAA,CAAAC,IAAA,YAAAD,GAAA,CAAArG,IAAA,IAAAqG,GAAA,CAAArG,IAAA,CAAA0D,MAAA;gBACA;gBACA8M,QAAA,GAAAnK,GAAA,CAAArG,IAAA;gBACAsQ,MAAA,CAAAV,eAAA,CAAAY,QAAA;cACA;cAAAE,SAAA,CAAA9B,CAAA;cAAA;YAAA;cAAA8B,SAAA,CAAA7B,CAAA;cAAA4B,GAAA,GAAAC,SAAA,CAAA3B,CAAA;cAEAvJ,OAAA,CAAAgG,KAAA,cAAAiF,GAAA;YAAA;cAAA,OAAAC,SAAA,CAAAjM,CAAA;UAAA;QAAA,GAAA8L,QAAA;MAAA;IAEA;IAEA;IACAI,WAAA,WAAAA,YAAAzE,GAAA;MACA,KAAAA,GAAA;MACA,IAAA0E,eAAA;MACA,IAAAC,QAAA,GAAA3E,GAAA,CAAA4E,WAAA;MACA,OAAAF,eAAA,CAAAjN,IAAA,WAAAoN,GAAA;QAAA,OAAAF,QAAA,CAAA3K,QAAA,CAAA6K,GAAA;MAAA;IACA;IAEA;IACAC,SAAA,WAAAA,UAAA9E,GAAA;MACA,KAAAA,GAAA;MACA,OAAAA,GAAA,CAAA4E,WAAA,GAAA5K,QAAA;IACA;IAEA;IACA+K,aAAA,WAAAA,cAAAtH,MAAA;MACA,IAAAuH,QAAA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MAEA,IAAAC,KAAA,GAAAD,QAAA,CAAAvH,MAAA;;MAEA;QACAwH,KAAA,EAAAA,KAAA;QACAC,MAAA;MACA;IACA;IAEA;IACAC,kBAAA,WAAAA,mBAAArI,MAAA;MAAA,IAAAsI,MAAA;MACA,SAAAhO,eAAA;;MAEA,KAAAA,eAAA;MACA,KAAAD,aAAA;;MAEA,IAAA/B,MAAA;QACAiQ,WAAA,EAAAvI,MAAA,CAAAX,OAAA;QACA2B,QAAA,EAAAhB,MAAA,CAAAgB,QAAA;QACA/J,MAAA,OAAAA,MAAA;QACAuR,GAAA,OAAAnO,aAAA;QACAsG,MAAA,EAAAX,MAAA,CAAAW;MACA;MAEA,IAAA8H,oBAAA,EAAAnQ,MAAA,EAAA8E,IAAA,WAAAC,GAAA;QACA,IAAAA,GAAA,CAAAC,IAAA;UACAgL,MAAA,CAAAzI,QAAA,CAAAC,OAAA;QACA;UACAwI,MAAA,CAAAzI,QAAA,CAAA2C,KAAA,CAAAnF,GAAA,CAAAqL,GAAA;QACA;MACA,GAAAC,KAAA,WAAAnG,KAAA;QACAhG,OAAA,CAAAgG,KAAA,cAAAA,KAAA;QACA8F,MAAA,CAAAzI,QAAA,CAAA2C,KAAA;MACA,GAAAoG,OAAA;QACAN,MAAA,CAAAhO,eAAA;MACA;IACA;EAGA;AACA", "ignoreList": []}]}