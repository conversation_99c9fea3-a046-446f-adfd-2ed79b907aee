<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="f0b922d8-7e98-49ff-86f3-79b2a3bdd18d" name="更改" comment="">
      <change beforePath="$PROJECT_DIR$/cube-admin/src/main/java/com/cube/web/controller/common/CommonController.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-admin/src/main/java/com/cube/web/controller/common/CommonController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-admin/src/main/java/com/cube/wechat/selfapp/app/config/MyWebSocketHandler.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-admin/src/main/java/com/cube/wechat/selfapp/app/config/MyWebSocketHandler.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-admin/src/main/java/com/cube/wechat/selfapp/app/service/impl/AppLoginServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-admin/src/main/java/com/cube/wechat/selfapp/app/service/impl/AppLoginServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-admin/src/main/resources/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/cube-admin/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-admin/target/classes/application.yml" beforeDir="false" afterPath="$PROJECT_DIR$/cube-admin/target/classes/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/controller/AIGCController.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/controller/AIGCController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/controller/BrowserController.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/controller/BrowserController.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/controller/StartupRunner.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/controller/StartupRunner.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/entity/UserInfoRequest.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/entity/UserInfoRequest.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/websocket/WebSocketClientService.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/src/main/java/com/playwright/websocket/WebSocketClientService.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-engine/src/main/resources/application.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/cube-engine/src/main/resources/application.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-framework/src/main/java/com/cube/framework/config/ResourcesConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-framework/src/main/java/com/cube/framework/config/ResourcesConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-framework/src/main/java/com/cube/framework/config/SecurityConfig.java" beforeDir="false" afterPath="$PROJECT_DIR$/cube-framework/src/main/java/com/cube/framework/config/SecurityConfig.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/api/login.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/api/login.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/config.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/manifest.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/manifest.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/pages/login/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/pages/login/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/pages/work/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/pages/work/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/permission.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/permission.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/common/main.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/common/main.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/common/runtime.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/common/runtime.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/common/vendor.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/common/vendor.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/login/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/avatar/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/avatar/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/info/edit.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/info/edit.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/info/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/mine/info/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/user/points/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/user/points/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/work/index.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/pages/work/index.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-badge/components/uni-badge/uni-badge.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-badge/components/uni-badge/uni-badge.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-forms/components/uni-forms/uni-forms.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-forms/components/uni-forms/uni-forms.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-list/components/uni-list-item/uni-list-item.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-list/components/uni-list-item/uni-list-item.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-list/components/uni-list/uni-list.js.map" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/.sourcemap/mp-weixin/uni_modules/uni-list/components/uni-list/uni-list.js.map" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/app.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/app.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/app.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/app.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/app.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/app.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/common/main.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/common/main.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/common/main.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/common/main.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/common/runtime.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/common/runtime.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/common/vendor.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/common/vendor.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/index.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/index.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/index.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/login/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/login/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/login/index.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/login/index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/login/index.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/login/index.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/avatar/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/avatar/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/avatar/index.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/avatar/index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/avatar/index.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/avatar/index.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/index.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/index.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/index.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/edit.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/edit.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/edit.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/edit.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/edit.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/edit.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/index.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/index.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/mine/info/index.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/user/points/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/user/points/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/user/points/index.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/user/points/index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/user/points/index.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/user/points/index.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/work/index.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/work/index.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/work/index.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/work/index.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/work/index.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/pages/work/index.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/project.config.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/project.config.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/sitemap.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-badge/components/uni-badge/uni-badge.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-badge/components/uni-badge/uni-badge.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-badge/components/uni-badge/uni-badge.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-badge/components/uni-badge/uni-badge.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-badge/components/uni-badge/uni-badge.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-badge/components/uni-badge/uni-badge.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-easyinput/components/uni-easyinput/uni-easyinput.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms-item/uni-forms-item.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms/uni-forms.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms/uni-forms.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms/uni-forms.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms/uni-forms.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms/uni-forms.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-forms/components/uni-forms/uni-forms.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-icons/components/uni-icons/uni-icons.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list-item/uni-list-item.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list-item/uni-list-item.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list-item/uni-list-item.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list-item/uni-list-item.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list-item/uni-list-item.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list-item/uni-list-item.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list/uni-list.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list/uni-list.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list/uni-list.json" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list/uni-list.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list/uni-list.wxss" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/unpackage/dist/dev/mp-weixin/uni_modules/uni-list/components/uni-list/uni-list.wxss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-mini/utils/request.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-mini/utils/request.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-ui/src/views/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cube-ui/src/views/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-ui/src/views/wechat/chrome/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/cube-ui/src/views/wechat/chrome/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/cube-ui/vue.config.js" beforeDir="false" afterPath="$PROJECT_DIR$/cube-ui/vue.config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/sql/ucube.sql" beforeDir="false" afterPath="$PROJECT_DIR$/sql/ucube.sql" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2zUPFpYW1Lp4hbwQDVrxxtOVEiq" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Maven.cube [clean].executor&quot;: &quot;Run&quot;,
    &quot;Maven.cube [package].executor&quot;: &quot;Run&quot;,
    &quot;Maven.cube-admin [clean].executor&quot;: &quot;Run&quot;,
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;Spring Boot.App.executor&quot;: &quot;Run&quot;,
    &quot;Spring Boot.CubeApplication.executor&quot;: &quot;Run&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/JavaWorkSpace/U3W-AI&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;模块&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.lookFeel&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Spring Boot.App">
    <configuration name="App" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="cube-engine" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.playwright.App" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="CubeApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <option name="FRAME_DEACTIVATION_UPDATE_POLICY" value="UpdateClassesAndResources" />
      <module name="cube-admin" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.cube.CubeApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="f0b922d8-7e98-49ff-86f3-79b2a3bdd18d" name="更改" comment="" />
      <created>1751782577516</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751782577516</updated>
      <workItem from="1751782579565" duration="7035000" />
      <workItem from="1751792197595" duration="211000" />
      <workItem from="1751792413403" duration="141000" />
      <workItem from="1751792558081" duration="4619000" />
      <workItem from="1751797966192" duration="179000" />
      <workItem from="1751798149712" duration="105000" />
      <workItem from="1751798258040" duration="11142000" />
      <workItem from="1751854125590" duration="16949000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>