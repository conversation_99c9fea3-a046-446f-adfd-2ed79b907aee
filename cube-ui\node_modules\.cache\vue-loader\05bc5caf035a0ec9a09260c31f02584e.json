{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\index.vue", "mtime": 1751901200955}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751784291203}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgUGFuZWxHcm91cCBmcm9tICcuL2Rhc2hib2FyZC9QYW5lbEdyb3VwJw0KaW1wb3J0IExpbmVDaGFydCBmcm9tICcuL2Rhc2hib2FyZC9MaW5lQ2hhcnQnDQppbXBvcnQgUmFkZGFyQ2hhcnQgZnJvbSAnLi9kYXNoYm9hcmQvUmFkZGFyQ2hhcnQnDQppbXBvcnQgUGllQ2hhcnQgZnJvbSAnLi9kYXNoYm9hcmQvUGllQ2hhcnQnDQppbXBvcnQgQmFyQ2hhcnQgZnJvbSAnLi9kYXNoYm9hcmQvQmFyQ2hhcnQnDQppbXBvcnQgdXNlckF2YXRhciBmcm9tICJAL3ZpZXdzL3N5c3RlbS91c2VyL3Byb2ZpbGUvdXNlckF2YXRhciI7DQppbXBvcnQgdXNlckluZm8gZnJvbSAiQC92aWV3cy9zeXN0ZW0vdXNlci9wcm9maWxlL3VzZXJJbmZvIjsNCmltcG9ydCByZXNldFB3ZCBmcm9tICJAL3ZpZXdzL3N5c3RlbS91c2VyL3Byb2ZpbGUvcmVzZXRQd2QiOw0KaW1wb3J0IHtnZXRVc2VyUHJvZmlsZSwgYmluZFdjT2ZmaWNlQWNjb3VudCwgZ2V0T2ZmaWNlQWNjb3VudCwgZ2V0QWdlbnRCaW5kLCBzYXZlQWdlbnRCaW5kLCBzYXZlU3BhY2VCaW5kLCBnZXRTcGFjZUluZm9CeVVzZXJJZH0gZnJvbSAiQC9hcGkvc3lzdGVtL3VzZXIiOw0KaW1wb3J0IHtnZXRVc2VyUG9pbnRzUmVjb3JkIH0gZnJvbSAiQC9hcGkvd2VjaGF0L2NvbXBhbnkiOw0KaW1wb3J0IHdlYnNvY2tldENsaWVudCBmcm9tICdAL3V0aWxzL3dlYnNvY2tldCc7DQoNCmNvbnN0IGxpbmVDaGFydERhdGEgPSB7DQogIG5ld1Zpc2l0aXM6IHsNCiAgICBleHBlY3RlZERhdGE6IFsxMDAsIDEyMCwgMTYxLCAxMzQsIDEwNSwgMTYwLCAxNjVdLA0KICAgIGFjdHVhbERhdGE6IFsxMjAsIDgyLCA5MSwgMTU0LCAxNjIsIDE0MCwgMTQ1XQ0KICB9LA0KICBtZXNzYWdlczogew0KICAgIGV4cGVjdGVkRGF0YTogWzIwMCwgMTkyLCAxMjAsIDE0NCwgMTYwLCAxMzAsIDE0MF0sDQogICAgYWN0dWFsRGF0YTogWzE4MCwgMTYwLCAxNTEsIDEwNiwgMTQ1LCAxNTAsIDEzMF0NCiAgfSwNCiAgcHVyY2hhc2VzOiB7DQogICAgZXhwZWN0ZWREYXRhOiBbODAsIDEwMCwgMTIxLCAxMDQsIDEwNSwgOTAsIDEwMF0sDQogICAgYWN0dWFsRGF0YTogWzEyMCwgOTAsIDEwMCwgMTM4LCAxNDIsIDEzMCwgMTMwXQ0KICB9LA0KICBzaG9wcGluZ3M6IHsNCiAgICBleHBlY3RlZERhdGE6IFsxMzAsIDE0MCwgMTQxLCAxNDIsIDE0NSwgMTUwLCAxNjBdLA0KICAgIGFjdHVhbERhdGE6IFsxMjAsIDgyLCA5MSwgMTU0LCAxNjIsIDE0MCwgMTMwXQ0KICB9DQp9DQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogJ0luZGV4JywNCiAgY29tcG9uZW50czogew0KICAgIFBhbmVsR3JvdXAsDQogICAgTGluZUNoYXJ0LA0KICAgIFJhZGRhckNoYXJ0LA0KICAgIFBpZUNoYXJ0LA0KICAgIEJhckNoYXJ0LHVzZXJBdmF0YXIsIHVzZXJJbmZvLCByZXNldFB3ZA0KICB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBsaW5lQ2hhcnREYXRhOiBsaW5lQ2hhcnREYXRhLm5ld1Zpc2l0aXMsDQogICAgICB1c2VyOiB7fSwNCiAgICAgIHJvbGVHcm91cDoge30sDQogICAgICBwb3N0R3JvdXA6IHt9LA0KICAgICAgYWN0aXZlVGFiOiAidXNlcmluZm8iLA0KICAgICAgLy8tLS0tLS0g57uR5a6a5YWs5LyX5Y+355u45YWz5Y+Y6YePIC0tLS0tLS8vDQogICAgICBkaWFsb2dGb3JtVmlzaWJsZTogZmFsc2UsIC8vIOe7keWumuWFrOS8l+WPt+W8ueeqlw0KICAgICAgZGlhbG9nQWdlbnRGb3JtVmlzaWJsZTogZmFsc2UsIC8vIOe7keWumuaZuuiDveS9k+W8ueeqlw0KICAgICAgZGlhbG9nU3BhY2VGb3JtVmlzaWJsZTogZmFsc2UsIC8vIOe7keWumuWFg+WZqOepuumXtOW8ueeqlw0KICAgICAgZm9ybTogew0KICAgICAgICBhcHBJZDogJycsIC8vIOWFrOS8l+WPt2FwcElkDQogICAgICAgIGFwcFNlY3JldDogJycsIC8vIOWFrOS8l+WPt2FwcFNlY3JldA0KICAgICAgICBvZmZpY2VBY2NvdW50TmFtZTogJycsIC8vIOWFrOS8l+WPt+WQjeensA0KICAgICAgICBwaWNVcmw6ICcnLCAvLyDlhazkvJflj7flsIHpnaLlm74NCiAgICAgIH0sDQogICAgICBhZ2VudEZvcm06IHsNCiAgICAgICAgYWdlbnRJZDogJycsIC8vIOaZuuiDveS9k0lEDQogICAgICAgIGFnZW50VG9rZW46ICcnLCAvLyDmmbrog73kvZN0b2tlbg0KICAgICAgfSwNCiAgICAgIHNwYWNlRm9ybTogew0KICAgICAgICBzcGFjZUlkOiAnJywgLy8g56m66Ze0SUQNCiAgICAgICAgc3BhY2VOYW1lOiAnJywgLy8g56m66Ze05ZCN56ewDQogICAgICB9LA0KICAgICAgZm9ybUxhYmVsV2lkdGg6ICcxMjBweCcsIC8v6L6T5YWl5qGG5a695bqmDQogICAgICAvLyDnu5HlrprlhazkvJflj7fooajljZXpqozor4Hop4TliJkNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGFwcElkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpWFwcElkJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgYXBwU2VjcmV0OiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpWFwcFNlY3JldCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0sDQogICAgICAgIG9mZmljZUFjY291bnROYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogZmFsc2UsIG1lc3NhZ2U6ICfor7fovpPlhaXlhazkvJflj7flkI3np7AnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgYWdlbnRydWxlczogew0KICAgICAgICBhZ2VudElkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpWFnZW50SWQnLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdLA0KICAgICAgICBhZ2VudFRva2VuOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpWFnZW50VG9rZW4nLCB0cmlnZ2VyOiAnYmx1cicgfQ0KICAgICAgICBdDQogICAgICB9LA0KICAgICAgc3BhY2VydWxlczogew0KICAgICAgICBzcGFjZUlkOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeepuumXtElEJywgdHJpZ2dlcjogJ2JsdXInIH0NCiAgICAgICAgXSwNCiAgICAgICAgc3BhY2VOYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogJ+ivt+i+k+WFpeepuumXtOWQjeensCcsIHRyaWdnZXI6ICdibHVyJyB9DQogICAgICAgIF0NCiAgICAgIH0sDQoNCiAgICAgIC8vLS0tLS0tIOenr+WIhuebuOWFs+WPmOmHjyAtLS0tLS0vLw0KICAgICAgbG9hZGluZzogdHJ1ZSwgLy8g6YGu572p5bGCDQogICAgICBjaGFuZ2VUeXBlOlsNCiAgICAgICAgew0KICAgICAgICAgIGxhYmVsOiLlhajpg6giLA0KICAgICAgICAgIHZhbHVlOiIwIg0KICAgICAgICB9LA0KICAgICAgICB7DQogICAgICAgIGxhYmVsOiLlop7liqAiLA0KICAgICAgICB2YWx1ZToiMSINCiAgICAgIH0sDQogICAgICAgIHsNCiAgICAgICAgICBsYWJlbDoi5raI6ICXIiwNCiAgICAgICAgICB2YWx1ZToiMiINCiAgICAgICAgfSwNCiAgICAgIF0sICAvLyDnp6/liIbmmI7nu4booajkuK3nmoTnp6/liIbnsbvlnosNCiAgICAgIG9wZW5Qb2ludHNSZWNvcmQ6IGZhbHNlLCAvLyDnp6/liIbmmI7nu4blvLnnqpcNCiAgICAgIHBvaW50dG90YWw6IDAsIC8vIOenr+WIhuaYjue7huaAu+aVsA0KICAgICAgcXVlcnlQb2ludEZvcm06ew0KICAgICAgICBsaW1pdDoxMCwNCiAgICAgICAgcGFnZToxLA0KICAgICAgICB0eXBlOicnLA0KICAgICAgICB1c2VySWQ6JycNCiAgICAgIH0sIC8vIOenr+WIhuaYjue7huafpeivoumcgOimgeeahOafpeivouWPguaVsA0KICAgICAgcG9pbnRzUmVjb3JkTGlzdDogbnVsbCwgLy8g56ev5YiG5piO57uG5YiX6KGoDQoNCiAgICAgIC8vLS0tLS0tIOetvuWIsOebuOWFs+WPmOmHjyAtLS0tLS0vLw0KICAgICAgd2Vla0RheXM6IFsn5ZGo5pelJywgJ+WRqOS4gCcsICflkajkuownLCAn5ZGo5LiJJywgJ+WRqOWbmycsICflkajkupQnLCAn5ZGo5YWtJ10sDQogICAgICBjdXJyZW50WWVhcjogbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpLA0KICAgICAgY3VycmVudE1vbnRoOiBuZXcgRGF0ZSgpLmdldE1vbnRoKCkgKyAxLA0KICAgICAgY29udGludW91c1NpZ25JbkRheXM6IDcsDQogICAgICBtb250aGx5U2lnbkluRGF5czogMTUsDQogICAgICB0b3RhbFNpZ25JbkRheXM6IDEyOCwNCiAgICAgIHRvZGF5U2lnbmVkSW46IGZhbHNlLA0KICAgICAgc2lnbkluSGlzdG9yeTogWw0KICAgICAgICB7IGRhdGU6ICcyMDI0LTAxLTE1JywgdGltZTogJzA4OjMwOjI1JyB9LA0KICAgICAgICB7IGRhdGU6ICcyMDI0LTAxLTE0JywgdGltZTogJzA5OjE1OjMzJyB9LA0KICAgICAgICB7IGRhdGU6ICcyMDI0LTAxLTEzJywgdGltZTogJzA3OjQ1OjEyJyB9LA0KICAgICAgICB7IGRhdGU6ICcyMDI0LTAxLTEyJywgdGltZTogJzA4OjIwOjQ1JyB9LA0KICAgICAgICB7IGRhdGU6ICcyMDI0LTAxLTExJywgdGltZTogJzA5OjAwOjE4JyB9DQogICAgICBdLA0KICAgICAgc2lnbmVkRGF0ZXM6IFsNCiAgICAgICAgbmV3IERhdGUoMjAyNCwgMCwgMSksDQogICAgICAgIG5ldyBEYXRlKDIwMjQsIDAsIDIpLA0KICAgICAgICBuZXcgRGF0ZSgyMDI0LCAwLCAzKSwNCiAgICAgICAgbmV3IERhdGUoMjAyNCwgMCwgNCksDQogICAgICAgIG5ldyBEYXRlKDIwMjQsIDAsIDUpDQogICAgICBdLA0KICAgICAgYWlMb2dpblN0YXR1czogew0KICAgICAgICB5dWFuYmFvOiBmYWxzZSwNCiAgICAgICAgZG91YmFvOiBmYWxzZSwNCiAgICAgICAgYWdlbnQ6IGZhbHNlLA0KICAgICAgICB3ZW54aW46IGZhbHNlLA0KICAgICAgICAvLyBxdzogZmFsc2UNCiAgICAgIH0sDQogICAgICBhY2NvdW50czogew0KICAgICAgICB5dWFuYmFvOiAnJywNCiAgICAgICAgZG91YmFvOiAnJywNCiAgICAgICAgYWdlbnQ6ICcnLA0KICAgICAgICB3ZW54aW46ICcnLA0KICAgICAgICAvLyBxdzogJycNCiAgICAgIH0sDQogICAgICBpc0NsaWNrOiB7DQogICAgICAgIHl1YW5iYW86IGZhbHNlLA0KICAgICAgICBkb3ViYW86IGZhbHNlLA0KICAgICAgICBhZ2VudDogZmFsc2UsDQogICAgICAgIHdlbnhpbjogZmFsc2UsDQogICAgICAgIC8vIHF3OiBmYWxzZQ0KICAgICAgfSwNCiAgICAgIGFpTG9naW5EaWFsb2dWaXNpYmxlOiBmYWxzZSwNCiAgICAgIGN1cnJlbnRBaVR5cGU6ICcnLA0KICAgICAgcXJDb2RlVXJsOiAnJywNCiAgICAgIC8vIOa2iOaBr+ebuOWFs+WPmOmHjw0KICAgICAgbWVzc2FnZXM6IFtdLA0KICAgICAgbWVzc2FnZUlucHV0OiAnJywNCiAgICAgIGlzTG9hZGluZzogew0KICAgICAgICB5dWFuYmFvOiB0cnVlLA0KICAgICAgICBkb3ViYW86IHRydWUsDQogICAgICAgIHdlbnhpbjogdHJ1ZSwNCiAgICAgICAgYWdlbnQ6IHRydWUsDQogICAgICAgIC8vIHF3OiB0cnVlDQogICAgICB9LA0KICAgIH0NCiAgfSwNCiAgLy8g6K6h566X5b2T5YmN5pyI5Lu955qE562+5Yiw5pel5pyfDQogIGNvbXB1dGVkOiB7DQogICAgY2FsZW5kYXJEYXRlcygpIHsNCiAgICAgIGNvbnN0IGRhdGVzID0gW107DQogICAgICBjb25zdCBmaXJzdERheSA9IG5ldyBEYXRlKHRoaXMuY3VycmVudFllYXIsIHRoaXMuY3VycmVudE1vbnRoIC0gMSwgMSk7DQogICAgICBjb25zdCBsYXN0RGF5ID0gbmV3IERhdGUodGhpcy5jdXJyZW50WWVhciwgdGhpcy5jdXJyZW50TW9udGgsIDApOw0KDQogICAgICAvLyBGaWxsIGluIGVtcHR5IHNsb3RzIGJlZm9yZSBmaXJzdCBkYXkNCiAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgZmlyc3REYXkuZ2V0RGF5KCk7IGkrKykgew0KICAgICAgICBkYXRlcy5wdXNoKG51bGwpOw0KICAgICAgfQ0KDQogICAgICAvLyBGaWxsIGluIGRheXMgb2YgdGhlIG1vbnRoDQogICAgICBmb3IgKGxldCBpID0gMTsgaSA8PSBsYXN0RGF5LmdldERhdGUoKTsgaSsrKSB7DQogICAgICAgIGRhdGVzLnB1c2gobmV3IERhdGUodGhpcy5jdXJyZW50WWVhciwgdGhpcy5jdXJyZW50TW9udGggLSAxLCBpKSk7DQogICAgICB9DQoNCiAgICAgIHJldHVybiBkYXRlczsNCiAgICB9LA0KICAgIGdldEFpTG9naW5UaXRsZSgpIHsNCiAgICAgIGNvbnN0IHRpdGxlcyA9IHsNCiAgICAgICAgeXVhbmJhbzogJ+iFvuiur+WFg+WuneeZu+W9lScsDQogICAgICAgIGRvdWJhbzogJ+ixhuWMheeZu+W9lScsDQogICAgICAgIGFnZW50OiAn5pm66IO95L2T55m75b2VJywNCiAgICAgICAgd2VueGluOiAn55m+5bqmQUnnmbvlvZUnLA0KICAgICAgICAvLyBxdzogJ+mAmuS5ieWNg+mXrueZu+W9lScNCiAgICAgIH07DQogICAgICByZXR1cm4gdGl0bGVzW3RoaXMuY3VycmVudEFpVHlwZV0gfHwgJ+eZu+W9lSc7DQogICAgfQ0KICB9LA0KDQogIGNyZWF0ZWQoKSB7DQogICAgdGhpcy5nZXRVc2VyKCk7DQoNCiAgfSwNCiAgbWV0aG9kczogew0KICAgIGhhbmRsZVNldExpbmVDaGFydERhdGEodHlwZSkgew0KICAgICAgdGhpcy5saW5lQ2hhcnREYXRhID0gbGluZUNoYXJ0RGF0YVt0eXBlXQ0KICAgIH0sDQogICAgZ2V0VXNlcigpIHsNCiAgICAgIGdldFVzZXJQcm9maWxlKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMudXNlciA9IHJlc3BvbnNlLmRhdGE7DQogICAgICAgIHRoaXMucm9sZUdyb3VwID0gcmVzcG9uc2Uucm9sZUdyb3VwOw0KICAgICAgICB0aGlzLnBvc3RHcm91cCA9IHJlc3BvbnNlLnBvc3RHcm91cDsNCiAgICAgICAgdGhpcy51c2VySWQgPSByZXNwb25zZS5kYXRhLnVzZXJJZDsNCiAgICAgICAgdGhpcy5jb3JwSWQgPSByZXNwb25zZS5kYXRhLmNvcnBJZDsNCg0KICAgICAgICB0aGlzLmluaXRXZWJTb2NrZXQodGhpcy51c2VySWQpOyAvLyDliJvlu7rml7blu7rnq4vov57mjqUNCg0KICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAvLyDmo4Dmn6XlhYPlrp3nmbvlvZXnirbmgIENCiAgICAgICAgICB0aGlzLnNlbmRNZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdQTEFZX0NIRUNLX1lCX0xPR0lOJywNCiAgICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgICBjb3JwSWQ6IHRoaXMuY29ycElkDQogICAgICAgICAgfSk7DQoNCiAgICAgICAgICAvLyDmo4Dmn6XosYbljIXnmbvlvZXnirbmgIENCiAgICAgICAgICB0aGlzLnNlbmRNZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdQTEFZX0NIRUNLX0RCX0xPR0lOJywNCiAgICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgICBjb3JwSWQ6IHRoaXMuY29ycElkDQogICAgICAgICAgfSk7DQoNCiAgICAgICAgICAvLyDmo4Dmn6Xmmbrog73kvZPnmbvlvZXnirbmgIENCiAgICAgICAgICB0aGlzLnNlbmRNZXNzYWdlKHsNCiAgICAgICAgICAgIHR5cGU6ICdQTEFZX0NIRUNLX0FHRU5UX0xPR0lOJywNCiAgICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgICBjb3JwSWQ6IHRoaXMuY29ycElkDQogICAgICAgICAgfSk7DQoNCiAgICAgICAgICAvLyDmo4Dmn6Xnmb7luqZBSeeZu+W9leeKtuaAgQ0KICAgICAgICAgIHRoaXMuc2VuZE1lc3NhZ2Uoew0KICAgICAgICAgICAgdHlwZTogJ1BMQVlfQ0hFQ0tfV1hfTE9HSU4nLA0KICAgICAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQNCiAgICAgICAgICB9KTsNCg0KICAgICAgICAgIC8vIOajgOafpemAmuS5ieWNg+mXrueZu+W9leeKtuaAgQ0KICAgICAgICAgIC8vIHRoaXMuc2VuZE1lc3NhZ2Uoew0KICAgICAgICAgIC8vICAgdHlwZTogJ1BMQVlfQ0hFQ0tfUVdfTE9HSU4nLA0KICAgICAgICAgIC8vICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgICAvLyAgIGNvcnBJZDogdGhpcy5jb3JwSWQNCiAgICAgICAgICAvLyB9KTsNCiAgICAgICAgfSwgMTAwMCk7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOiOt+WPluWFrOS8l+WPt+S/oeaBrw0KICAgIGhhbmRsZUJpbmRXZWNoYXQoKSB7DQogICAgICBnZXRPZmZpY2VBY2NvdW50KCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhICE9IG51bGwpIHsNCiAgICAgICAgICB0aGlzLmZvcm0uYXBwSWQgPSByZXNwb25zZS5kYXRhLmFwcElkOw0KICAgICAgICAgIHRoaXMuZm9ybS5hcHBTZWNyZXQgPSByZXNwb25zZS5kYXRhLmFwcFNlY3JldDsNCiAgICAgICAgICB0aGlzLmZvcm0ub2ZmaWNlQWNjb3VudE5hbWUgPSByZXNwb25zZS5kYXRhLm9mZmljZUFjY291bnROYW1lOw0KICAgICAgICAgIHRoaXMuZm9ybS5waWNVcmwgPSByZXNwb25zZS5kYXRhLnBpY1VybDsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmRpYWxvZ0Zvcm1WaXNpYmxlID0gdHJ1ZTsNCiAgICAgIH0pOw0KICAgIH0sDQogICAgaGFuZGxlQWdlbnRCaW5kKCkgew0KICAgICAgZ2V0QWdlbnRCaW5kKCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhICE9IG51bGwpIHsNCiAgICAgICAgICB0aGlzLmFnZW50Rm9ybS5hZ2VudElkID0gcmVzcG9uc2UuZGF0YS5hZ2VudF9pZDsNCiAgICAgICAgICB0aGlzLmFnZW50Rm9ybS5hZ2VudFRva2VuID0gcmVzcG9uc2UuZGF0YS5hZ2VudF90b2tlbjsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmRpYWxvZ0FnZW50Rm9ybVZpc2libGUgPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICBoYW5kbGVTcGFjZUJpbmQoKSB7DQogICAgICBnZXRTcGFjZUluZm9CeVVzZXJJZCgpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSAhPSBudWxsKSB7DQogICAgICAgICAgdGhpcy5zcGFjZUZvcm0uc3BhY2VJZCA9IHJlc3BvbnNlLmRhdGEuc3BhY2VJZDsNCiAgICAgICAgICB0aGlzLnNwYWNlRm9ybS5zcGFjZU5hbWUgPSByZXNwb25zZS5kYXRhLnNwYWNlTmFtZTsNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLmRpYWxvZ1NwYWNlRm9ybVZpc2libGUgPSB0cnVlOw0KICAgICAgfSk7DQogICAgfSwNCiAgICAvLyDnu5HlrprlhazkvJflj7cNCiAgICBjb25maXJtQmluZCgpIHsNCiAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g6KGo5Y2V6aqM6K+B6YCa6L+H77yM57un57ut5o+Q5LqkDQogICAgICAgICAgYmluZFdjT2ZmaWNlQWNjb3VudCh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICAgICAgdGhpcy5kaWFsb2dGb3JtVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgLy8g6KGo5Y2V6aqM6K+B5aSx6LSlDQogICAgICAgICAgcmV0dXJuIGZhbHNlOw0KICAgICAgICB9DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOe7keWumuWFrOS8l+WPtw0KICAgIGNvbmZpcm1BZ2VudEJpbmQoKSB7DQogICAgICB0aGlzLiRyZWZzLmFnZW50Rm9ybS52YWxpZGF0ZSgodmFsaWQpID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgLy8g6KGo5Y2V6aqM6K+B6YCa6L+H77yM57un57ut5o+Q5LqkDQogICAgICAgICAgc2F2ZUFnZW50QmluZCh0aGlzLmFnZW50Rm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MocmVzcG9uc2UuZGF0YSk7DQogICAgICAgICAgICB0aGlzLmRpYWxvZ0FnZW50Rm9ybVZpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICB9KQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIC8vIOihqOWNlemqjOivgeWksei0pQ0KICAgICAgICAgIHJldHVybiBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCiAgICBjb25maXJtU3BhY2VCaW5kKCkgew0KICAgICAgdGhpcy4kcmVmcy5zcGFjZUZvcm0udmFsaWRhdGUoKHZhbGlkKSA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIC8vIOihqOWNlemqjOivgemAmui/h++8jOe7p+e7reaPkOS6pA0KICAgICAgICAgIHNhdmVTcGFjZUJpbmQodGhpcy5zcGFjZUZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKHJlc3BvbnNlLmRhdGEpOw0KICAgICAgICAgICAgdGhpcy5kaWFsb2dTcGFjZUZvcm1WaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgfSkNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyDooajljZXpqozor4HlpLHotKUNCiAgICAgICAgICByZXR1cm4gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0pOw0KICAgIH0sDQogICAgLy8g6I635Y+W5b2T5YmN55m75b2V55So5oi356ev5YiG5piO57uGDQogICAgc2hvd1BvaW50c0RldGFpbCgpIHsNCiAgICAgIHRoaXMucXVlcnlQb2ludEZvcm0udXNlcklkID0gdGhpcy51c2VyLnVzZXJJZA0KICAgICAgdGhpcy5nZXRVc2VyUG9pbnRzUmVjb3JkKCk7DQogICAgfSwNCiAgICAvLyDojrflj5bnp6/liIbmmI7nu4YNCiAgICBnZXRVc2VyUG9pbnRzUmVjb3JkKCl7DQogICAgICBnZXRVc2VyUG9pbnRzUmVjb3JkKHRoaXMucXVlcnlQb2ludEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLm9wZW5Qb2ludHNSZWNvcmQgPSB0cnVlOw0KICAgICAgICB0aGlzLnBvaW50c1JlY29yZExpc3QgPSByZXNwb25zZS5kYXRhLmxpc3Q7DQogICAgICAgIHRoaXMucG9pbnR0b3RhbCA9IHJlc3BvbnNlLmRhdGEudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2U7DQogICAgICB9KTsNCiAgICB9LA0KICAgIC8vIOiOt+WPluW9k+WJjeaciOS7veeahOetvuWIsOaXpeacnw0KICAgIGlzU2lnbmVkRGF0ZShkYXRlKSB7DQogICAgICBpZiAoIWRhdGUpIHJldHVybiBmYWxzZTsNCiAgICAgIHJldHVybiB0aGlzLnNpZ25lZERhdGVzLnNvbWUoc2lnbmVkRGF0ZSA9Pg0KICAgICAgICBzaWduZWREYXRlLmdldERhdGUoKSA9PT0gZGF0ZS5nZXREYXRlKCkgJiYNCiAgICAgICAgc2lnbmVkRGF0ZS5nZXRNb250aCgpID09PSBkYXRlLmdldE1vbnRoKCkgJiYNCiAgICAgICAgc2lnbmVkRGF0ZS5nZXRGdWxsWWVhcigpID09PSBkYXRlLmdldEZ1bGxZZWFyKCkNCiAgICAgICk7DQogICAgfSwNCiAgICBpc1RvZGF5KGRhdGUpIHsNCiAgICAgIGlmICghZGF0ZSkgcmV0dXJuIGZhbHNlOw0KICAgICAgY29uc3QgdG9kYXkgPSBuZXcgRGF0ZSgpOw0KICAgICAgcmV0dXJuIGRhdGUuZ2V0RGF0ZSgpID09PSB0b2RheS5nZXREYXRlKCkgJiYNCiAgICAgICAgICAgICBkYXRlLmdldE1vbnRoKCkgPT09IHRvZGF5LmdldE1vbnRoKCkgJiYNCiAgICAgICAgICAgICBkYXRlLmdldEZ1bGxZZWFyKCkgPT09IHRvZGF5LmdldEZ1bGxZZWFyKCk7DQogICAgfSwNCiAgICBpc0Z1dHVyZURhdGUoZGF0ZSkgew0KICAgICAgaWYgKCFkYXRlKSByZXR1cm4gZmFsc2U7DQogICAgICBjb25zdCB0b2RheSA9IG5ldyBEYXRlKCk7DQogICAgICB0b2RheS5zZXRIb3VycygwLCAwLCAwLCAwKTsNCiAgICAgIHJldHVybiBkYXRlID4gdG9kYXk7DQogICAgfSwNCiAgICBoYW5kbGVTaWduSW4oKSB7DQogICAgICBpZiAoIXRoaXMudG9kYXlTaWduZWRJbikgew0KICAgICAgICB0aGlzLnRvZGF5U2lnbmVkSW4gPSB0cnVlOw0KICAgICAgICB0aGlzLnNpZ25lZERhdGVzLnB1c2gobmV3IERhdGUoKSk7DQogICAgICAgIGNvbnN0IG5vdyA9IG5ldyBEYXRlKCk7DQogICAgICAgIHRoaXMuc2lnbkluSGlzdG9yeS51bnNoaWZ0KHsNCiAgICAgICAgICBkYXRlOiBgJHtub3cuZ2V0RnVsbFllYXIoKX0tJHtTdHJpbmcobm93LmdldE1vbnRoKCkgKyAxKS5wYWRTdGFydCgyLCAnMCcpfS0ke1N0cmluZyhub3cuZ2V0RGF0ZSgpKS5wYWRTdGFydCgyLCAnMCcpfWAsDQogICAgICAgICAgdGltZTogYCR7U3RyaW5nKG5vdy5nZXRIb3VycygpKS5wYWRTdGFydCgyLCAnMCcpfToke1N0cmluZyhub3cuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpfToke1N0cmluZyhub3cuZ2V0U2Vjb25kcygpKS5wYWRTdGFydCgyLCAnMCcpfWANCiAgICAgICAgfSk7DQogICAgICAgIHRoaXMubW9udGhseVNpZ25JbkRheXMrKzsNCiAgICAgICAgdGhpcy50b3RhbFNpZ25JbkRheXMrKzsNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUFpTG9naW4odHlwZSkgew0KICAgICAgdGhpcy5jdXJyZW50QWlUeXBlID0gdHlwZTsNCiAgICAgIHRoaXMuYWlMb2dpbkRpYWxvZ1Zpc2libGUgPSB0cnVlOw0KICAgICAgdGhpcy5pc0xvYWRpbmdbdHlwZV0gPSB0cnVlOw0KICAgICAgdGhpcy5nZXRRckNvZGUodHlwZSk7DQogICAgfSwNCiAgICBnZXRRckNvZGUodHlwZSkgew0KICAgICAgdGhpcy5xckNvZGVVcmwgPSAnJw0KICAgICAgaWYodHlwZSA9PSAneXVhbmJhbycpew0KICAgICAgICB0aGlzLnNlbmRNZXNzYWdlKHsNCiAgICAgICAgICB0eXBlOiAnUExBWV9HRVRfWUJfUVJDT0RFJywNCiAgICAgICAgICB1c2VySWQ6IHRoaXMudXNlcklkLA0KICAgICAgICAgIGNvcnBJZDogdGhpcy5jb3JwSWQNCiAgICAgICAgfSk7DQogICAgICB9DQogICAgICBpZih0eXBlID09ICdkb3ViYW8nKXsNCiAgICAgICAgdGhpcy5zZW5kTWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ1BMQVlfR0VUX0RCX1FSQ09ERScsDQogICAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgICBjb3JwSWQ6IHRoaXMuY29ycElkDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgaWYodHlwZSA9PSAnYWdlbnQnKXsNCiAgICAgICAgdGhpcy5zZW5kTWVzc2FnZSh7DQogICAgICAgICAgdHlwZTogJ1BMQVlfR0VUX0FHRU5UX1FSQ09ERScsDQogICAgICAgICAgdXNlcklkOiB0aGlzLnVzZXJJZCwNCiAgICAgICAgICBjb3JwSWQ6IHRoaXMuY29ycElkDQogICAgICAgIH0pOw0KICAgICAgfQ0KICAgICAgaWYodHlwZSA9PSAnd2VueGluJyl7DQogICAgICAgIHRoaXMuc2VuZE1lc3NhZ2Uoew0KICAgICAgICAgIHR5cGU6ICdQTEFZX0dFVF9XWF9RUkNPREUnLA0KICAgICAgICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAgICAgY29ycElkOiB0aGlzLmNvcnBJZA0KICAgICAgICB9KTsNCiAgICAgIH0NCiAgICAgIC8vIGlmKHR5cGUgPT0gJ3F3Jyl7DQogICAgICAvLyAgIHRoaXMuc2VuZE1lc3NhZ2Uoew0KICAgICAgLy8gICAgIHR5cGU6ICdQTEFZX0dFVF9RV19RUkNPREUnLA0KICAgICAgLy8gICAgIHVzZXJJZDogdGhpcy51c2VySWQsDQogICAgICAvLyAgICAgY29ycElkOiB0aGlzLmNvcnBJZA0KICAgICAgLy8gICB9KTsNCiAgICAgIC8vIH0NCiAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICBtZXNzYWdlOiAn5q2j5Zyo6I635Y+W55m75b2V5LqM57u056CBLi4uJywNCiAgICAgICAgdHlwZTogJ2luZm8nDQogICAgICB9KTsNCiAgICB9LA0KICAgIGdldFBsYXRmb3JtSWNvbih0eXBlKSB7DQogICAgICBjb25zdCBpY29ucyA9IHsNCiAgICAgICAgeXVhbmJhbzogcmVxdWlyZSgnQC9hc3NldHMvbG9nby95dWFuYmFvLnBuZycpLA0KICAgICAgICBkb3ViYW86IHJlcXVpcmUoJ0AvYXNzZXRzL2xvZ28vZG91YmFvLnBuZycpLA0KICAgICAgICBhZ2VudDogcmVxdWlyZSgnQC9hc3NldHMvbG9nby95dWFuYmFvLnBuZycpLA0KICAgICAgICB3ZW54aW46IHJlcXVpcmUoJ0AvYXNzZXRzL2FpL3l1YW5iYW8ucG5nJyksDQogICAgICAgIHF3OiByZXF1aXJlKCdAL2Fzc2V0cy9sb2dvL3F3LnBuZycpDQogICAgICB9Ow0KICAgICAgcmV0dXJuIGljb25zW3R5cGVdIHx8ICcnOw0KICAgIH0sDQogICAgZ2V0UGxhdGZvcm1OYW1lKHR5cGUpIHsNCiAgICAgIGNvbnN0IG5hbWVzID0gew0KICAgICAgICB5dWFuYmFvOiAn6IW+6K6v5YWD5a6dJywNCiAgICAgICAgZG91YmFvOiAn6LGG5YyFJywNCiAgICAgICAgYWdlbnQ6ICfmmbrog73kvZMnLA0KICAgICAgICB3ZW54aW46ICfnmb7luqZBSScsDQogICAgICAgIC8vIHF3OiAn6YCa5LmJ5Y2D6ZeuJw0KICAgICAgfTsNCiAgICAgIHJldHVybiBuYW1lc1t0eXBlXSB8fCAnJzsNCiAgICB9LA0KICAgIC8vIFdlYlNvY2tldCDnm7jlhbPmlrnms5UNCiAgICBpbml0V2ViU29ja2V0KGlkKSB7DQogICAgICBjb25zdCB3c1VybCA9IHByb2Nlc3MuZW52LlZVRV9BUFBfV1NfQVBJICsgYG15cGMtJHtpZH1gOw0KICAgICAgY29uc29sZS5sb2coJ1dlYlNvY2tldCBVUkw6JywgcHJvY2Vzcy5lbnYuVlVFX0FQUF9XU19BUEkpOw0KICAgICAgd2Vic29ja2V0Q2xpZW50LmNvbm5lY3Qod3NVcmwsIChldmVudCkgPT4gew0KICAgICAgICBzd2l0Y2ggKGV2ZW50LnR5cGUpIHsNCiAgICAgICAgICBjYXNlICdvcGVuJzoNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygn5q2j5Zyo6I635Y+W5pyA5paw55m75b2V54q25oCB77yM6K+356iN5ZCOLi4uJyk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICdtZXNzYWdlJzoNCiAgICAgICAgICAgIHRoaXMuaGFuZGxlV2ViU29ja2V0TWVzc2FnZShldmVudC5kYXRhKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgJ2Nsb3NlJzoNCiAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygnV2ViU29ja2V06L+e5o6l5bey5YWz6ZetJyk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgICBjYXNlICdlcnJvcic6DQogICAgICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCdXZWJTb2NrZXTov57mjqXplJnor68nKTsNCiAgICAgICAgICAgIGJyZWFrOw0KICAgICAgICAgIGNhc2UgJ3JlY29ubmVjdF9mYWlsZWQnOg0KICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcignV2ViU29ja2V06YeN6L+e5aSx6LSl77yM6K+35Yi35paw6aG16Z2i6YeN6K+VJyk7DQogICAgICAgICAgICBicmVhazsNCiAgICAgICAgfQ0KICAgICAgfSk7DQogICAgfSwNCg0KICAgIGhhbmRsZVdlYlNvY2tldE1lc3NhZ2UoZGF0YSkgew0KICAgICAgY29uc29sZS5sb2coJ+aUtuWIsOa2iOaBrzonLCBkYXRhKTsNCiAgICAgIGNvbnN0IGRhdGFzdHIgPSBkYXRhOw0KICAgICAgY29uc3QgZGF0YU9iaiA9IEpTT04ucGFyc2UoZGF0YXN0cik7DQoNCiAgICAgIGlmIChkYXRhc3RyLmluY2x1ZGVzKCJSRVRVUk5fWUJfU1RBVFVTIikgJiYgZGF0YU9iai5zdGF0dXMgIT0gJycpIHsNCiAgICAgICAgaWYgKCFkYXRhc3RyLmluY2x1ZGVzKCJmYWxzZSIpKSB7DQogICAgICAgICAgdGhpcy5haUxvZ2luRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuYWlMb2dpblN0YXR1cy55dWFuYmFvID0gdHJ1ZTsNCiAgICAgICAgICB0aGlzLmFjY291bnRzLnl1YW5iYW8gPSBkYXRhT2JqLnN0YXR1czsNCiAgICAgICAgICB0aGlzLmlzTG9hZGluZy55dWFuYmFvID0gZmFsc2U7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5pc0NsaWNrLnl1YW5iYW8gPSB0cnVlOw0KICAgICAgICAgIHRoaXMuaXNMb2FkaW5nLnl1YW5iYW8gPSBmYWxzZTsNCiAgICAgICAgfQ0KICAgICAgfSBlbHNlIGlmIChkYXRhc3RyLmluY2x1ZGVzKCJSRVRVUk5fQUdFTlRfU1RBVFVTIikgJiYgZGF0YU9iai5zdGF0dXMgIT0gJycpIHsNCiAgICAgICAgaWYgKCFkYXRhc3RyLmluY2x1ZGVzKCJmYWxzZSIpKSB7DQogICAgICAgICAgdGhpcy5haUxvZ2luRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuYWlMb2dpblN0YXR1cy5hZ2VudCA9IHRydWU7DQogICAgICAgICAgdGhpcy5hY2NvdW50cy5hZ2VudCA9IGRhdGFPYmouc3RhdHVzOw0KICAgICAgICAgIHRoaXMuaXNMb2FkaW5nLmFnZW50ID0gZmFsc2U7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5pc0NsaWNrLmFnZW50ID0gdHJ1ZTsNCiAgICAgICAgICB0aGlzLmlzTG9hZGluZy5hZ2VudCA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKGRhdGFzdHIuaW5jbHVkZXMoIlJFVFVSTl9QQ19ZQl9RUlVSTCIpIHx8IGRhdGFzdHIuaW5jbHVkZXMoIlJFVFVSTl9QQ19EQl9RUlVSTCIpIHx8IGRhdGFzdHIuaW5jbHVkZXMoIlJFVFVSTl9QQ19BR0VOVF9RUlVSTCIpIHx8IGRhdGFzdHIuaW5jbHVkZXMoIlJFVFVSTl9QQ19RV19RUlVSTCIpKSB7DQogICAgICAgIHRoaXMucXJDb2RlVXJsID0gZGF0YU9iai51cmw7DQogICAgICB9IGVsc2UgaWYgKGRhdGFzdHIuaW5jbHVkZXMoIlJFVFVSTl9QQ19XWF9RUlVSTCIpKSB7DQogICAgICAgIC8vIOeZvuW6pkFJ5LiN6ZyA6KaB5pi+56S65LqM57u056CB5Zu+54mH77yM55u05o6l5pi+56S65o+Q56S65L+h5oGvDQogICAgICAgIHRoaXMucXJDb2RlVXJsID0gJyc7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uoew0KICAgICAgICAgIG1lc3NhZ2U6ICfor7flnKjmtY/op4jlmajkuK3miavnoIHnmbvlvZXnmb7luqZBSScsDQogICAgICAgICAgdHlwZTogJ2luZm8nLA0KICAgICAgICAgIGR1cmF0aW9uOiAzMDAwDQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIGlmIChkYXRhc3RyLmluY2x1ZGVzKCJSRVRVUk5fREJfU1RBVFVTIikgJiYgZGF0YU9iai5zdGF0dXMgIT0gJycpIHsNCiAgICAgICAgaWYgKCFkYXRhc3RyLmluY2x1ZGVzKCJmYWxzZSIpKSB7DQogICAgICAgICAgdGhpcy5haUxvZ2luRGlhbG9nVmlzaWJsZSA9IGZhbHNlOw0KICAgICAgICAgIHRoaXMuYWlMb2dpblN0YXR1cy5kb3ViYW8gPSB0cnVlOw0KICAgICAgICAgIHRoaXMuYWNjb3VudHMuZG91YmFvID0gZGF0YU9iai5zdGF0dXM7DQogICAgICAgICAgdGhpcy5pc0xvYWRpbmcuZG91YmFvID0gZmFsc2U7DQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5pc0NsaWNrLmRvdWJhbyA9IHRydWU7DQogICAgICAgICAgdGhpcy5pc0xvYWRpbmcuZG91YmFvID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0gZWxzZSBpZiAoZGF0YXN0ci5pbmNsdWRlcygiUkVUVVJOX1dYX1NUQVRVUyIpICYmIGRhdGFPYmouc3RhdHVzICE9ICcnKSB7DQogICAgICAgIGlmICghZGF0YXN0ci5pbmNsdWRlcygiZmFsc2UiKSkgew0KICAgICAgICAgIHRoaXMuYWlMb2dpbkRpYWxvZ1Zpc2libGUgPSBmYWxzZTsNCiAgICAgICAgICB0aGlzLmFpTG9naW5TdGF0dXMud2VueGluID0gdHJ1ZTsNCiAgICAgICAgICB0aGlzLmFjY291bnRzLndlbnhpbiA9IGRhdGFPYmouc3RhdHVzOw0KICAgICAgICAgIHRoaXMuaXNMb2FkaW5nLndlbnhpbiA9IGZhbHNlOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuaXNDbGljay53ZW54aW4gPSB0cnVlOw0KICAgICAgICAgIHRoaXMuaXNMb2FkaW5nLndlbnhpbiA9IGZhbHNlOw0KICAgICAgICB9DQogICAgICB9IGVsc2UgaWYgKGRhdGFzdHIuaW5jbHVkZXMoIlJFVFVSTl9RV19TVEFUVVMiKSAmJiBkYXRhT2JqLnN0YXR1cyAhPSAnJykgew0KICAgICAgICBpZiAoIWRhdGFzdHIuaW5jbHVkZXMoImZhbHNlIikpIHsNCiAgICAgICAgICB0aGlzLmFpTG9naW5EaWFsb2dWaXNpYmxlID0gZmFsc2U7DQogICAgICAgICAgdGhpcy5haUxvZ2luU3RhdHVzLnF3ID0gdHJ1ZTsNCiAgICAgICAgICB0aGlzLmFjY291bnRzLnF3ID0gZGF0YU9iai5zdGF0dXM7DQogICAgICAgICAgdGhpcy5pc0xvYWRpbmcucXcgPSBmYWxzZTsNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLmlzQ2xpY2sucXcgPSB0cnVlOw0KICAgICAgICAgIHRoaXMuaXNMb2FkaW5nLnF3ID0gZmFsc2U7DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KDQogICAgY2xvc2VXZWJTb2NrZXQoKSB7DQogICAgICB3ZWJzb2NrZXRDbGllbnQuY2xvc2UoKTsNCiAgICB9LA0KDQogICAgc2VuZE1lc3NhZ2UoZGF0YSkgew0KICAgICAgaWYgKHdlYnNvY2tldENsaWVudC5zZW5kKGRhdGEpKSB7DQogICAgICAgIC8vIOa7muWKqOWIsOW6lemDqA0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5zY3JvbGxUb0JvdHRvbSgpOw0KICAgICAgICB9KTsNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoJ1dlYlNvY2tldOacqui/nuaOpScpOw0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5qC85byP5YyW5pe26Ze0DQogICAgZm9ybWF0VGltZShkYXRlKSB7DQogICAgICBjb25zdCBob3VycyA9IFN0cmluZyhkYXRlLmdldEhvdXJzKCkpLnBhZFN0YXJ0KDIsICcwJyk7DQogICAgICBjb25zdCBtaW51dGVzID0gU3RyaW5nKGRhdGUuZ2V0TWludXRlcygpKS5wYWRTdGFydCgyLCAnMCcpOw0KICAgICAgY29uc3Qgc2Vjb25kcyA9IFN0cmluZyhkYXRlLmdldFNlY29uZHMoKSkucGFkU3RhcnQoMiwgJzAnKTsNCiAgICAgIHJldHVybiBgJHtob3Vyc306JHttaW51dGVzfToke3NlY29uZHN9YDsNCiAgICB9LA0KDQogICAgLy8g5rua5Yqo5Yiw5bqV6YOoDQogICAgc2Nyb2xsVG9Cb3R0b20oKSB7DQogICAgICBjb25zdCBtZXNzYWdlTGlzdCA9IHRoaXMuJHJlZnMubWVzc2FnZUxpc3Q7DQogICAgICBpZiAobWVzc2FnZUxpc3QpIHsNCiAgICAgICAgbWVzc2FnZUxpc3Quc2Nyb2xsVG9wID0gbWVzc2FnZUxpc3Quc2Nyb2xsSGVpZ2h0Ow0KICAgICAgfQ0KICAgIH0sDQogICAgaGFuZGxlUmVmcmVzaCgpIHsNCiAgICAgIHdpbmRvdy5sb2NhdGlvbi5yZWxvYWQoKTsNCiAgICB9LA0KICB9LA0KICBiZWZvcmVEZXN0cm95KCkgew0KICAgIHRoaXMuY2xvc2VXZWJTb2NrZXQoKTsgLy8g6ZSA5q+B5pe25YWz6Zet6L+e5o6lDQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiNA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views", "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n\r\n<!--    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />-->\r\n    <div class=\"app-container\">\r\n      <el-row :gutter=\"30\">\r\n        <el-col :span=\"6\" :xs=\"30\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>个人信息</span>\r\n            </div>\r\n            <div>\r\n              <div class=\"text-center\">\r\n                <userAvatar />\r\n              </div>\r\n              <ul class=\"list-group list-group-striped\">\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />用户名称\r\n                  <div class=\"pull-right\" id=\"userName\">{{ user.nickName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"phone\" />手机号码\r\n                  <div class=\"pull-right\">{{ user.phonenumber }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"date\" />创建日期\r\n                  <div class=\"pull-right\">{{ user.createTime }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />\r\n                  <span>积分余额</span>\r\n                  <div  :style=\"{ color: user.points >= 0 ? 'green' : 'red' }\" class=\"pull-right\">{{ user.points }}</div>\r\n                  <el-tooltip content=\"点击可查看积分明细\" placement=\"top\" effect=\"light\">\r\n                    <i class=\"el-icon-chat-dot-round\" @click=\"showPointsDetail\"></i>\r\n                  </el-tooltip>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleBindWechat\">绑定公众号</el-button>\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleAgentBind\">Agent API设置</el-button>\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleSpaceBind\">元器空间绑定</el-button>\r\n\r\n\r\n                  <el-dialog title=\"公众号智能体API配置\" :visible.sync=\"dialogAgentFormVisible\">\r\n                    <el-form :model=\"agentForm\" :rules=\"agentrules\" ref=\"agentForm\" >\r\n                      <el-form-item label=\"智能体ID\" :label-width=\"formLabelWidth\" prop=\"agentId\">\r\n                        <el-input v-model=\"agentForm.agentId\" maxlength=\"32\"  placeholder=\"请输入agentId\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"智能体Token\" :label-width=\"formLabelWidth\" prop=\"agentToken\">\r\n                        <el-input v-model=\"agentForm.agentToken\" maxlength=\"50\"  placeholder=\"请输入agentToken\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogAgentFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmAgentBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n\r\n                  <el-dialog title=\"元器空间绑定\" :visible.sync=\"dialogSpaceFormVisible\">\r\n                    <el-form :model=\"spaceForm\" :rules=\"spacerules\" ref=\"spaceForm\" >\r\n                      <el-form-item label=\"空间ID\" :label-width=\"formLabelWidth\" prop=\"spaceId\">\r\n                        <el-input v-model=\"spaceForm.spaceId\" maxlength=\"32\"  placeholder=\"请输入空间ID\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"空间名称\" :label-width=\"formLabelWidth\" prop=\"spaceName\">\r\n                        <el-input v-model=\"spaceForm.spaceName\" maxlength=\"50\"  placeholder=\"请输入空间名称\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogSpaceFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmSpaceBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n\r\n                  <el-dialog title=\"绑定微信公众号\" :visible.sync=\"dialogFormVisible\">\r\n                    <el-form :model=\"form\" :rules=\"rules\" ref=\"form\" >\r\n                      <el-form-item label=\"appId\" :label-width=\"formLabelWidth\" prop=\"appId\">\r\n                        <el-input v-model=\"form.appId\" maxlength=\"32\"  placeholder=\"请输入appId\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"appSecret\" :label-width=\"formLabelWidth\" prop=\"appSecret\">\r\n                        <el-input v-model=\"form.appSecret\" maxlength=\"50\"  placeholder=\"请输入appSecret\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"公众号名称\" :label-width=\"formLabelWidth\" prop=\"officeAccountName\">\r\n                        <el-input v-model=\"form.officeAccountName\" maxlength=\"50\"  placeholder=\"请输入公众号名称\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"素材封面图\" :label-width=\"formLabelWidth\" prop=\"picUrl\">\r\n                        <image-upload v-model=\"form.picUrl\"/>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"规范说明\" :label-width=\"formLabelWidth\">\r\n                        <div style=\"color: #f56c6c; font-size: 13px;\">\r\n                          请把IP: *************** 添加到公众号IP白名单。步骤：登录微信公众平台→点击设置与开发→安全中心→IP白名单。一般一小时后生效。\r\n                        </div>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :span=\"6\" :xs=\"30\">\r\n          <el-card class=\"box-card ai-status-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span class=\"card-title\">\r\n                <svg-icon icon-class=\"ai\" class=\"title-icon\" />\r\n                AI 登录状态\r\n              </span>\r\n              <el-button\r\n                style=\"float: right; margin-top: -30px;\"\r\n                type=\"text\"\r\n                @click=\"handleRefresh\"\r\n              >\r\n                <i class=\"el-icon-refresh\"></i> 刷新\r\n              </el-button>\r\n            </div>\r\n            <div class=\"ai-status-list\">\r\n              <div class=\"ai-status-item\" v-for=\"(status, type) in aiLoginStatus\" :key=\"type\">\r\n                <div class=\"ai-platform\">\r\n                  <div class=\"platform-icon\">\r\n                    <img :src=\"getPlatformIcon(type)\" :alt=\"getPlatformName(type)\" />\r\n                  </div>\r\n                  <div class=\"platform-name\">\r\n                    {{ getPlatformName(type) }}\r\n                    <el-tooltip v-if=\"isLoading[type]\" content=\"正在登录中...\" placement=\"top\">\r\n                      <i class=\"el-icon-loading loading-icon\"></i>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n                <div class=\"status-action\">\r\n                  <el-tag v-if=\"status\" type=\"success\" effect=\"dark\" class=\"status-tag\">\r\n                    <i class=\"el-icon-success\"></i> <span>{{ accounts[type] }}</span>\r\n                  </el-tag>\r\n                  <el-button v-else type=\"primary\" size=\"small\" :disabled=\"!isClick[type]\" @click=\"handleAiLogin(type)\" class=\"login-btn\">\r\n                    <i class=\"el-icon-connection\"></i> 点击登录\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n\r\n\r\n\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- AI登录二维码对话框 -->\r\n    <el-dialog :title=\"getAiLoginTitle\" :visible.sync=\"aiLoginDialogVisible\" width=\"1200px\" height=\"800px\" center>\r\n      <div class=\"qr-code-container\" v-loading=\"!qrCodeUrl\">\r\n        <div v-if=\"qrCodeUrl\" class=\"qr-code\">\r\n          <img style=\"width: 100%;height: 100%;\" :src=\"qrCodeUrl\" alt=\"登录二维码\" />\r\n          <p class=\"qr-tip\">请使用对应AI平台APP扫码登录</p>\r\n        </div>\r\n        <div v-else class=\"loading-tip\">\r\n          正在获取登录二维码...\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"积分详细\" :visible.sync=\"openPointsRecord\" width=\"1000px\" append-to-body>\r\n        <el-select\r\n          v-model=\"queryPointForm.type\"\r\n          placeholder=\"积分类型\"\r\n          clearable\r\n          style=\"width: 240px;margin-bottom: 10px\"\r\n          @change=\"getUserPointsRecord\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in changeType\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      <el-table  v-loading=\"loading\" :data=\"pointsRecordList\">\r\n        <el-table-column label=\"用户昵称\" align=\"center\" key=\"nick_name\" prop=\"nick_name\"  :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"变更数量\" align=\"center\" key=\"change_amount\" prop=\"change_amount\" :show-overflow-tooltip=\"true\">\r\n          <template slot-scope=\"scope\">\r\n                <span :style=\"{ color: scope.row.change_amount >= 0 ? 'green' : 'red' }\">\r\n                  {{ scope.row.change_amount }}\r\n                </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"积分余额\" align=\"center\" key=\"balance_after\" prop=\"balance_after\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"变更类型\" align=\"center\" key=\"change_type\" prop=\"change_type\"   />\r\n        <el-table-column  width=\"200\" label=\"变更时间\" align=\"center\" prop=\"create_time\" >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.create_time) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作人\" align=\"center\" key=\"create_name\" prop=\"create_name\"   />\r\n        <el-table-column label=\"备注\" align=\"center\" key=\"remark\" prop=\"remark\"   />\r\n\r\n      </el-table>\r\n      <pagination\r\n        v-show=\"pointtotal>0\"\r\n        :total=\"pointtotal\"\r\n        :page.sync=\"queryPointForm.page\"\r\n        :limit.sync=\"queryPointForm.limit\"\r\n        @pagination=\"getUserPointsRecord\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport PanelGroup from './dashboard/PanelGroup'\r\nimport LineChart from './dashboard/LineChart'\r\nimport RaddarChart from './dashboard/RaddarChart'\r\nimport PieChart from './dashboard/PieChart'\r\nimport BarChart from './dashboard/BarChart'\r\nimport userAvatar from \"@/views/system/user/profile/userAvatar\";\r\nimport userInfo from \"@/views/system/user/profile/userInfo\";\r\nimport resetPwd from \"@/views/system/user/profile/resetPwd\";\r\nimport {getUserProfile, bindWcOfficeAccount, getOfficeAccount, getAgentBind, saveAgentBind, saveSpaceBind, getSpaceInfoByUserId} from \"@/api/system/user\";\r\nimport {getUserPointsRecord } from \"@/api/wechat/company\";\r\nimport websocketClient from '@/utils/websocket';\r\n\r\nconst lineChartData = {\r\n  newVisitis: {\r\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\r\n    actualData: [120, 82, 91, 154, 162, 140, 145]\r\n  },\r\n  messages: {\r\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\r\n    actualData: [180, 160, 151, 106, 145, 150, 130]\r\n  },\r\n  purchases: {\r\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\r\n    actualData: [120, 90, 100, 138, 142, 130, 130]\r\n  },\r\n  shoppings: {\r\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\r\n    actualData: [120, 82, 91, 154, 162, 140, 130]\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: 'Index',\r\n  components: {\r\n    PanelGroup,\r\n    LineChart,\r\n    RaddarChart,\r\n    PieChart,\r\n    BarChart,userAvatar, userInfo, resetPwd\r\n  },\r\n  data() {\r\n    return {\r\n      lineChartData: lineChartData.newVisitis,\r\n      user: {},\r\n      roleGroup: {},\r\n      postGroup: {},\r\n      activeTab: \"userinfo\",\r\n      //------ 绑定公众号相关变量 ------//\r\n      dialogFormVisible: false, // 绑定公众号弹窗\r\n      dialogAgentFormVisible: false, // 绑定智能体弹窗\r\n      dialogSpaceFormVisible: false, // 绑定元器空间弹窗\r\n      form: {\r\n        appId: '', // 公众号appId\r\n        appSecret: '', // 公众号appSecret\r\n        officeAccountName: '', // 公众号名称\r\n        picUrl: '', // 公众号封面图\r\n      },\r\n      agentForm: {\r\n        agentId: '', // 智能体ID\r\n        agentToken: '', // 智能体token\r\n      },\r\n      spaceForm: {\r\n        spaceId: '', // 空间ID\r\n        spaceName: '', // 空间名称\r\n      },\r\n      formLabelWidth: '120px', //输入框宽度\r\n      // 绑定公众号表单验证规则\r\n      rules: {\r\n        appId: [\r\n          { required: true, message: '请输入appId', trigger: 'blur' }\r\n        ],\r\n        appSecret: [\r\n          { required: true, message: '请输入appSecret', trigger: 'blur' }\r\n        ],\r\n        officeAccountName: [\r\n          { required: false, message: '请输入公众号名称', trigger: 'blur' }\r\n        ]\r\n      },\r\n      agentrules: {\r\n        agentId: [\r\n          { required: true, message: '请输入agentId', trigger: 'blur' }\r\n        ],\r\n        agentToken: [\r\n          { required: true, message: '请输入agentToken', trigger: 'blur' }\r\n        ]\r\n      },\r\n      spacerules: {\r\n        spaceId: [\r\n          { required: true, message: '请输入空间ID', trigger: 'blur' }\r\n        ],\r\n        spaceName: [\r\n          { required: true, message: '请输入空间名称', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      //------ 积分相关变量 ------//\r\n      loading: true, // 遮罩层\r\n      changeType:[\r\n        {\r\n          label:\"全部\",\r\n          value:\"0\"\r\n        },\r\n        {\r\n        label:\"增加\",\r\n        value:\"1\"\r\n      },\r\n        {\r\n          label:\"消耗\",\r\n          value:\"2\"\r\n        },\r\n      ],  // 积分明细表中的积分类型\r\n      openPointsRecord: false, // 积分明细弹窗\r\n      pointtotal: 0, // 积分明细总数\r\n      queryPointForm:{\r\n        limit:10,\r\n        page:1,\r\n        type:'',\r\n        userId:''\r\n      }, // 积分明细查询需要的查询参数\r\n      pointsRecordList: null, // 积分明细列表\r\n\r\n      //------ 签到相关变量 ------//\r\n      weekDays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],\r\n      currentYear: new Date().getFullYear(),\r\n      currentMonth: new Date().getMonth() + 1,\r\n      continuousSignInDays: 7,\r\n      monthlySignInDays: 15,\r\n      totalSignInDays: 128,\r\n      todaySignedIn: false,\r\n      signInHistory: [\r\n        { date: '2024-01-15', time: '08:30:25' },\r\n        { date: '2024-01-14', time: '09:15:33' },\r\n        { date: '2024-01-13', time: '07:45:12' },\r\n        { date: '2024-01-12', time: '08:20:45' },\r\n        { date: '2024-01-11', time: '09:00:18' }\r\n      ],\r\n      signedDates: [\r\n        new Date(2024, 0, 1),\r\n        new Date(2024, 0, 2),\r\n        new Date(2024, 0, 3),\r\n        new Date(2024, 0, 4),\r\n        new Date(2024, 0, 5)\r\n      ],\r\n      aiLoginStatus: {\r\n        yuanbao: false,\r\n        doubao: false,\r\n        agent: false,\r\n        wenxin: false,\r\n        // qw: false\r\n      },\r\n      accounts: {\r\n        yuanbao: '',\r\n        doubao: '',\r\n        agent: '',\r\n        wenxin: '',\r\n        // qw: ''\r\n      },\r\n      isClick: {\r\n        yuanbao: false,\r\n        doubao: false,\r\n        agent: false,\r\n        wenxin: false,\r\n        // qw: false\r\n      },\r\n      aiLoginDialogVisible: false,\r\n      currentAiType: '',\r\n      qrCodeUrl: '',\r\n      // 消息相关变量\r\n      messages: [],\r\n      messageInput: '',\r\n      isLoading: {\r\n        yuanbao: true,\r\n        doubao: true,\r\n        wenxin: true,\r\n        agent: true,\r\n        // qw: true\r\n      },\r\n    }\r\n  },\r\n  // 计算当前月份的签到日期\r\n  computed: {\r\n    calendarDates() {\r\n      const dates = [];\r\n      const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1);\r\n      const lastDay = new Date(this.currentYear, this.currentMonth, 0);\r\n\r\n      // Fill in empty slots before first day\r\n      for (let i = 0; i < firstDay.getDay(); i++) {\r\n        dates.push(null);\r\n      }\r\n\r\n      // Fill in days of the month\r\n      for (let i = 1; i <= lastDay.getDate(); i++) {\r\n        dates.push(new Date(this.currentYear, this.currentMonth - 1, i));\r\n      }\r\n\r\n      return dates;\r\n    },\r\n    getAiLoginTitle() {\r\n      const titles = {\r\n        yuanbao: '腾讯元宝登录',\r\n        doubao: '豆包登录',\r\n        agent: '智能体登录',\r\n        wenxin: '百度AI登录',\r\n        // qw: '通义千问登录'\r\n      };\r\n      return titles[this.currentAiType] || '登录';\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.getUser();\r\n\r\n  },\r\n  methods: {\r\n    handleSetLineChartData(type) {\r\n      this.lineChartData = lineChartData[type]\r\n    },\r\n    getUser() {\r\n      getUserProfile().then(response => {\r\n        this.user = response.data;\r\n        this.roleGroup = response.roleGroup;\r\n        this.postGroup = response.postGroup;\r\n        this.userId = response.data.userId;\r\n        this.corpId = response.data.corpId;\r\n\r\n        this.initWebSocket(this.userId); // 创建时建立连接\r\n\r\n        setTimeout(() => {\r\n          // 检查元宝登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_YB_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查豆包登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_DB_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查智能体登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_AGENT_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查百度AI登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_WX_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查通义千问登录状态\r\n          // this.sendMessage({\r\n          //   type: 'PLAY_CHECK_QW_LOGIN',\r\n          //   userId: this.userId,\r\n          //   corpId: this.corpId\r\n          // });\r\n        }, 1000);\r\n      });\r\n    },\r\n    // 获取公众号信息\r\n    handleBindWechat() {\r\n      getOfficeAccount().then(response => {\r\n        if (response.data != null) {\r\n          this.form.appId = response.data.appId;\r\n          this.form.appSecret = response.data.appSecret;\r\n          this.form.officeAccountName = response.data.officeAccountName;\r\n          this.form.picUrl = response.data.picUrl;\r\n        }\r\n        this.dialogFormVisible = true;\r\n      });\r\n    },\r\n    handleAgentBind() {\r\n      getAgentBind().then(response => {\r\n        if (response.data != null) {\r\n          this.agentForm.agentId = response.data.agent_id;\r\n          this.agentForm.agentToken = response.data.agent_token;\r\n        }\r\n        this.dialogAgentFormVisible = true;\r\n      });\r\n    },\r\n    handleSpaceBind() {\r\n      getSpaceInfoByUserId().then(response => {\r\n        if (response.data != null) {\r\n          this.spaceForm.spaceId = response.data.spaceId;\r\n          this.spaceForm.spaceName = response.data.spaceName;\r\n        }\r\n        this.dialogSpaceFormVisible = true;\r\n      });\r\n    },\r\n    // 绑定公众号\r\n    confirmBind() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          bindWcOfficeAccount(this.form).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 绑定公众号\r\n    confirmAgentBind() {\r\n      this.$refs.agentForm.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          saveAgentBind(this.agentForm).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogAgentFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    confirmSpaceBind() {\r\n      this.$refs.spaceForm.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          saveSpaceBind(this.spaceForm).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogSpaceFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 获取当前登录用户积分明细\r\n    showPointsDetail() {\r\n      this.queryPointForm.userId = this.user.userId\r\n      this.getUserPointsRecord();\r\n    },\r\n    // 获取积分明细\r\n    getUserPointsRecord(){\r\n      getUserPointsRecord(this.queryPointForm).then(response => {\r\n        this.openPointsRecord = true;\r\n        this.pointsRecordList = response.data.list;\r\n        this.pointtotal = response.data.total\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 获取当前月份的签到日期\r\n    isSignedDate(date) {\r\n      if (!date) return false;\r\n      return this.signedDates.some(signedDate =>\r\n        signedDate.getDate() === date.getDate() &&\r\n        signedDate.getMonth() === date.getMonth() &&\r\n        signedDate.getFullYear() === date.getFullYear()\r\n      );\r\n    },\r\n    isToday(date) {\r\n      if (!date) return false;\r\n      const today = new Date();\r\n      return date.getDate() === today.getDate() &&\r\n             date.getMonth() === today.getMonth() &&\r\n             date.getFullYear() === today.getFullYear();\r\n    },\r\n    isFutureDate(date) {\r\n      if (!date) return false;\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      return date > today;\r\n    },\r\n    handleSignIn() {\r\n      if (!this.todaySignedIn) {\r\n        this.todaySignedIn = true;\r\n        this.signedDates.push(new Date());\r\n        const now = new Date();\r\n        this.signInHistory.unshift({\r\n          date: `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`,\r\n          time: `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`\r\n        });\r\n        this.monthlySignInDays++;\r\n        this.totalSignInDays++;\r\n      }\r\n    },\r\n    handleAiLogin(type) {\r\n      this.currentAiType = type;\r\n      this.aiLoginDialogVisible = true;\r\n      this.isLoading[type] = true;\r\n      this.getQrCode(type);\r\n    },\r\n    getQrCode(type) {\r\n      this.qrCodeUrl = ''\r\n      if(type == 'yuanbao'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_YB_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'doubao'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_DB_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'agent'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_AGENT_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'wenxin'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_WX_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      // if(type == 'qw'){\r\n      //   this.sendMessage({\r\n      //     type: 'PLAY_GET_QW_QRCODE',\r\n      //     userId: this.userId,\r\n      //     corpId: this.corpId\r\n      //   });\r\n      // }\r\n      this.$message({\r\n        message: '正在获取登录二维码...',\r\n        type: 'info'\r\n      });\r\n    },\r\n    getPlatformIcon(type) {\r\n      const icons = {\r\n        yuanbao: require('@/assets/logo/yuanbao.png'),\r\n        doubao: require('@/assets/logo/doubao.png'),\r\n        agent: require('@/assets/logo/yuanbao.png'),\r\n        wenxin: require('@/assets/ai/yuanbao.png'),\r\n        qw: require('@/assets/logo/qw.png')\r\n      };\r\n      return icons[type] || '';\r\n    },\r\n    getPlatformName(type) {\r\n      const names = {\r\n        yuanbao: '腾讯元宝',\r\n        doubao: '豆包',\r\n        agent: '智能体',\r\n        wenxin: '百度AI',\r\n        // qw: '通义千问'\r\n      };\r\n      return names[type] || '';\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log('WebSocket URL:', process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case 'open':\r\n            this.$message.success('正在获取最新登录状态，请稍后...');\r\n            break;\r\n          case 'message':\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case 'close':\r\n            this.$message.warning('WebSocket连接已关闭');\r\n            break;\r\n          case 'error':\r\n            this.$message.error('WebSocket连接错误');\r\n            break;\r\n          case 'reconnect_failed':\r\n            this.$message.error('WebSocket重连失败，请刷新页面重试');\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n      console.log('收到消息:', data);\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      if (datastr.includes(\"RETURN_YB_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.yuanbao = true;\r\n          this.accounts.yuanbao = dataObj.status;\r\n          this.isLoading.yuanbao = false;\r\n        } else {\r\n          this.isClick.yuanbao = true;\r\n          this.isLoading.yuanbao = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_AGENT_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.agent = true;\r\n          this.accounts.agent = dataObj.status;\r\n          this.isLoading.agent = false;\r\n        } else {\r\n          this.isClick.agent = true;\r\n          this.isLoading.agent = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_PC_YB_QRURL\") || datastr.includes(\"RETURN_PC_DB_QRURL\") || datastr.includes(\"RETURN_PC_AGENT_QRURL\") || datastr.includes(\"RETURN_PC_QW_QRURL\")) {\r\n        this.qrCodeUrl = dataObj.url;\r\n      } else if (datastr.includes(\"RETURN_PC_WX_QRURL\")) {\r\n        // 百度AI不需要显示二维码图片，直接显示提示信息\r\n        this.qrCodeUrl = '';\r\n        this.$message({\r\n          message: '请在浏览器中扫码登录百度AI',\r\n          type: 'info',\r\n          duration: 3000\r\n        });\r\n      } else if (datastr.includes(\"RETURN_DB_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.doubao = true;\r\n          this.accounts.doubao = dataObj.status;\r\n          this.isLoading.doubao = false;\r\n        } else {\r\n          this.isClick.doubao = true;\r\n          this.isLoading.doubao = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_WX_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.wenxin = true;\r\n          this.accounts.wenxin = dataObj.status;\r\n          this.isLoading.wenxin = false;\r\n        } else {\r\n          this.isClick.wenxin = true;\r\n          this.isLoading.wenxin = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_QW_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.qw = true;\r\n          this.accounts.qw = dataObj.status;\r\n          this.isLoading.qw = false;\r\n        } else {\r\n          this.isClick.qw = true;\r\n          this.isLoading.qw = false;\r\n        }\r\n      }\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error('WebSocket未连接');\r\n      }\r\n    },\r\n    // 格式化时间\r\n    formatTime(date) {\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n      return `${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    // 滚动到底部\r\n    scrollToBottom() {\r\n      const messageList = this.$refs.messageList;\r\n      if (messageList) {\r\n        messageList.scrollTop = messageList.scrollHeight;\r\n      }\r\n    },\r\n    handleRefresh() {\r\n      window.location.reload();\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    this.closeWebSocket(); // 销毁时关闭连接\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-editor-container {\r\n  padding: 32px;\r\n  background-color: rgb(240, 242, 245);\r\n  position: relative;\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 32px;\r\n  }\r\n}\r\n\r\n@media (max-width:1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n\r\n// 签到日历样式\r\n.sign-in-container {\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  padding: 20px 16px;\r\n  font-size: 13px;\r\n  color: #333333;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stats-card {\r\n  flex: 1;\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  text-align: center;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.stats-number {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  margin-bottom: 8px;\r\n  color: #FF6B6B;\r\n}\r\n\r\n.stats-label {\r\n  color: #666666;\r\n}\r\n\r\n.calendar-section {\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.calendar-header {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.month-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  text-align: center;\r\n}\r\n\r\n.weekdays {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  text-align: center;\r\n  color: #666666;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.calendar-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  gap: 4px;\r\n}\r\n\r\n.calendar-day {\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 4px;\r\n}\r\n\r\n.calendar-day.signed {\r\n  background-color: #FF6B6B;\r\n  color: white;\r\n}\r\n\r\n.calendar-day.today {\r\n  border: 2px solid #FF6B6B;\r\n}\r\n\r\n.calendar-day.future {\r\n  color: #999999;\r\n}\r\n\r\n.calendar-day.empty {\r\n  background: none;\r\n}\r\n\r\n.sign-in-button {\r\n  width: 100%;\r\n  height: 44px;\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  background-color: #FF6B6B;\r\n  border: none;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.sign-in-button:disabled {\r\n  background-color: #CCCCCC;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.history-section {\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.history-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.history-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #EEEEEE;\r\n}\r\n\r\n.history-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-time {\r\n  color: #666666;\r\n}\r\n\r\n.pull-right .el-button--text {\r\n  padding: 0;\r\n  color: #409EFF;\r\n}\r\n\r\n.qr-code-container {\r\n  padding: 20px;\r\n  text-align: center;\r\n  min-height: 600px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.qr-code {\r\n  img {\r\n    width: 1600px;\r\n    height: 600px;\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.qr-tip {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.loading-tip {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.ai-status-card {\r\n  .card-title {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n\r\n    .title-icon {\r\n      margin-right: 8px;\r\n      font-size: 18px;\r\n      color: #409EFF;\r\n    }\r\n  }\r\n\r\n  .el-button--text {\r\n    color: #409EFF;\r\n    font-size: 14px;\r\n\r\n    &:hover {\r\n      color: #66b1ff;\r\n    }\r\n\r\n    i {\r\n      margin-right: 4px;\r\n    }\r\n  }\r\n\r\n  .ai-status-list {\r\n    .ai-status-item {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #EBEEF5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .ai-platform {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .platform-icon {\r\n          width: 32px;\r\n          height: 32px;\r\n          border-radius: 50%;\r\n          background: #F5F7FA;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 12px;\r\n          overflow: hidden;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n          }\r\n        }\r\n\r\n        .platform-name {\r\n          font-size: 14px;\r\n          color: #606266;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          .loading-icon {\r\n            margin-left: 8px;\r\n            color: #409EFF;\r\n            font-size: 16px;\r\n            animation: rotating 2s linear infinite;\r\n          }\r\n        }\r\n      }\r\n\r\n      .status-action {\r\n        .status-tag {\r\n          padding: 0px 12px;\r\n          border-radius: 16px;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n\r\n        .login-btn {\r\n          padding: 6px 12px;\r\n          border-radius: 16px;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.qr-code-container {\r\n  padding: 20px;\r\n  text-align: center;\r\n  min-height: 550px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #F5F7FA;\r\n  border-radius: 8px;\r\n}\r\n\r\n.qr-code {\r\n  background: #FFFFFF;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);\r\n\r\n  img {\r\n    width: 1000px;\r\n    height: 550px;\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.qr-tip {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n  font-weight: 500;\r\n}\r\n\r\n.loading-tip {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  &::before {\r\n    content: '';\r\n    display: inline-block;\r\n    width: 16px;\r\n    height: 16px;\r\n    margin-right: 8px;\r\n    border: 2px solid #DCDFE6;\r\n    border-top-color: #409EFF;\r\n    border-radius: 50%;\r\n    animation: loading 1s linear infinite;\r\n  }\r\n}\r\n\r\n@keyframes loading {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.message-card {\r\n  margin-top: 20px;\r\n\r\n  .message-list {\r\n    height: 300px;\r\n    overflow-y: auto;\r\n    padding: 10px;\r\n    background: #f5f7fa;\r\n    border-radius: 4px;\r\n\r\n    .message-item {\r\n      margin-bottom: 10px;\r\n\r\n      .message-content {\r\n        max-width: 80%;\r\n\r\n        .message-time {\r\n          font-size: 12px;\r\n          color: #909399;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .message-text {\r\n          padding: 8px 12px;\r\n          border-radius: 4px;\r\n          word-break: break-all;\r\n        }\r\n      }\r\n    }\r\n\r\n    .message-send {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n\r\n      .message-content {\r\n        .message-text {\r\n          background: #409EFF;\r\n          color: white;\r\n        }\r\n      }\r\n    }\r\n\r\n    .message-receive {\r\n      display: flex;\r\n      justify-content: flex-start;\r\n\r\n      .message-content {\r\n        .message-text {\r\n          background: white;\r\n          color: #303133;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-input {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n@keyframes rotating {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n</style>\r\n"]}]}