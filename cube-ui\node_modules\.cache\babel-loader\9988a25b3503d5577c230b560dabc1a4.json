{"remainingRequest": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\src\\views\\index.vue", "mtime": 1751901200955}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\babel.config.js", "mtime": 1751782516642}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1751784291203}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1751784291169}, {"path": "D:\\JavaWorkSpace\\U3W-AI\\cube-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1751784287559}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_PanelGroup", "_interopRequireDefault", "require", "_Line<PERSON><PERSON>", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON>", "_<PERSON><PERSON><PERSON>", "_userAvatar", "_userInfo", "_resetPwd", "_user", "_company", "_websocket", "lineChartData", "new<PERSON><PERSON><PERSON>", "expectedData", "actualData", "messages", "purchases", "shoppings", "_default", "exports", "default", "name", "components", "PanelGroup", "Line<PERSON>hart", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "userAvatar", "userInfo", "resetPwd", "data", "user", "roleGroup", "postGroup", "activeTab", "dialogFormVisible", "dialogAgentFormVisible", "dialogSpaceFormVisible", "form", "appId", "appSecret", "officeAccountName", "picUrl", "agentForm", "agentId", "agentToken", "spaceForm", "spaceId", "spaceName", "form<PERSON>abe<PERSON><PERSON>", "rules", "required", "message", "trigger", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "loading", "changeType", "label", "value", "openPointsRecord", "pointtotal", "queryPointForm", "limit", "page", "type", "userId", "pointsRecordList", "weekDays", "currentYear", "Date", "getFullYear", "currentMonth", "getMonth", "continuousSignInDays", "monthlySignInDays", "totalSignInDays", "todaySignedIn", "signInHistory", "date", "time", "signedDates", "aiLoginStatus", "yuanbao", "do<PERSON>o", "agent", "wenxin", "accounts", "isClick", "aiLoginDialogVisible", "currentAiType", "qrCodeUrl", "messageInput", "isLoading", "computed", "calendarDates", "dates", "firstDay", "lastDay", "i", "getDay", "push", "getDate", "getAiLoginTitle", "titles", "created", "getUser", "methods", "handleSetLineChartData", "_this", "getUserProfile", "then", "response", "corpId", "initWebSocket", "setTimeout", "sendMessage", "handleBindWechat", "_this2", "getOfficeAccount", "handleAgentBind", "_this3", "getAgentBind", "agent_id", "agent_token", "handleSpaceBind", "_this4", "getSpaceInfoByUserId", "confirmBind", "_this5", "$refs", "validate", "valid", "bindWcOfficeAccount", "$message", "success", "confirmAgentBind", "_this6", "saveAgentBind", "confirmSpaceBind", "_this7", "saveSpaceBind", "showPointsDetail", "getUserPointsRecord", "_this8", "list", "total", "isSignedDate", "some", "signedDate", "isToday", "today", "isFutureDate", "setHours", "handleSignIn", "now", "unshift", "concat", "String", "padStart", "getHours", "getMinutes", "getSeconds", "handleAiLogin", "getQrCode", "getPlatformIcon", "icons", "qw", "getPlatformName", "names", "id", "_this9", "wsUrl", "process", "env", "VUE_APP_WS_API", "console", "log", "websocketClient", "connect", "event", "handleWebSocketMessage", "warning", "error", "datastr", "dataObj", "JSON", "parse", "includes", "status", "url", "duration", "closeWebSocket", "close", "_this0", "send", "$nextTick", "scrollToBottom", "formatTime", "hours", "minutes", "seconds", "messageList", "scrollTop", "scrollHeight", "handleRefresh", "window", "location", "reload", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["src/views/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"dashboard-editor-container\">\r\n\r\n<!--    <panel-group @handleSetLineChartData=\"handleSetLineChartData\" />-->\r\n    <div class=\"app-container\">\r\n      <el-row :gutter=\"30\">\r\n        <el-col :span=\"6\" :xs=\"30\">\r\n          <el-card class=\"box-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span>个人信息</span>\r\n            </div>\r\n            <div>\r\n              <div class=\"text-center\">\r\n                <userAvatar />\r\n              </div>\r\n              <ul class=\"list-group list-group-striped\">\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />用户名称\r\n                  <div class=\"pull-right\" id=\"userName\">{{ user.nickName }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"phone\" />手机号码\r\n                  <div class=\"pull-right\">{{ user.phonenumber }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"date\" />创建日期\r\n                  <div class=\"pull-right\">{{ user.createTime }}</div>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <svg-icon icon-class=\"user\" />\r\n                  <span>积分余额</span>\r\n                  <div  :style=\"{ color: user.points >= 0 ? 'green' : 'red' }\" class=\"pull-right\">{{ user.points }}</div>\r\n                  <el-tooltip content=\"点击可查看积分明细\" placement=\"top\" effect=\"light\">\r\n                    <i class=\"el-icon-chat-dot-round\" @click=\"showPointsDetail\"></i>\r\n                  </el-tooltip>\r\n                </li>\r\n                <li class=\"list-group-item\">\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleBindWechat\">绑定公众号</el-button>\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleAgentBind\">Agent API设置</el-button>\r\n                  <el-button type=\"primary\" size=\"mini\" @click=\"handleSpaceBind\">元器空间绑定</el-button>\r\n\r\n\r\n                  <el-dialog title=\"公众号智能体API配置\" :visible.sync=\"dialogAgentFormVisible\">\r\n                    <el-form :model=\"agentForm\" :rules=\"agentrules\" ref=\"agentForm\" >\r\n                      <el-form-item label=\"智能体ID\" :label-width=\"formLabelWidth\" prop=\"agentId\">\r\n                        <el-input v-model=\"agentForm.agentId\" maxlength=\"32\"  placeholder=\"请输入agentId\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"智能体Token\" :label-width=\"formLabelWidth\" prop=\"agentToken\">\r\n                        <el-input v-model=\"agentForm.agentToken\" maxlength=\"50\"  placeholder=\"请输入agentToken\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogAgentFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmAgentBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n\r\n                  <el-dialog title=\"元器空间绑定\" :visible.sync=\"dialogSpaceFormVisible\">\r\n                    <el-form :model=\"spaceForm\" :rules=\"spacerules\" ref=\"spaceForm\" >\r\n                      <el-form-item label=\"空间ID\" :label-width=\"formLabelWidth\" prop=\"spaceId\">\r\n                        <el-input v-model=\"spaceForm.spaceId\" maxlength=\"32\"  placeholder=\"请输入空间ID\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"空间名称\" :label-width=\"formLabelWidth\" prop=\"spaceName\">\r\n                        <el-input v-model=\"spaceForm.spaceName\" maxlength=\"50\"  placeholder=\"请输入空间名称\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogSpaceFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmSpaceBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n\r\n                  <el-dialog title=\"绑定微信公众号\" :visible.sync=\"dialogFormVisible\">\r\n                    <el-form :model=\"form\" :rules=\"rules\" ref=\"form\" >\r\n                      <el-form-item label=\"appId\" :label-width=\"formLabelWidth\" prop=\"appId\">\r\n                        <el-input v-model=\"form.appId\" maxlength=\"32\"  placeholder=\"请输入appId\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"appSecret\" :label-width=\"formLabelWidth\" prop=\"appSecret\">\r\n                        <el-input v-model=\"form.appSecret\" maxlength=\"50\"  placeholder=\"请输入appSecret\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"公众号名称\" :label-width=\"formLabelWidth\" prop=\"officeAccountName\">\r\n                        <el-input v-model=\"form.officeAccountName\" maxlength=\"50\"  placeholder=\"请输入公众号名称\" autocomplete=\"off\"></el-input>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"素材封面图\" :label-width=\"formLabelWidth\" prop=\"picUrl\">\r\n                        <image-upload v-model=\"form.picUrl\"/>\r\n                      </el-form-item>\r\n                      <el-form-item label=\"规范说明\" :label-width=\"formLabelWidth\">\r\n                        <div style=\"color: #f56c6c; font-size: 13px;\">\r\n                          请把IP: *************** 添加到公众号IP白名单。步骤：登录微信公众平台→点击设置与开发→安全中心→IP白名单。一般一小时后生效。\r\n                        </div>\r\n                      </el-form-item>\r\n                    </el-form>\r\n                    <div slot=\"footer\" class=\"dialog-footer\">\r\n                      <el-button @click=\"dialogFormVisible = false\">取 消</el-button>\r\n                      <el-button type=\"primary\" @click=\"confirmBind\">确 定</el-button>\r\n                    </div>\r\n                  </el-dialog>\r\n                </li>\r\n              </ul>\r\n            </div>\r\n          </el-card>\r\n        </el-col>\r\n        <el-col :span=\"6\" :xs=\"30\">\r\n          <el-card class=\"box-card ai-status-card\">\r\n            <div slot=\"header\" class=\"clearfix\">\r\n              <span class=\"card-title\">\r\n                <svg-icon icon-class=\"ai\" class=\"title-icon\" />\r\n                AI 登录状态\r\n              </span>\r\n              <el-button\r\n                style=\"float: right; margin-top: -30px;\"\r\n                type=\"text\"\r\n                @click=\"handleRefresh\"\r\n              >\r\n                <i class=\"el-icon-refresh\"></i> 刷新\r\n              </el-button>\r\n            </div>\r\n            <div class=\"ai-status-list\">\r\n              <div class=\"ai-status-item\" v-for=\"(status, type) in aiLoginStatus\" :key=\"type\">\r\n                <div class=\"ai-platform\">\r\n                  <div class=\"platform-icon\">\r\n                    <img :src=\"getPlatformIcon(type)\" :alt=\"getPlatformName(type)\" />\r\n                  </div>\r\n                  <div class=\"platform-name\">\r\n                    {{ getPlatformName(type) }}\r\n                    <el-tooltip v-if=\"isLoading[type]\" content=\"正在登录中...\" placement=\"top\">\r\n                      <i class=\"el-icon-loading loading-icon\"></i>\r\n                    </el-tooltip>\r\n                  </div>\r\n                </div>\r\n                <div class=\"status-action\">\r\n                  <el-tag v-if=\"status\" type=\"success\" effect=\"dark\" class=\"status-tag\">\r\n                    <i class=\"el-icon-success\"></i> <span>{{ accounts[type] }}</span>\r\n                  </el-tag>\r\n                  <el-button v-else type=\"primary\" size=\"small\" :disabled=\"!isClick[type]\" @click=\"handleAiLogin(type)\" class=\"login-btn\">\r\n                    <i class=\"el-icon-connection\"></i> 点击登录\r\n                  </el-button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-card>\r\n\r\n\r\n\r\n        </el-col>\r\n      </el-row>\r\n    </div>\r\n\r\n    <!-- AI登录二维码对话框 -->\r\n    <el-dialog :title=\"getAiLoginTitle\" :visible.sync=\"aiLoginDialogVisible\" width=\"1200px\" height=\"800px\" center>\r\n      <div class=\"qr-code-container\" v-loading=\"!qrCodeUrl\">\r\n        <div v-if=\"qrCodeUrl\" class=\"qr-code\">\r\n          <img style=\"width: 100%;height: 100%;\" :src=\"qrCodeUrl\" alt=\"登录二维码\" />\r\n          <p class=\"qr-tip\">请使用对应AI平台APP扫码登录</p>\r\n        </div>\r\n        <div v-else class=\"loading-tip\">\r\n          正在获取登录二维码...\r\n        </div>\r\n      </div>\r\n    </el-dialog>\r\n\r\n    <el-dialog title=\"积分详细\" :visible.sync=\"openPointsRecord\" width=\"1000px\" append-to-body>\r\n        <el-select\r\n          v-model=\"queryPointForm.type\"\r\n          placeholder=\"积分类型\"\r\n          clearable\r\n          style=\"width: 240px;margin-bottom: 10px\"\r\n          @change=\"getUserPointsRecord\"\r\n        >\r\n          <el-option\r\n            v-for=\"dict in changeType\"\r\n            :key=\"dict.value\"\r\n            :label=\"dict.label\"\r\n            :value=\"dict.value\"\r\n          />\r\n        </el-select>\r\n      <el-table  v-loading=\"loading\" :data=\"pointsRecordList\">\r\n        <el-table-column label=\"用户昵称\" align=\"center\" key=\"nick_name\" prop=\"nick_name\"  :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"变更数量\" align=\"center\" key=\"change_amount\" prop=\"change_amount\" :show-overflow-tooltip=\"true\">\r\n          <template slot-scope=\"scope\">\r\n                <span :style=\"{ color: scope.row.change_amount >= 0 ? 'green' : 'red' }\">\r\n                  {{ scope.row.change_amount }}\r\n                </span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"积分余额\" align=\"center\" key=\"balance_after\" prop=\"balance_after\" :show-overflow-tooltip=\"true\" />\r\n        <el-table-column label=\"变更类型\" align=\"center\" key=\"change_type\" prop=\"change_type\"   />\r\n        <el-table-column  width=\"200\" label=\"变更时间\" align=\"center\" prop=\"create_time\" >\r\n          <template slot-scope=\"scope\">\r\n            <span>{{ parseTime(scope.row.create_time) }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column label=\"操作人\" align=\"center\" key=\"create_name\" prop=\"create_name\"   />\r\n        <el-table-column label=\"备注\" align=\"center\" key=\"remark\" prop=\"remark\"   />\r\n\r\n      </el-table>\r\n      <pagination\r\n        v-show=\"pointtotal>0\"\r\n        :total=\"pointtotal\"\r\n        :page.sync=\"queryPointForm.page\"\r\n        :limit.sync=\"queryPointForm.limit\"\r\n        @pagination=\"getUserPointsRecord\"\r\n      />\r\n    </el-dialog>\r\n  </div>\r\n\r\n</template>\r\n\r\n<script>\r\nimport PanelGroup from './dashboard/PanelGroup'\r\nimport LineChart from './dashboard/LineChart'\r\nimport RaddarChart from './dashboard/RaddarChart'\r\nimport PieChart from './dashboard/PieChart'\r\nimport BarChart from './dashboard/BarChart'\r\nimport userAvatar from \"@/views/system/user/profile/userAvatar\";\r\nimport userInfo from \"@/views/system/user/profile/userInfo\";\r\nimport resetPwd from \"@/views/system/user/profile/resetPwd\";\r\nimport {getUserProfile, bindWcOfficeAccount, getOfficeAccount, getAgentBind, saveAgentBind, saveSpaceBind, getSpaceInfoByUserId} from \"@/api/system/user\";\r\nimport {getUserPointsRecord } from \"@/api/wechat/company\";\r\nimport websocketClient from '@/utils/websocket';\r\n\r\nconst lineChartData = {\r\n  newVisitis: {\r\n    expectedData: [100, 120, 161, 134, 105, 160, 165],\r\n    actualData: [120, 82, 91, 154, 162, 140, 145]\r\n  },\r\n  messages: {\r\n    expectedData: [200, 192, 120, 144, 160, 130, 140],\r\n    actualData: [180, 160, 151, 106, 145, 150, 130]\r\n  },\r\n  purchases: {\r\n    expectedData: [80, 100, 121, 104, 105, 90, 100],\r\n    actualData: [120, 90, 100, 138, 142, 130, 130]\r\n  },\r\n  shoppings: {\r\n    expectedData: [130, 140, 141, 142, 145, 150, 160],\r\n    actualData: [120, 82, 91, 154, 162, 140, 130]\r\n  }\r\n}\r\n\r\nexport default {\r\n  name: 'Index',\r\n  components: {\r\n    PanelGroup,\r\n    LineChart,\r\n    RaddarChart,\r\n    PieChart,\r\n    BarChart,userAvatar, userInfo, resetPwd\r\n  },\r\n  data() {\r\n    return {\r\n      lineChartData: lineChartData.newVisitis,\r\n      user: {},\r\n      roleGroup: {},\r\n      postGroup: {},\r\n      activeTab: \"userinfo\",\r\n      //------ 绑定公众号相关变量 ------//\r\n      dialogFormVisible: false, // 绑定公众号弹窗\r\n      dialogAgentFormVisible: false, // 绑定智能体弹窗\r\n      dialogSpaceFormVisible: false, // 绑定元器空间弹窗\r\n      form: {\r\n        appId: '', // 公众号appId\r\n        appSecret: '', // 公众号appSecret\r\n        officeAccountName: '', // 公众号名称\r\n        picUrl: '', // 公众号封面图\r\n      },\r\n      agentForm: {\r\n        agentId: '', // 智能体ID\r\n        agentToken: '', // 智能体token\r\n      },\r\n      spaceForm: {\r\n        spaceId: '', // 空间ID\r\n        spaceName: '', // 空间名称\r\n      },\r\n      formLabelWidth: '120px', //输入框宽度\r\n      // 绑定公众号表单验证规则\r\n      rules: {\r\n        appId: [\r\n          { required: true, message: '请输入appId', trigger: 'blur' }\r\n        ],\r\n        appSecret: [\r\n          { required: true, message: '请输入appSecret', trigger: 'blur' }\r\n        ],\r\n        officeAccountName: [\r\n          { required: false, message: '请输入公众号名称', trigger: 'blur' }\r\n        ]\r\n      },\r\n      agentrules: {\r\n        agentId: [\r\n          { required: true, message: '请输入agentId', trigger: 'blur' }\r\n        ],\r\n        agentToken: [\r\n          { required: true, message: '请输入agentToken', trigger: 'blur' }\r\n        ]\r\n      },\r\n      spacerules: {\r\n        spaceId: [\r\n          { required: true, message: '请输入空间ID', trigger: 'blur' }\r\n        ],\r\n        spaceName: [\r\n          { required: true, message: '请输入空间名称', trigger: 'blur' }\r\n        ]\r\n      },\r\n\r\n      //------ 积分相关变量 ------//\r\n      loading: true, // 遮罩层\r\n      changeType:[\r\n        {\r\n          label:\"全部\",\r\n          value:\"0\"\r\n        },\r\n        {\r\n        label:\"增加\",\r\n        value:\"1\"\r\n      },\r\n        {\r\n          label:\"消耗\",\r\n          value:\"2\"\r\n        },\r\n      ],  // 积分明细表中的积分类型\r\n      openPointsRecord: false, // 积分明细弹窗\r\n      pointtotal: 0, // 积分明细总数\r\n      queryPointForm:{\r\n        limit:10,\r\n        page:1,\r\n        type:'',\r\n        userId:''\r\n      }, // 积分明细查询需要的查询参数\r\n      pointsRecordList: null, // 积分明细列表\r\n\r\n      //------ 签到相关变量 ------//\r\n      weekDays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],\r\n      currentYear: new Date().getFullYear(),\r\n      currentMonth: new Date().getMonth() + 1,\r\n      continuousSignInDays: 7,\r\n      monthlySignInDays: 15,\r\n      totalSignInDays: 128,\r\n      todaySignedIn: false,\r\n      signInHistory: [\r\n        { date: '2024-01-15', time: '08:30:25' },\r\n        { date: '2024-01-14', time: '09:15:33' },\r\n        { date: '2024-01-13', time: '07:45:12' },\r\n        { date: '2024-01-12', time: '08:20:45' },\r\n        { date: '2024-01-11', time: '09:00:18' }\r\n      ],\r\n      signedDates: [\r\n        new Date(2024, 0, 1),\r\n        new Date(2024, 0, 2),\r\n        new Date(2024, 0, 3),\r\n        new Date(2024, 0, 4),\r\n        new Date(2024, 0, 5)\r\n      ],\r\n      aiLoginStatus: {\r\n        yuanbao: false,\r\n        doubao: false,\r\n        agent: false,\r\n        wenxin: false,\r\n        // qw: false\r\n      },\r\n      accounts: {\r\n        yuanbao: '',\r\n        doubao: '',\r\n        agent: '',\r\n        wenxin: '',\r\n        // qw: ''\r\n      },\r\n      isClick: {\r\n        yuanbao: false,\r\n        doubao: false,\r\n        agent: false,\r\n        wenxin: false,\r\n        // qw: false\r\n      },\r\n      aiLoginDialogVisible: false,\r\n      currentAiType: '',\r\n      qrCodeUrl: '',\r\n      // 消息相关变量\r\n      messages: [],\r\n      messageInput: '',\r\n      isLoading: {\r\n        yuanbao: true,\r\n        doubao: true,\r\n        wenxin: true,\r\n        agent: true,\r\n        // qw: true\r\n      },\r\n    }\r\n  },\r\n  // 计算当前月份的签到日期\r\n  computed: {\r\n    calendarDates() {\r\n      const dates = [];\r\n      const firstDay = new Date(this.currentYear, this.currentMonth - 1, 1);\r\n      const lastDay = new Date(this.currentYear, this.currentMonth, 0);\r\n\r\n      // Fill in empty slots before first day\r\n      for (let i = 0; i < firstDay.getDay(); i++) {\r\n        dates.push(null);\r\n      }\r\n\r\n      // Fill in days of the month\r\n      for (let i = 1; i <= lastDay.getDate(); i++) {\r\n        dates.push(new Date(this.currentYear, this.currentMonth - 1, i));\r\n      }\r\n\r\n      return dates;\r\n    },\r\n    getAiLoginTitle() {\r\n      const titles = {\r\n        yuanbao: '腾讯元宝登录',\r\n        doubao: '豆包登录',\r\n        agent: '智能体登录',\r\n        wenxin: '百度AI登录',\r\n        // qw: '通义千问登录'\r\n      };\r\n      return titles[this.currentAiType] || '登录';\r\n    }\r\n  },\r\n\r\n  created() {\r\n    this.getUser();\r\n\r\n  },\r\n  methods: {\r\n    handleSetLineChartData(type) {\r\n      this.lineChartData = lineChartData[type]\r\n    },\r\n    getUser() {\r\n      getUserProfile().then(response => {\r\n        this.user = response.data;\r\n        this.roleGroup = response.roleGroup;\r\n        this.postGroup = response.postGroup;\r\n        this.userId = response.data.userId;\r\n        this.corpId = response.data.corpId;\r\n\r\n        this.initWebSocket(this.userId); // 创建时建立连接\r\n\r\n        setTimeout(() => {\r\n          // 检查元宝登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_YB_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查豆包登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_DB_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查智能体登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_AGENT_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查百度AI登录状态\r\n          this.sendMessage({\r\n            type: 'PLAY_CHECK_WX_LOGIN',\r\n            userId: this.userId,\r\n            corpId: this.corpId\r\n          });\r\n\r\n          // 检查通义千问登录状态\r\n          // this.sendMessage({\r\n          //   type: 'PLAY_CHECK_QW_LOGIN',\r\n          //   userId: this.userId,\r\n          //   corpId: this.corpId\r\n          // });\r\n        }, 1000);\r\n      });\r\n    },\r\n    // 获取公众号信息\r\n    handleBindWechat() {\r\n      getOfficeAccount().then(response => {\r\n        if (response.data != null) {\r\n          this.form.appId = response.data.appId;\r\n          this.form.appSecret = response.data.appSecret;\r\n          this.form.officeAccountName = response.data.officeAccountName;\r\n          this.form.picUrl = response.data.picUrl;\r\n        }\r\n        this.dialogFormVisible = true;\r\n      });\r\n    },\r\n    handleAgentBind() {\r\n      getAgentBind().then(response => {\r\n        if (response.data != null) {\r\n          this.agentForm.agentId = response.data.agent_id;\r\n          this.agentForm.agentToken = response.data.agent_token;\r\n        }\r\n        this.dialogAgentFormVisible = true;\r\n      });\r\n    },\r\n    handleSpaceBind() {\r\n      getSpaceInfoByUserId().then(response => {\r\n        if (response.data != null) {\r\n          this.spaceForm.spaceId = response.data.spaceId;\r\n          this.spaceForm.spaceName = response.data.spaceName;\r\n        }\r\n        this.dialogSpaceFormVisible = true;\r\n      });\r\n    },\r\n    // 绑定公众号\r\n    confirmBind() {\r\n      this.$refs.form.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          bindWcOfficeAccount(this.form).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 绑定公众号\r\n    confirmAgentBind() {\r\n      this.$refs.agentForm.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          saveAgentBind(this.agentForm).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogAgentFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    confirmSpaceBind() {\r\n      this.$refs.spaceForm.validate((valid) => {\r\n        if (valid) {\r\n          // 表单验证通过，继续提交\r\n          saveSpaceBind(this.spaceForm).then(response => {\r\n            this.$message.success(response.data);\r\n            this.dialogSpaceFormVisible = false;\r\n          })\r\n        } else {\r\n          // 表单验证失败\r\n          return false;\r\n        }\r\n      });\r\n    },\r\n    // 获取当前登录用户积分明细\r\n    showPointsDetail() {\r\n      this.queryPointForm.userId = this.user.userId\r\n      this.getUserPointsRecord();\r\n    },\r\n    // 获取积分明细\r\n    getUserPointsRecord(){\r\n      getUserPointsRecord(this.queryPointForm).then(response => {\r\n        this.openPointsRecord = true;\r\n        this.pointsRecordList = response.data.list;\r\n        this.pointtotal = response.data.total\r\n        this.loading = false;\r\n      });\r\n    },\r\n    // 获取当前月份的签到日期\r\n    isSignedDate(date) {\r\n      if (!date) return false;\r\n      return this.signedDates.some(signedDate =>\r\n        signedDate.getDate() === date.getDate() &&\r\n        signedDate.getMonth() === date.getMonth() &&\r\n        signedDate.getFullYear() === date.getFullYear()\r\n      );\r\n    },\r\n    isToday(date) {\r\n      if (!date) return false;\r\n      const today = new Date();\r\n      return date.getDate() === today.getDate() &&\r\n             date.getMonth() === today.getMonth() &&\r\n             date.getFullYear() === today.getFullYear();\r\n    },\r\n    isFutureDate(date) {\r\n      if (!date) return false;\r\n      const today = new Date();\r\n      today.setHours(0, 0, 0, 0);\r\n      return date > today;\r\n    },\r\n    handleSignIn() {\r\n      if (!this.todaySignedIn) {\r\n        this.todaySignedIn = true;\r\n        this.signedDates.push(new Date());\r\n        const now = new Date();\r\n        this.signInHistory.unshift({\r\n          date: `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`,\r\n          time: `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`\r\n        });\r\n        this.monthlySignInDays++;\r\n        this.totalSignInDays++;\r\n      }\r\n    },\r\n    handleAiLogin(type) {\r\n      this.currentAiType = type;\r\n      this.aiLoginDialogVisible = true;\r\n      this.isLoading[type] = true;\r\n      this.getQrCode(type);\r\n    },\r\n    getQrCode(type) {\r\n      this.qrCodeUrl = ''\r\n      if(type == 'yuanbao'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_YB_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'doubao'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_DB_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'agent'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_AGENT_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      if(type == 'wenxin'){\r\n        this.sendMessage({\r\n          type: 'PLAY_GET_WX_QRCODE',\r\n          userId: this.userId,\r\n          corpId: this.corpId\r\n        });\r\n      }\r\n      // if(type == 'qw'){\r\n      //   this.sendMessage({\r\n      //     type: 'PLAY_GET_QW_QRCODE',\r\n      //     userId: this.userId,\r\n      //     corpId: this.corpId\r\n      //   });\r\n      // }\r\n      this.$message({\r\n        message: '正在获取登录二维码...',\r\n        type: 'info'\r\n      });\r\n    },\r\n    getPlatformIcon(type) {\r\n      const icons = {\r\n        yuanbao: require('@/assets/logo/yuanbao.png'),\r\n        doubao: require('@/assets/logo/doubao.png'),\r\n        agent: require('@/assets/logo/yuanbao.png'),\r\n        wenxin: require('@/assets/ai/yuanbao.png'),\r\n        qw: require('@/assets/logo/qw.png')\r\n      };\r\n      return icons[type] || '';\r\n    },\r\n    getPlatformName(type) {\r\n      const names = {\r\n        yuanbao: '腾讯元宝',\r\n        doubao: '豆包',\r\n        agent: '智能体',\r\n        wenxin: '百度AI',\r\n        // qw: '通义千问'\r\n      };\r\n      return names[type] || '';\r\n    },\r\n    // WebSocket 相关方法\r\n    initWebSocket(id) {\r\n      const wsUrl = process.env.VUE_APP_WS_API + `mypc-${id}`;\r\n      console.log('WebSocket URL:', process.env.VUE_APP_WS_API);\r\n      websocketClient.connect(wsUrl, (event) => {\r\n        switch (event.type) {\r\n          case 'open':\r\n            this.$message.success('正在获取最新登录状态，请稍后...');\r\n            break;\r\n          case 'message':\r\n            this.handleWebSocketMessage(event.data);\r\n            break;\r\n          case 'close':\r\n            this.$message.warning('WebSocket连接已关闭');\r\n            break;\r\n          case 'error':\r\n            this.$message.error('WebSocket连接错误');\r\n            break;\r\n          case 'reconnect_failed':\r\n            this.$message.error('WebSocket重连失败，请刷新页面重试');\r\n            break;\r\n        }\r\n      });\r\n    },\r\n\r\n    handleWebSocketMessage(data) {\r\n      console.log('收到消息:', data);\r\n      const datastr = data;\r\n      const dataObj = JSON.parse(datastr);\r\n\r\n      if (datastr.includes(\"RETURN_YB_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.yuanbao = true;\r\n          this.accounts.yuanbao = dataObj.status;\r\n          this.isLoading.yuanbao = false;\r\n        } else {\r\n          this.isClick.yuanbao = true;\r\n          this.isLoading.yuanbao = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_AGENT_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.agent = true;\r\n          this.accounts.agent = dataObj.status;\r\n          this.isLoading.agent = false;\r\n        } else {\r\n          this.isClick.agent = true;\r\n          this.isLoading.agent = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_PC_YB_QRURL\") || datastr.includes(\"RETURN_PC_DB_QRURL\") || datastr.includes(\"RETURN_PC_AGENT_QRURL\") || datastr.includes(\"RETURN_PC_QW_QRURL\")) {\r\n        this.qrCodeUrl = dataObj.url;\r\n      } else if (datastr.includes(\"RETURN_PC_WX_QRURL\")) {\r\n        // 百度AI不需要显示二维码图片，直接显示提示信息\r\n        this.qrCodeUrl = '';\r\n        this.$message({\r\n          message: '请在浏览器中扫码登录百度AI',\r\n          type: 'info',\r\n          duration: 3000\r\n        });\r\n      } else if (datastr.includes(\"RETURN_DB_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.doubao = true;\r\n          this.accounts.doubao = dataObj.status;\r\n          this.isLoading.doubao = false;\r\n        } else {\r\n          this.isClick.doubao = true;\r\n          this.isLoading.doubao = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_WX_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.wenxin = true;\r\n          this.accounts.wenxin = dataObj.status;\r\n          this.isLoading.wenxin = false;\r\n        } else {\r\n          this.isClick.wenxin = true;\r\n          this.isLoading.wenxin = false;\r\n        }\r\n      } else if (datastr.includes(\"RETURN_QW_STATUS\") && dataObj.status != '') {\r\n        if (!datastr.includes(\"false\")) {\r\n          this.aiLoginDialogVisible = false;\r\n          this.aiLoginStatus.qw = true;\r\n          this.accounts.qw = dataObj.status;\r\n          this.isLoading.qw = false;\r\n        } else {\r\n          this.isClick.qw = true;\r\n          this.isLoading.qw = false;\r\n        }\r\n      }\r\n    },\r\n\r\n    closeWebSocket() {\r\n      websocketClient.close();\r\n    },\r\n\r\n    sendMessage(data) {\r\n      if (websocketClient.send(data)) {\r\n        // 滚动到底部\r\n        this.$nextTick(() => {\r\n          this.scrollToBottom();\r\n        });\r\n      } else {\r\n        this.$message.error('WebSocket未连接');\r\n      }\r\n    },\r\n    // 格式化时间\r\n    formatTime(date) {\r\n      const hours = String(date.getHours()).padStart(2, '0');\r\n      const minutes = String(date.getMinutes()).padStart(2, '0');\r\n      const seconds = String(date.getSeconds()).padStart(2, '0');\r\n      return `${hours}:${minutes}:${seconds}`;\r\n    },\r\n\r\n    // 滚动到底部\r\n    scrollToBottom() {\r\n      const messageList = this.$refs.messageList;\r\n      if (messageList) {\r\n        messageList.scrollTop = messageList.scrollHeight;\r\n      }\r\n    },\r\n    handleRefresh() {\r\n      window.location.reload();\r\n    },\r\n  },\r\n  beforeDestroy() {\r\n    this.closeWebSocket(); // 销毁时关闭连接\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dashboard-editor-container {\r\n  padding: 32px;\r\n  background-color: rgb(240, 242, 245);\r\n  position: relative;\r\n\r\n  .chart-wrapper {\r\n    background: #fff;\r\n    padding: 16px 16px 0;\r\n    margin-bottom: 32px;\r\n  }\r\n}\r\n\r\n@media (max-width:1024px) {\r\n  .chart-wrapper {\r\n    padding: 8px;\r\n  }\r\n}\r\n\r\n// 签到日历样式\r\n.sign-in-container {\r\n  max-width: 600px;\r\n  margin: 0 auto;\r\n  padding: 20px 16px;\r\n  font-size: 13px;\r\n  color: #333333;\r\n}\r\n\r\n.stats-cards {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  gap: 16px;\r\n  margin-bottom: 20px;\r\n}\r\n\r\n.stats-card {\r\n  flex: 1;\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  text-align: center;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.stats-number {\r\n  font-size: 20px;\r\n  font-weight: bold;\r\n  margin-bottom: 8px;\r\n  color: #FF6B6B;\r\n}\r\n\r\n.stats-label {\r\n  color: #666666;\r\n}\r\n\r\n.calendar-section {\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  margin-bottom: 20px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.calendar-header {\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.month-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  text-align: center;\r\n}\r\n\r\n.weekdays {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  text-align: center;\r\n  color: #666666;\r\n  margin-bottom: 8px;\r\n}\r\n\r\n.calendar-grid {\r\n  display: grid;\r\n  grid-template-columns: repeat(7, 1fr);\r\n  gap: 4px;\r\n}\r\n\r\n.calendar-day {\r\n  height: 40px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border-radius: 4px;\r\n}\r\n\r\n.calendar-day.signed {\r\n  background-color: #FF6B6B;\r\n  color: white;\r\n}\r\n\r\n.calendar-day.today {\r\n  border: 2px solid #FF6B6B;\r\n}\r\n\r\n.calendar-day.future {\r\n  color: #999999;\r\n}\r\n\r\n.calendar-day.empty {\r\n  background: none;\r\n}\r\n\r\n.sign-in-button {\r\n  width: 100%;\r\n  height: 44px;\r\n  font-size: 16px;\r\n  margin-bottom: 20px;\r\n  background-color: #FF6B6B;\r\n  border: none;\r\n  color: white;\r\n  cursor: pointer;\r\n  transition: background-color 0.3s ease;\r\n}\r\n\r\n.sign-in-button:disabled {\r\n  background-color: #CCCCCC;\r\n  cursor: not-allowed;\r\n}\r\n\r\n.history-section {\r\n  background: #FFFFFF;\r\n  border-radius: 8px;\r\n  padding: 16px;\r\n  box-shadow: 0 2px 8px rgba(0,0,0,0.05);\r\n}\r\n\r\n.history-title {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  margin-bottom: 16px;\r\n}\r\n\r\n.history-item {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #EEEEEE;\r\n}\r\n\r\n.history-item:last-child {\r\n  border-bottom: none;\r\n}\r\n\r\n.history-time {\r\n  color: #666666;\r\n}\r\n\r\n.pull-right .el-button--text {\r\n  padding: 0;\r\n  color: #409EFF;\r\n}\r\n\r\n.qr-code-container {\r\n  padding: 20px;\r\n  text-align: center;\r\n  min-height: 600px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.qr-code {\r\n  img {\r\n    width: 1600px;\r\n    height: 600px;\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.qr-tip {\r\n  color: #666;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n}\r\n\r\n.loading-tip {\r\n  color: #909399;\r\n  font-size: 14px;\r\n}\r\n\r\n.ai-status-card {\r\n  .card-title {\r\n    display: flex;\r\n    align-items: center;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    color: #303133;\r\n\r\n    .title-icon {\r\n      margin-right: 8px;\r\n      font-size: 18px;\r\n      color: #409EFF;\r\n    }\r\n  }\r\n\r\n  .el-button--text {\r\n    color: #409EFF;\r\n    font-size: 14px;\r\n\r\n    &:hover {\r\n      color: #66b1ff;\r\n    }\r\n\r\n    i {\r\n      margin-right: 4px;\r\n    }\r\n  }\r\n\r\n  .ai-status-list {\r\n    .ai-status-item {\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      padding: 12px 0;\r\n      border-bottom: 1px solid #EBEEF5;\r\n\r\n      &:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .ai-platform {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .platform-icon {\r\n          width: 32px;\r\n          height: 32px;\r\n          border-radius: 50%;\r\n          background: #F5F7FA;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          margin-right: 12px;\r\n          overflow: hidden;\r\n\r\n          img {\r\n            width: 100%;\r\n            height: 100%;\r\n            object-fit: cover;\r\n          }\r\n        }\r\n\r\n        .platform-name {\r\n          font-size: 14px;\r\n          color: #606266;\r\n          font-weight: 500;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          .loading-icon {\r\n            margin-left: 8px;\r\n            color: #409EFF;\r\n            font-size: 16px;\r\n            animation: rotating 2s linear infinite;\r\n          }\r\n        }\r\n      }\r\n\r\n      .status-action {\r\n        .status-tag {\r\n          padding: 0px 12px;\r\n          border-radius: 16px;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n\r\n        .login-btn {\r\n          padding: 6px 12px;\r\n          border-radius: 16px;\r\n\r\n          i {\r\n            margin-right: 4px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.qr-code-container {\r\n  padding: 20px;\r\n  text-align: center;\r\n  min-height: 550px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background: #F5F7FA;\r\n  border-radius: 8px;\r\n}\r\n\r\n.qr-code {\r\n  background: #FFFFFF;\r\n  padding: 20px;\r\n  border-radius: 8px;\r\n  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);\r\n\r\n  img {\r\n    width: 1000px;\r\n    height: 550px;\r\n    margin-bottom: 15px;\r\n  }\r\n}\r\n\r\n.qr-tip {\r\n  color: #606266;\r\n  font-size: 14px;\r\n  margin-top: 10px;\r\n  font-weight: 500;\r\n}\r\n\r\n.loading-tip {\r\n  color: #909399;\r\n  font-size: 14px;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  &::before {\r\n    content: '';\r\n    display: inline-block;\r\n    width: 16px;\r\n    height: 16px;\r\n    margin-right: 8px;\r\n    border: 2px solid #DCDFE6;\r\n    border-top-color: #409EFF;\r\n    border-radius: 50%;\r\n    animation: loading 1s linear infinite;\r\n  }\r\n}\r\n\r\n@keyframes loading {\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n\r\n.message-card {\r\n  margin-top: 20px;\r\n\r\n  .message-list {\r\n    height: 300px;\r\n    overflow-y: auto;\r\n    padding: 10px;\r\n    background: #f5f7fa;\r\n    border-radius: 4px;\r\n\r\n    .message-item {\r\n      margin-bottom: 10px;\r\n\r\n      .message-content {\r\n        max-width: 80%;\r\n\r\n        .message-time {\r\n          font-size: 12px;\r\n          color: #909399;\r\n          margin-bottom: 4px;\r\n        }\r\n\r\n        .message-text {\r\n          padding: 8px 12px;\r\n          border-radius: 4px;\r\n          word-break: break-all;\r\n        }\r\n      }\r\n    }\r\n\r\n    .message-send {\r\n      display: flex;\r\n      justify-content: flex-end;\r\n\r\n      .message-content {\r\n        .message-text {\r\n          background: #409EFF;\r\n          color: white;\r\n        }\r\n      }\r\n    }\r\n\r\n    .message-receive {\r\n      display: flex;\r\n      justify-content: flex-start;\r\n\r\n      .message-content {\r\n        .message-text {\r\n          background: white;\r\n          color: #303133;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .message-input {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n\r\n@keyframes rotating {\r\n  from {\r\n    transform: rotate(0deg);\r\n  }\r\n  to {\r\n    transform: rotate(360deg);\r\n  }\r\n}\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;AAiNA,IAAAA,WAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,SAAA,GAAAJ,sBAAA,CAAAC,OAAA;AACA,IAAAI,SAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,WAAA,GAAAN,sBAAA,CAAAC,OAAA;AACA,IAAAM,SAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,SAAA,GAAAR,sBAAA,CAAAC,OAAA;AACA,IAAAQ,KAAA,GAAAR,OAAA;AACA,IAAAS,QAAA,GAAAT,OAAA;AACA,IAAAU,UAAA,GAAAX,sBAAA,CAAAC,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAW,aAAA;EACAC,UAAA;IACAC,YAAA;IACAC,UAAA;EACA;EACAC,QAAA;IACAF,YAAA;IACAC,UAAA;EACA;EACAE,SAAA;IACAH,YAAA;IACAC,UAAA;EACA;EACAG,SAAA;IACAJ,YAAA;IACAC,UAAA;EACA;AACA;AAAA,IAAAI,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEA;EACAC,IAAA;EACAC,UAAA;IACAC,UAAA,EAAAA,mBAAA;IACAC,SAAA,EAAAA,kBAAA;IACAC,WAAA,EAAAA,oBAAA;IACAC,QAAA,EAAAA,iBAAA;IACAC,QAAA,EAAAA,iBAAA;IAAAC,UAAA,EAAAA,mBAAA;IAAAC,QAAA,EAAAA,iBAAA;IAAAC,QAAA,EAAAA;EACA;EACAC,IAAA,WAAAA,KAAA;IACA;MACApB,aAAA,EAAAA,aAAA,CAAAC,UAAA;MACAoB,IAAA;MACAC,SAAA;MACAC,SAAA;MACAC,SAAA;MACA;MACAC,iBAAA;MAAA;MACAC,sBAAA;MAAA;MACAC,sBAAA;MAAA;MACAC,IAAA;QACAC,KAAA;QAAA;QACAC,SAAA;QAAA;QACAC,iBAAA;QAAA;QACAC,MAAA;MACA;MACAC,SAAA;QACAC,OAAA;QAAA;QACAC,UAAA;MACA;MACAC,SAAA;QACAC,OAAA;QAAA;QACAC,SAAA;MACA;MACAC,cAAA;MAAA;MACA;MACAC,KAAA;QACAX,KAAA,GACA;UAAAY,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAb,SAAA,GACA;UAAAW,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAZ,iBAAA,GACA;UAAAU,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAC,UAAA;QACAV,OAAA,GACA;UAAAO,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAR,UAAA,GACA;UAAAM,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MACAE,UAAA;QACAR,OAAA,GACA;UAAAI,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA,EACA;QACAL,SAAA,GACA;UAAAG,QAAA;UAAAC,OAAA;UAAAC,OAAA;QAAA;MAEA;MAEA;MACAG,OAAA;MAAA;MACAC,UAAA,GACA;QACAC,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,GACA;QACAD,KAAA;QACAC,KAAA;MACA,EACA;MAAA;MACAC,gBAAA;MAAA;MACAC,UAAA;MAAA;MACAC,cAAA;QACAC,KAAA;QACAC,IAAA;QACAC,IAAA;QACAC,MAAA;MACA;MAAA;MACAC,gBAAA;MAAA;;MAEA;MACAC,QAAA;MACAC,WAAA,MAAAC,IAAA,GAAAC,WAAA;MACAC,YAAA,MAAAF,IAAA,GAAAG,QAAA;MACAC,oBAAA;MACAC,iBAAA;MACAC,eAAA;MACAC,aAAA;MACAC,aAAA,GACA;QAAAC,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,IAAA;MAAA,GACA;QAAAD,IAAA;QAAAC,IAAA;MAAA,EACA;MACAC,WAAA,GACA,IAAAX,IAAA,cACA,IAAAA,IAAA,cACA,IAAAA,IAAA,cACA,IAAAA,IAAA,cACA,IAAAA,IAAA,aACA;MACAY,aAAA;QACAC,OAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;QACA;MACA;MACAC,QAAA;QACAJ,OAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;QACA;MACA;MACAE,OAAA;QACAL,OAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;QACA;MACA;MACAG,oBAAA;MACAC,aAAA;MACAC,SAAA;MACA;MACA7E,QAAA;MACA8E,YAAA;MACAC,SAAA;QACAV,OAAA;QACAC,MAAA;QACAE,MAAA;QACAD,KAAA;QACA;MACA;IACA;EACA;EACA;EACAS,QAAA;IACAC,aAAA,WAAAA,cAAA;MACA,IAAAC,KAAA;MACA,IAAAC,QAAA,OAAA3B,IAAA,MAAAD,WAAA,OAAAG,YAAA;MACA,IAAA0B,OAAA,OAAA5B,IAAA,MAAAD,WAAA,OAAAG,YAAA;;MAEA;MACA,SAAA2B,CAAA,MAAAA,CAAA,GAAAF,QAAA,CAAAG,MAAA,IAAAD,CAAA;QACAH,KAAA,CAAAK,IAAA;MACA;;MAEA;MACA,SAAAF,EAAA,MAAAA,EAAA,IAAAD,OAAA,CAAAI,OAAA,IAAAH,EAAA;QACAH,KAAA,CAAAK,IAAA,KAAA/B,IAAA,MAAAD,WAAA,OAAAG,YAAA,MAAA2B,EAAA;MACA;MAEA,OAAAH,KAAA;IACA;IACAO,eAAA,WAAAA,gBAAA;MACA,IAAAC,MAAA;QACArB,OAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;QACA;MACA;MACA,OAAAkB,MAAA,MAAAd,aAAA;IACA;EACA;EAEAe,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EAEA;EACAC,OAAA;IACAC,sBAAA,WAAAA,uBAAA3C,IAAA;MACA,KAAAvD,aAAA,GAAAA,aAAA,CAAAuD,IAAA;IACA;IACAyC,OAAA,WAAAA,QAAA;MAAA,IAAAG,KAAA;MACA,IAAAC,oBAAA,IAAAC,IAAA,WAAAC,QAAA;QACAH,KAAA,CAAA9E,IAAA,GAAAiF,QAAA,CAAAlF,IAAA;QACA+E,KAAA,CAAA7E,SAAA,GAAAgF,QAAA,CAAAhF,SAAA;QACA6E,KAAA,CAAA5E,SAAA,GAAA+E,QAAA,CAAA/E,SAAA;QACA4E,KAAA,CAAA3C,MAAA,GAAA8C,QAAA,CAAAlF,IAAA,CAAAoC,MAAA;QACA2C,KAAA,CAAAI,MAAA,GAAAD,QAAA,CAAAlF,IAAA,CAAAmF,MAAA;QAEAJ,KAAA,CAAAK,aAAA,CAAAL,KAAA,CAAA3C,MAAA;;QAEAiD,UAAA;UACA;UACAN,KAAA,CAAAO,WAAA;YACAnD,IAAA;YACAC,MAAA,EAAA2C,KAAA,CAAA3C,MAAA;YACA+C,MAAA,EAAAJ,KAAA,CAAAI;UACA;;UAEA;UACAJ,KAAA,CAAAO,WAAA;YACAnD,IAAA;YACAC,MAAA,EAAA2C,KAAA,CAAA3C,MAAA;YACA+C,MAAA,EAAAJ,KAAA,CAAAI;UACA;;UAEA;UACAJ,KAAA,CAAAO,WAAA;YACAnD,IAAA;YACAC,MAAA,EAAA2C,KAAA,CAAA3C,MAAA;YACA+C,MAAA,EAAAJ,KAAA,CAAAI;UACA;;UAEA;UACAJ,KAAA,CAAAO,WAAA;YACAnD,IAAA;YACAC,MAAA,EAAA2C,KAAA,CAAA3C,MAAA;YACA+C,MAAA,EAAAJ,KAAA,CAAAI;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAI,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,sBAAA,IAAAR,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAlF,IAAA;UACAwF,MAAA,CAAAhF,IAAA,CAAAC,KAAA,GAAAyE,QAAA,CAAAlF,IAAA,CAAAS,KAAA;UACA+E,MAAA,CAAAhF,IAAA,CAAAE,SAAA,GAAAwE,QAAA,CAAAlF,IAAA,CAAAU,SAAA;UACA8E,MAAA,CAAAhF,IAAA,CAAAG,iBAAA,GAAAuE,QAAA,CAAAlF,IAAA,CAAAW,iBAAA;UACA6E,MAAA,CAAAhF,IAAA,CAAAI,MAAA,GAAAsE,QAAA,CAAAlF,IAAA,CAAAY,MAAA;QACA;QACA4E,MAAA,CAAAnF,iBAAA;MACA;IACA;IACAqF,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,kBAAA,IAAAX,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAlF,IAAA;UACA2F,MAAA,CAAA9E,SAAA,CAAAC,OAAA,GAAAoE,QAAA,CAAAlF,IAAA,CAAA6F,QAAA;UACAF,MAAA,CAAA9E,SAAA,CAAAE,UAAA,GAAAmE,QAAA,CAAAlF,IAAA,CAAA8F,WAAA;QACA;QACAH,MAAA,CAAArF,sBAAA;MACA;IACA;IACAyF,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,0BAAA,IAAAhB,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAlF,IAAA;UACAgG,MAAA,CAAAhF,SAAA,CAAAC,OAAA,GAAAiE,QAAA,CAAAlF,IAAA,CAAAiB,OAAA;UACA+E,MAAA,CAAAhF,SAAA,CAAAE,SAAA,GAAAgE,QAAA,CAAAlF,IAAA,CAAAkB,SAAA;QACA;QACA8E,MAAA,CAAAzF,sBAAA;MACA;IACA;IACA;IACA2F,WAAA,WAAAA,YAAA;MAAA,IAAAC,MAAA;MACA,KAAAC,KAAA,CAAA5F,IAAA,CAAA6F,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAC,yBAAA,EAAAJ,MAAA,CAAA3F,IAAA,EAAAyE,IAAA,WAAAC,QAAA;YACAiB,MAAA,CAAAK,QAAA,CAAAC,OAAA,CAAAvB,QAAA,CAAAlF,IAAA;YACAmG,MAAA,CAAA9F,iBAAA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAqG,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAAP,KAAA,CAAAvF,SAAA,CAAAwF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAM,mBAAA,EAAAD,MAAA,CAAA9F,SAAA,EAAAoE,IAAA,WAAAC,QAAA;YACAyB,MAAA,CAAAH,QAAA,CAAAC,OAAA,CAAAvB,QAAA,CAAAlF,IAAA;YACA2G,MAAA,CAAArG,sBAAA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACAuG,gBAAA,WAAAA,iBAAA;MAAA,IAAAC,MAAA;MACA,KAAAV,KAAA,CAAApF,SAAA,CAAAqF,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAS,mBAAA,EAAAD,MAAA,CAAA9F,SAAA,EAAAiE,IAAA,WAAAC,QAAA;YACA4B,MAAA,CAAAN,QAAA,CAAAC,OAAA,CAAAvB,QAAA,CAAAlF,IAAA;YACA8G,MAAA,CAAAvG,sBAAA;UACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAyG,gBAAA,WAAAA,iBAAA;MACA,KAAAhF,cAAA,CAAAI,MAAA,QAAAnC,IAAA,CAAAmC,MAAA;MACA,KAAA6E,mBAAA;IACA;IACA;IACAA,mBAAA,WAAAA,oBAAA;MAAA,IAAAC,MAAA;MACA,IAAAD,4BAAA,OAAAjF,cAAA,EAAAiD,IAAA,WAAAC,QAAA;QACAgC,MAAA,CAAApF,gBAAA;QACAoF,MAAA,CAAA7E,gBAAA,GAAA6C,QAAA,CAAAlF,IAAA,CAAAmH,IAAA;QACAD,MAAA,CAAAnF,UAAA,GAAAmD,QAAA,CAAAlF,IAAA,CAAAoH,KAAA;QACAF,MAAA,CAAAxF,OAAA;MACA;IACA;IACA;IACA2F,YAAA,WAAAA,aAAApE,IAAA;MACA,KAAAA,IAAA;MACA,YAAAE,WAAA,CAAAmE,IAAA,WAAAC,UAAA;QAAA,OACAA,UAAA,CAAA/C,OAAA,OAAAvB,IAAA,CAAAuB,OAAA,MACA+C,UAAA,CAAA5E,QAAA,OAAAM,IAAA,CAAAN,QAAA,MACA4E,UAAA,CAAA9E,WAAA,OAAAQ,IAAA,CAAAR,WAAA;MAAA,CACA;IACA;IACA+E,OAAA,WAAAA,QAAAvE,IAAA;MACA,KAAAA,IAAA;MACA,IAAAwE,KAAA,OAAAjF,IAAA;MACA,OAAAS,IAAA,CAAAuB,OAAA,OAAAiD,KAAA,CAAAjD,OAAA,MACAvB,IAAA,CAAAN,QAAA,OAAA8E,KAAA,CAAA9E,QAAA,MACAM,IAAA,CAAAR,WAAA,OAAAgF,KAAA,CAAAhF,WAAA;IACA;IACAiF,YAAA,WAAAA,aAAAzE,IAAA;MACA,KAAAA,IAAA;MACA,IAAAwE,KAAA,OAAAjF,IAAA;MACAiF,KAAA,CAAAE,QAAA;MACA,OAAA1E,IAAA,GAAAwE,KAAA;IACA;IACAG,YAAA,WAAAA,aAAA;MACA,UAAA7E,aAAA;QACA,KAAAA,aAAA;QACA,KAAAI,WAAA,CAAAoB,IAAA,KAAA/B,IAAA;QACA,IAAAqF,GAAA,OAAArF,IAAA;QACA,KAAAQ,aAAA,CAAA8E,OAAA;UACA7E,IAAA,KAAA8E,MAAA,CAAAF,GAAA,CAAApF,WAAA,SAAAsF,MAAA,CAAAC,MAAA,CAAAH,GAAA,CAAAlF,QAAA,QAAAsF,QAAA,eAAAF,MAAA,CAAAC,MAAA,CAAAH,GAAA,CAAArD,OAAA,IAAAyD,QAAA;UACA/E,IAAA,KAAA6E,MAAA,CAAAC,MAAA,CAAAH,GAAA,CAAAK,QAAA,IAAAD,QAAA,eAAAF,MAAA,CAAAC,MAAA,CAAAH,GAAA,CAAAM,UAAA,IAAAF,QAAA,eAAAF,MAAA,CAAAC,MAAA,CAAAH,GAAA,CAAAO,UAAA,IAAAH,QAAA;QACA;QACA,KAAApF,iBAAA;QACA,KAAAC,eAAA;MACA;IACA;IACAuF,aAAA,WAAAA,cAAAlG,IAAA;MACA,KAAAyB,aAAA,GAAAzB,IAAA;MACA,KAAAwB,oBAAA;MACA,KAAAI,SAAA,CAAA5B,IAAA;MACA,KAAAmG,SAAA,CAAAnG,IAAA;IACA;IACAmG,SAAA,WAAAA,UAAAnG,IAAA;MACA,KAAA0B,SAAA;MACA,IAAA1B,IAAA;QACA,KAAAmD,WAAA;UACAnD,IAAA;UACAC,MAAA,OAAAA,MAAA;UACA+C,MAAA,OAAAA;QACA;MACA;MACA,IAAAhD,IAAA;QACA,KAAAmD,WAAA;UACAnD,IAAA;UACAC,MAAA,OAAAA,MAAA;UACA+C,MAAA,OAAAA;QACA;MACA;MACA,IAAAhD,IAAA;QACA,KAAAmD,WAAA;UACAnD,IAAA;UACAC,MAAA,OAAAA,MAAA;UACA+C,MAAA,OAAAA;QACA;MACA;MACA,IAAAhD,IAAA;QACA,KAAAmD,WAAA;UACAnD,IAAA;UACAC,MAAA,OAAAA,MAAA;UACA+C,MAAA,OAAAA;QACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA,KAAAqB,QAAA;QACAlF,OAAA;QACAa,IAAA;MACA;IACA;IACAoG,eAAA,WAAAA,gBAAApG,IAAA;MACA,IAAAqG,KAAA;QACAnF,OAAA,EAAApF,OAAA;QACAqF,MAAA,EAAArF,OAAA;QACAsF,KAAA,EAAAtF,OAAA;QACAuF,MAAA,EAAAvF,OAAA;QACAwK,EAAA,EAAAxK,OAAA;MACA;MACA,OAAAuK,KAAA,CAAArG,IAAA;IACA;IACAuG,eAAA,WAAAA,gBAAAvG,IAAA;MACA,IAAAwG,KAAA;QACAtF,OAAA;QACAC,MAAA;QACAC,KAAA;QACAC,MAAA;QACA;MACA;MACA,OAAAmF,KAAA,CAAAxG,IAAA;IACA;IACA;IACAiD,aAAA,WAAAA,cAAAwD,EAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,KAAA,GAAAC,OAAA,CAAAC,GAAA,CAAAC,cAAA,WAAAlB,MAAA,CAAAa,EAAA;MACAM,OAAA,CAAAC,GAAA,mBAAAJ,OAAA,CAAAC,GAAA,CAAAC,cAAA;MACAG,kBAAA,CAAAC,OAAA,CAAAP,KAAA,YAAAQ,KAAA;QACA,QAAAA,KAAA,CAAAnH,IAAA;UACA;YACA0G,MAAA,CAAArC,QAAA,CAAAC,OAAA;YACA;UACA;YACAoC,MAAA,CAAAU,sBAAA,CAAAD,KAAA,CAAAtJ,IAAA;YACA;UACA;YACA6I,MAAA,CAAArC,QAAA,CAAAgD,OAAA;YACA;UACA;YACAX,MAAA,CAAArC,QAAA,CAAAiD,KAAA;YACA;UACA;YACAZ,MAAA,CAAArC,QAAA,CAAAiD,KAAA;YACA;QACA;MACA;IACA;IAEAF,sBAAA,WAAAA,uBAAAvJ,IAAA;MACAkJ,OAAA,CAAAC,GAAA,UAAAnJ,IAAA;MACA,IAAA0J,OAAA,GAAA1J,IAAA;MACA,IAAA2J,OAAA,GAAAC,IAAA,CAAAC,KAAA,CAAAH,OAAA;MAEA,IAAAA,OAAA,CAAAI,QAAA,wBAAAH,OAAA,CAAAI,MAAA;QACA,KAAAL,OAAA,CAAAI,QAAA;UACA,KAAAnG,oBAAA;UACA,KAAAP,aAAA,CAAAC,OAAA;UACA,KAAAI,QAAA,CAAAJ,OAAA,GAAAsG,OAAA,CAAAI,MAAA;UACA,KAAAhG,SAAA,CAAAV,OAAA;QACA;UACA,KAAAK,OAAA,CAAAL,OAAA;UACA,KAAAU,SAAA,CAAAV,OAAA;QACA;MACA,WAAAqG,OAAA,CAAAI,QAAA,2BAAAH,OAAA,CAAAI,MAAA;QACA,KAAAL,OAAA,CAAAI,QAAA;UACA,KAAAnG,oBAAA;UACA,KAAAP,aAAA,CAAAG,KAAA;UACA,KAAAE,QAAA,CAAAF,KAAA,GAAAoG,OAAA,CAAAI,MAAA;UACA,KAAAhG,SAAA,CAAAR,KAAA;QACA;UACA,KAAAG,OAAA,CAAAH,KAAA;UACA,KAAAQ,SAAA,CAAAR,KAAA;QACA;MACA,WAAAmG,OAAA,CAAAI,QAAA,0BAAAJ,OAAA,CAAAI,QAAA,0BAAAJ,OAAA,CAAAI,QAAA,6BAAAJ,OAAA,CAAAI,QAAA;QACA,KAAAjG,SAAA,GAAA8F,OAAA,CAAAK,GAAA;MACA,WAAAN,OAAA,CAAAI,QAAA;QACA;QACA,KAAAjG,SAAA;QACA,KAAA2C,QAAA;UACAlF,OAAA;UACAa,IAAA;UACA8H,QAAA;QACA;MACA,WAAAP,OAAA,CAAAI,QAAA,wBAAAH,OAAA,CAAAI,MAAA;QACA,KAAAL,OAAA,CAAAI,QAAA;UACA,KAAAnG,oBAAA;UACA,KAAAP,aAAA,CAAAE,MAAA;UACA,KAAAG,QAAA,CAAAH,MAAA,GAAAqG,OAAA,CAAAI,MAAA;UACA,KAAAhG,SAAA,CAAAT,MAAA;QACA;UACA,KAAAI,OAAA,CAAAJ,MAAA;UACA,KAAAS,SAAA,CAAAT,MAAA;QACA;MACA,WAAAoG,OAAA,CAAAI,QAAA,wBAAAH,OAAA,CAAAI,MAAA;QACA,KAAAL,OAAA,CAAAI,QAAA;UACA,KAAAnG,oBAAA;UACA,KAAAP,aAAA,CAAAI,MAAA;UACA,KAAAC,QAAA,CAAAD,MAAA,GAAAmG,OAAA,CAAAI,MAAA;UACA,KAAAhG,SAAA,CAAAP,MAAA;QACA;UACA,KAAAE,OAAA,CAAAF,MAAA;UACA,KAAAO,SAAA,CAAAP,MAAA;QACA;MACA,WAAAkG,OAAA,CAAAI,QAAA,wBAAAH,OAAA,CAAAI,MAAA;QACA,KAAAL,OAAA,CAAAI,QAAA;UACA,KAAAnG,oBAAA;UACA,KAAAP,aAAA,CAAAqF,EAAA;UACA,KAAAhF,QAAA,CAAAgF,EAAA,GAAAkB,OAAA,CAAAI,MAAA;UACA,KAAAhG,SAAA,CAAA0E,EAAA;QACA;UACA,KAAA/E,OAAA,CAAA+E,EAAA;UACA,KAAA1E,SAAA,CAAA0E,EAAA;QACA;MACA;IACA;IAEAyB,cAAA,WAAAA,eAAA;MACAd,kBAAA,CAAAe,KAAA;IACA;IAEA7E,WAAA,WAAAA,YAAAtF,IAAA;MAAA,IAAAoK,MAAA;MACA,IAAAhB,kBAAA,CAAAiB,IAAA,CAAArK,IAAA;QACA;QACA,KAAAsK,SAAA;UACAF,MAAA,CAAAG,cAAA;QACA;MACA;QACA,KAAA/D,QAAA,CAAAiD,KAAA;MACA;IACA;IACA;IACAe,UAAA,WAAAA,WAAAvH,IAAA;MACA,IAAAwH,KAAA,GAAAzC,MAAA,CAAA/E,IAAA,CAAAiF,QAAA,IAAAD,QAAA;MACA,IAAAyC,OAAA,GAAA1C,MAAA,CAAA/E,IAAA,CAAAkF,UAAA,IAAAF,QAAA;MACA,IAAA0C,OAAA,GAAA3C,MAAA,CAAA/E,IAAA,CAAAmF,UAAA,IAAAH,QAAA;MACA,UAAAF,MAAA,CAAA0C,KAAA,OAAA1C,MAAA,CAAA2C,OAAA,OAAA3C,MAAA,CAAA4C,OAAA;IACA;IAEA;IACAJ,cAAA,WAAAA,eAAA;MACA,IAAAK,WAAA,QAAAxE,KAAA,CAAAwE,WAAA;MACA,IAAAA,WAAA;QACAA,WAAA,CAAAC,SAAA,GAAAD,WAAA,CAAAE,YAAA;MACA;IACA;IACAC,aAAA,WAAAA,cAAA;MACAC,MAAA,CAAAC,QAAA,CAAAC,MAAA;IACA;EACA;EACAC,aAAA,WAAAA,cAAA;IACA,KAAAjB,cAAA;EACA;AACA", "ignoreList": []}]}