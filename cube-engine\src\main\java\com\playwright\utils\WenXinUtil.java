package com.playwright.utils;

import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.options.WaitForSelectorState;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 百度AI工具类
 * 处理百度AI页面的自动化操作
 * <AUTHOR>
 * @version JDK 17
 * @date 2025年01月21日 15:30
 */
@Component
public class WenXinUtil {

    @Autowired
    private LogMsgUtil logInfo;

    /**
     * 等待百度AI回答完成并获取内容
     * @param page Playwright页面实例
     * @param userId 用户ID
     * @param aiName AI名称
     * @return 回答内容
     */
    public String waitWXResponse(Page page, String userId, String aiName) {
        try {
            // 等待聊天框的内容稳定
            String currentContent = "";
            String lastContent = "";
            
            // 设置最大等待时间（单位：毫秒），比如 10 分钟
            long timeout = 600000; // 10 分钟
            long startTime = System.currentTimeMillis();  // 获取当前时间戳

            // 进入循环，直到内容不再变化或者超时
            while (true) {
                // 获取当前时间戳
                long elapsedTime = System.currentTimeMillis() - startTime;

                // 如果超时，退出循环
                if (elapsedTime > timeout) {
                    System.out.println("超时，AI未完成回答！");
                    logInfo.sendTaskLog("等待超时，AI可能未完成回答", userId, aiName);
                    break;
                }

                try {
                    // 检查是否出现回答完成的提示（支持R1和V3模型）
                    boolean isCompleted = page.locator(".title_g38hk_7").count() > 0;

                    // 额外检查完成状态的文本内容，确保是真正的完成
                    if (isCompleted) {
                        Locator completionTitle = page.locator(".title_g38hk_7");
                        String completionText = completionTitle.textContent();
                        logInfo.sendTaskLog("检测到完成状态: " + completionText, userId, aiName);

                        // 验证是否为有效的完成状态
                        if (completionText != null &&
                            (completionText.contains("回答完成") ||
                             completionText.contains("DeepSeek-R1") ||
                             completionText.contains("DeepSeek-V3"))) {
                            isCompleted = true;
                        } else {
                            isCompleted = false;
                        }
                    }

                    if (isCompleted) {
                        // 回答已完成，等待一下确保内容完全加载
                        Thread.sleep(2000);

                        // 获取原文内容
                        Locator contentLocator = page.locator(".marklang").last();
                        if (contentLocator.count() > 0) {
                            currentContent = contentLocator.innerHTML();

                            // 检查内容中是否还有加载中的标识
                            if (!currentContent.contains("cosd-markdown-loading")) {
                                logInfo.sendTaskLog(aiName + "回答完成，正在自动提取内容", userId, aiName);
                                break;
                            } else {
                                logInfo.sendTaskLog("检测到内容仍在加载中，继续等待...", userId, aiName);
                            }
                        }
                    }

                    // 如果还没有完成标志，尝试获取当前内容
                    Locator responseLocator = page.locator(".marklang").last();
                    if (responseLocator.count() > 0) {
                        currentContent = responseLocator.innerHTML();
                    }

                    System.out.println("当前内容: " + currentContent);

                    // 检查是否有加载指示器或者"正在思考"的提示
                    boolean isLoading = page.locator("text=正在思考").count() > 0 ||
                                       page.locator("text=生成中").count() > 0 ||
                                       page.locator("[class*='loading']").count() > 0 ||
                                       page.locator("[class*='thinking']").count() > 0 ||
                                       (currentContent != null && currentContent.contains("cosd-markdown-loading"));

                    // 如果当前内容和上次内容相同，且没有加载指示器，认为 AI 已经完成回答
                    if (currentContent.equals(lastContent) && !isLoading && !currentContent.isEmpty()) {
                        logInfo.sendTaskLog(aiName + "回答完成，正在自动提取内容", userId, aiName);
                        break;
                    }

                    // 更新上次内容为当前内容
                    lastContent = currentContent;

                } catch (Exception e) {
                    System.out.println("获取内容时出错: " + e.getMessage());
                }

                // 如果已经检测到完成，不需要再等待8秒
                if (page.locator(".title_g38hk_7").count() > 0 &&
                    currentContent != null && !currentContent.contains("cosd-markdown-loading")) {
                    logInfo.sendTaskLog("检测到回答已完成，跳过等待", userId, aiName);
                    break;
                }

                page.waitForTimeout(3000);  // 减少等待时间到3秒
            }
            
            logInfo.sendTaskLog(aiName + "内容已自动提取完成", userId, aiName);

            // 清理内容，移除不必要的HTML标签和内容
            if (currentContent != null && !currentContent.isEmpty()) {
                // 移除一些常见的无用内容
                currentContent = currentContent.replaceAll("<span>\\s*<span[^>]*?>\\d+</span>\\s*</span>", "");
                currentContent = currentContent.replaceAll("正在思考...", "");
                currentContent = currentContent.replaceAll("生成中...", "");

                // 提取纯文本内容，移除所有HTML标签
                try {
                    Document doc = Jsoup.parse(currentContent);
                    currentContent = doc.text();  // 提取纯文本内容
                } catch (Exception e) {
                    // 如果Jsoup解析失败，使用简单的正则表达式移除HTML标签
                    currentContent = currentContent.replaceAll("<[^>]+>", "");
                }
            }
            
            return currentContent;

        } catch (Exception e) {
            e.printStackTrace();
            logInfo.sendTaskLog("获取" + aiName + "内容时发生错误: " + e.getMessage(), userId, aiName);
        }
        return "获取内容失败";
    }

    /**
     * 等待并点击百度AI的复制按钮
     * @param page Playwright页面实例
     * @param userId 用户ID
     * @param aiName AI名称
     * @return 复制的内容
     */
    public String waitAndClickWXCopyButton(Page page, String userId, String aiName) {
        try {
            // 等待复制按钮出现
            page.waitForSelector("[data-testid='copy-button'], button[title*='复制'], button:has-text('复制')",
                    new Page.WaitForSelectorOptions()
                            .setState(WaitForSelectorState.VISIBLE)
                            .setTimeout(60000));  // 60秒超时
            
            logInfo.sendTaskLog("回答完成，正在自动获取内容", userId, aiName);
            Thread.sleep(2000);  // 额外等待确保按钮可点击

            // 点击复制按钮
            Locator copyButton = page.locator("[data-testid='copy-button']");
            if (copyButton.count() == 0) {
                copyButton = page.locator("button[title*='复制']");
            }
            if (copyButton.count() == 0) {
                copyButton = page.locator("button:has-text('复制')");
            }
            
            if (copyButton.count() > 0) {
                copyButton.last().click();  // 获取最后一个复制按钮
                logInfo.sendTaskLog("内容已自动复制完成", userId, aiName);
                System.out.println("复制成功");
                
                // 确保点击操作完成
                Thread.sleep(1000);
                
                // 从剪贴板读取内容
                String clipboardContent = (String) page.evaluate("navigator.clipboard.readText()");
                return clipboardContent;
            } else {
                logInfo.sendTaskLog("未找到复制按钮", userId, aiName);
                return "未找到复制按钮";
            }

        } catch (Exception e) {
            e.printStackTrace();
            logInfo.sendTaskLog("复制操作失败: " + e.getMessage(), userId, aiName);
        }
        return "复制失败";
    }

    /**
     * 检查百度AI是否需要登录
     * @param page Playwright页面实例
     * @return 是否需要登录
     */
    public boolean needsLogin(Page page) {
        try {
            // 检查是否存在登录按钮或登录相关元素
            return page.locator(".VGndm4Ya.VngKLt9L").count() > 0 ||
                   page.locator("text=立即登录").count() > 0 ||
                   page.locator("text=登录").count() > 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 等待用户登录完成
     * @param page Playwright页面实例
     * @param userId 用户ID
     * @param maxWaitTime 最大等待时间（毫秒）
     * @return 是否登录成功
     */
    public boolean waitForLogin(Page page, String userId, long maxWaitTime) {
        try {
            logInfo.sendTaskLog("检测到需要登录，请在浏览器中完成登录", userId, "百度AI");

            long startTime = System.currentTimeMillis();
            while (System.currentTimeMillis() - startTime < maxWaitTime) {
                if (!needsLogin(page)) {
                    logInfo.sendTaskLog("登录成功", userId, "百度AI");
                    return true;
                }
                Thread.sleep(2000); // 每2秒检查一次
            }

            logInfo.sendTaskLog("登录等待超时", userId, "百度AI");
            return false;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }
}
